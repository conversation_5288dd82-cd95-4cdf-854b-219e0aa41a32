/**
 * Enhanced Location Service
 * Robust location service with multiple fallbacks and better error handling
 */

export interface LocationData {
  address: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  coordinates: {
    lat: number;
    lng: number;
  };
}

export interface DeviceInfo {
  userAgent: string;
  platform: string;
  language: string;
  timezone: string;
  screenResolution: string;
  connectionType?: string;
  batteryLevel?: number;
  isOnline: boolean;
}

export class EnhancedLocationService {
  private static instance: EnhancedLocationService;
  private locationCache = new Map<string, { data: LocationData; timestamp: number }>();
  private readonly CACHE_DURATION = 30 * 60 * 1000; // 30 minutes

  static getInstance(): EnhancedLocationService {
    if (!EnhancedLocationService.instance) {
      EnhancedLocationService.instance = new EnhancedLocationService();
    }
    return EnhancedLocationService.instance;
  }

  /**
   * Get current location with fallbacks including Google Maps
   */
  async getCurrentLocation(): Promise<LocationData | null> {
    try {
      console.log('📍 Getting current location...');

      // Try to get GPS coordinates
      const position = await this.getGPSPosition();
      if (position) {
        // Try Google Maps first if available
        const googleLocation = await this.tryGoogleMapsGeocode(position.lat, position.lng);
        if (googleLocation) {
          return googleLocation;
        }

        // Fallback to other geocoding services
        const locationData = await this.reverseGeocode(position.lat, position.lng);
        if (locationData) {
          return locationData;
        }
      }

      // Fallback to IP-based location
      console.log('📍 GPS failed, trying IP-based location...');
      return await this.getLocationFromIP();
    } catch (error) {
      console.error('❌ Location service failed:', error);
      return this.getDefaultLocation();
    }
  }

  /**
   * Try Google Maps geocoding if available
   */
  private async tryGoogleMapsGeocode(lat: number, lng: number): Promise<LocationData | null> {
    try {
      // Check if Google Maps is available
      if (typeof (window as any).googleMapsLocationService !== 'undefined') {
        const googleService = (window as any).googleMapsLocationService;
        if (googleService.isGoogleMapsAvailable()) {
          console.log('🗺️ Trying Google Maps geocoding...');
          const result = await googleService.reverseGeocodeWithGoogle(lat, lng);
          if (result) {
            console.log('✅ Location from Google Maps:', result.city);
            return result;
          }
        }
      }
      return null;
    } catch (error) {
      console.warn('⚠️ Google Maps geocoding failed:', error);
      return null;
    }
  }

  /**
   * Get GPS position with timeout
   */
  private async getGPSPosition(): Promise<{ lat: number; lng: number } | null> {
    return new Promise((resolve) => {
      if (!navigator.geolocation) {
        console.warn('⚠️ Geolocation not supported');
        resolve(null);
        return;
      }

      const timeoutId = setTimeout(() => {
        console.warn('⚠️ GPS timeout');
        resolve(null);
      }, 10000); // 10 second timeout

      navigator.geolocation.getCurrentPosition(
        (position) => {
          clearTimeout(timeoutId);
          console.log('✅ GPS position obtained');
          resolve({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          });
        },
        (error) => {
          clearTimeout(timeoutId);
          console.warn('⚠️ GPS error:', error.message);
          resolve(null);
        },
        {
          enableHighAccuracy: false,
          timeout: 8000,
          maximumAge: 5 * 60 * 1000 // 5 minutes
        }
      );
    });
  }

  /**
   * Reverse geocode with multiple service fallbacks
   */
  private async reverseGeocode(lat: number, lng: number): Promise<LocationData | null> {
    const cacheKey = `${lat.toFixed(4)},${lng.toFixed(4)}`;
    
    // Check cache first
    const cached = this.locationCache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp) < this.CACHE_DURATION) {
      console.log('✅ Using cached location data');
      return cached.data;
    }

    const services = [
      {
        name: 'nominatim',
        url: `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`,
        headers: {
          'User-Agent': 'CTNL-AI-Workboard/1.0'
        },
        parser: (data: any): LocationData => ({
          address: data.display_name || 'Unknown Location',
          city: data.address?.city || data.address?.town || data.address?.village || 'Unknown City',
          state: data.address?.state || 'Unknown State',
          country: data.address?.country || 'Unknown Country',
          postalCode: data.address?.postcode || '',
          coordinates: { lat, lng }
        })
      },
      {
        name: 'bigdatacloud',
        url: `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lng}&localityLanguage=en`,
        headers: {},
        parser: (data: any): LocationData => ({
          address: data.locality || 'Unknown Location',
          city: data.city || 'Unknown City',
          state: data.principalSubdivision || 'Unknown State',
          country: data.countryName || 'Unknown Country',
          postalCode: data.postcode || '',
          coordinates: { lat, lng }
        })
      }
    ];

    for (const service of services) {
      try {
        console.log(`🌍 Trying ${service.name} geocoding service...`);
        
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 8000);

        const response = await fetch(service.url, {
          headers: service.headers,
          signal: controller.signal,
          mode: 'cors'
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          console.warn(`${service.name} returned ${response.status}`);
          continue;
        }

        const data = await response.json();
        const locationData = service.parser(data);
        
        // Cache the result
        this.locationCache.set(cacheKey, {
          data: locationData,
          timestamp: Date.now()
        });

        console.log(`✅ Location from ${service.name}:`, locationData.city);
        return locationData;
      } catch (error: any) {
        console.warn(`❌ ${service.name} failed:`, error.message);
        continue;
      }
    }

    console.warn('⚠️ All geocoding services failed');
    return null;
  }

  /**
   * Get location from IP address
   */
  private async getLocationFromIP(): Promise<LocationData | null> {
    const services = [
      {
        name: 'ipapi',
        url: 'https://ipapi.co/json/',
        parser: (data: any): LocationData => ({
          address: `${data.city}, ${data.region}, ${data.country_name}`,
          city: data.city || 'Unknown City',
          state: data.region || 'Unknown State',
          country: data.country_name || 'Unknown Country',
          postalCode: data.postal || '',
          coordinates: { lat: data.latitude || 0, lng: data.longitude || 0 }
        })
      },
      {
        name: 'ipgeolocation',
        url: 'https://api.ipgeolocation.io/ipgeo?apiKey=free',
        parser: (data: any): LocationData => ({
          address: `${data.city}, ${data.state_prov}, ${data.country_name}`,
          city: data.city || 'Unknown City',
          state: data.state_prov || 'Unknown State',
          country: data.country_name || 'Unknown Country',
          postalCode: data.zipcode || '',
          coordinates: { lat: parseFloat(data.latitude) || 0, lng: parseFloat(data.longitude) || 0 }
        })
      }
    ];

    for (const service of services) {
      try {
        console.log(`🌐 Trying ${service.name} IP location service...`);
        
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const response = await fetch(service.url, {
          signal: controller.signal,
          mode: 'cors'
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          console.warn(`${service.name} returned ${response.status}`);
          continue;
        }

        const data = await response.json();
        const locationData = service.parser(data);
        
        console.log(`✅ IP location from ${service.name}:`, locationData.city);
        return locationData;
      } catch (error: any) {
        console.warn(`❌ ${service.name} failed:`, error.message);
        continue;
      }
    }

    console.warn('⚠️ All IP location services failed');
    return null;
  }

  /**
   * Get default location when all services fail
   */
  private getDefaultLocation(): LocationData {
    console.log('📍 Using default location (Lagos, Nigeria)');
    return {
      address: 'Lagos, Nigeria',
      city: 'Lagos',
      state: 'Lagos State',
      country: 'Nigeria',
      postalCode: '100001',
      coordinates: { lat: 6.5244, lng: 3.3792 }
    };
  }

  /**
   * Get device information
   */
  getDeviceInfo(): DeviceInfo {
    const nav = navigator as any;
    
    return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      screenResolution: `${screen.width}x${screen.height}`,
      connectionType: nav.connection?.effectiveType || 'unknown',
      batteryLevel: nav.getBattery ? undefined : undefined, // Will be set async if available
      isOnline: navigator.onLine
    };
  }

  /**
   * Get IP address with fallbacks
   */
  async getIPAddress(): Promise<string> {
    const services = [
      'https://api.ipify.org?format=json',
      'https://ipapi.co/ip/',
      'https://api.my-ip.io/ip'
    ];

    for (const service of services) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 3000);

        const response = await fetch(service, {
          signal: controller.signal,
          mode: 'cors'
        });

        clearTimeout(timeoutId);

        if (!response.ok) continue;

        const data = await response.text();
        const ip = service.includes('json') ? JSON.parse(data).ip : data.trim();
        
        if (ip && ip.match(/^\d+\.\d+\.\d+\.\d+$/)) {
          console.log('✅ IP address obtained:', ip);
          return ip;
        }
      } catch (error) {
        console.warn(`IP service failed:`, error);
        continue;
      }
    }

    console.warn('⚠️ All IP services failed, using fallback');
    return '127.0.0.1';
  }

  /**
   * Clear location cache
   */
  clearCache(): void {
    this.locationCache.clear();
    console.log('🧹 Location cache cleared');
  }
}

// Global instance
export const enhancedLocationService = EnhancedLocationService.getInstance();

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).enhancedLocationService = enhancedLocationService;
  (window as any).getCurrentLocation = () => enhancedLocationService.getCurrentLocation();
  (window as any).getDeviceInfo = () => enhancedLocationService.getDeviceInfo();
  
  console.log('📍 Enhanced Location Service loaded. Available commands:');
  console.log('  - getCurrentLocation()');
  console.log('  - getDeviceInfo()');
}
