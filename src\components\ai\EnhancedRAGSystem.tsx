
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Progress } from "@/components/ui/progress";
import { 
  Brain, 
  Database, 
  FileText, 
  Search, 
  Zap, 
  Network,
  Cpu,
  Activity
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";

interface RAGResult {
  id: string;
  content: string;
  source: string;
  relevance: number;
  embedding?: number[];
  metadata?: any;
}

interface KnowledgeBase {
  id: string;
  name: string;
  documents: number;
  lastUpdated: Date;
  status: 'active' | 'indexing' | 'inactive';
}

export const EnhancedRAGSystem = () => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<RAGResult[]>([]);
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isIndexing, setIsIndexing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [selectedKB, setSelectedKB] = useState<string>('all');
  const { userProfile } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    loadKnowledgeBases();
  }, []);

  const loadKnowledgeBases = async () => {
    try {
      // Load real knowledge bases from ai_documents table
      const { data: documents, error } = await supabase
        .from('ai_documents')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Group documents by category to create knowledge bases
      const categoryGroups = documents?.reduce((acc, doc) => {
        const category = doc.category || 'uncategorized';
        if (!acc[category]) {
          acc[category] = [];
        }
        acc[category].push(doc);
        return acc;
      }, {} as Record<string, any[]>) || {};

      // Convert to KnowledgeBase format
      const kbs: KnowledgeBase[] = Object.entries(categoryGroups).map(([category, docs]) => {
        const latestDoc = docs.sort((a, b) =>
          new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
        )[0];

        return {
          id: category,
          name: category.charAt(0).toUpperCase() + category.slice(1).replace('_', ' '),
          documents: docs.length,
          lastUpdated: new Date(latestDoc.updated_at),
          status: docs.some(doc => doc.indexed_at) ? 'active' : 'indexing'
        };
      });

      // Add default knowledge bases if no documents exist
      if (kbs.length === 0) {
        const defaultKBs: KnowledgeBase[] = [
          {
            id: 'getting-started',
            name: 'Getting Started',
            documents: 0,
            lastUpdated: new Date(),
            status: 'inactive'
          }
        ];
        setKnowledgeBases(defaultKBs);
      } else {
        setKnowledgeBases(kbs);
      }
    } catch (error) {
      console.error('Error loading knowledge bases:', error);
      toast({
        title: "⚠️ Knowledge Base Error",
        description: "Failed to load knowledge bases. Using default configuration.",
        variant: "destructive",
      });

      // Fallback to default knowledge bases
      const fallbackKBs: KnowledgeBase[] = [
        {
          id: 'default',
          name: 'Default Knowledge Base',
          documents: 0,
          lastUpdated: new Date(),
          status: 'inactive'
        }
      ];
      setKnowledgeBases(fallbackKBs);
    }
  };

  const performRAGSearch = async () => {
    if (!query.trim()) return;

    setIsSearching(true);
    setProgress(0);

    try {
      // Simulate RAG search with vector embeddings
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 15, 90));
      }, 200);

      // Call enhanced RAG function
      const { data, error } = await supabase.functions.invoke('enhanced-rag-search', {
        body: {
          query: query.trim(),
          knowledgeBase: selectedKB,
          userId: userProfile?.id,
          userRole: userProfile?.role,
          maxResults: 10,
          threshold: 0.7
        }
      });

      clearInterval(progressInterval);
      setProgress(100);

      if (error) {
        console.error('RAG search error:', error);

        // Try to get relevant documents from knowledge base as fallback
        const { data: documents } = await supabase
          .from('ai_documents')
          .select('*')
          .ilike('content', `%${query}%`)
          .limit(5);

        if (documents && documents.length > 0) {
          const fallbackResults: RAGResult[] = documents.map((doc, index) => ({
            id: doc.id,
            content: doc.content.substring(0, 200) + '...',
            source: doc.title,
            relevance: 0.7 - (index * 0.1),
            metadata: {
              category: doc.category,
              document_type: doc.document_type,
              created_at: doc.created_at
            }
          }));

          setResults(fallbackResults);

          toast({
            title: "🔍 Fallback Search",
            description: `Found ${fallbackResults.length} documents using basic search`,
          });
        } else {
          // Show no results found
          setResults([]);
          toast({
            title: "🔍 No Results",
            description: "No relevant documents found for your query",
            variant: "destructive",
          });
        }
      } else {
        setResults(data?.results || []);

        if (data?.generated_response) {
          toast({
            title: "🧠 RAG Search Complete",
            description: `Found ${data?.results?.length || 0} relevant documents with AI-generated insights`,
          });
        } else {
          toast({
            title: "🔍 Search Complete",
            description: `Found ${data?.results?.length || 0} relevant documents`,
          });
        }
      }

    } catch (error: any) {
      console.error('RAG search error:', error);
      toast({
        title: "⚡ RAG System Alert",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsSearching(false);
      setProgress(0);
    }
  };

  const indexNewDocuments = async () => {
    setIsIndexing(true);
    setProgress(0);

    try {
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 10, 95));
      }, 500);

      // Call document indexing function
      const { data, error } = await supabase.functions.invoke('index-documents', {
        body: {
          userId: userProfile?.id,
          userRole: userProfile?.role,
          forceReindex: false
        }
      });

      clearInterval(progressInterval);
      setProgress(100);

      if (error) throw error;

      await loadKnowledgeBases();

      toast({
        title: "🚀 Indexing Complete",
        description: "Knowledge base updated with latest documents",
      });

    } catch (error: any) {
      console.error('Indexing error:', error);
      toast({
        title: "⚡ Indexing System Alert",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsIndexing(false);
      setProgress(0);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-600/20 text-green-300 border-green-500/50';
      case 'indexing': return 'bg-yellow-600/20 text-yellow-300 border-yellow-500/50';
      case 'inactive': return 'bg-red-600/20 text-red-300 border-red-500/50';
      default: return 'bg-gray-600/20 text-gray-300 border-gray-500/50';
    }
  };

  return (
    <div className="space-y-6">
      {/* RAG Control Panel */}
      <Card className="bg-card border-border">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-foreground">
            <Database className="h-5 w-5 text-primary" />
            Enhanced RAG System
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="🔍 Search across all organizational knowledge..."
              className="flex-1 bg-background border-border text-foreground"
              onKeyPress={(e) => e.key === 'Enter' && performRAGSearch()}
            />
            <Button
              onClick={performRAGSearch}
              disabled={isSearching || !query.trim()}
              className="bg-primary hover:bg-primary/90"
            >
              <Search className="h-4 w-4 mr-2" />
              Search
            </Button>
          </div>

          {isSearching && (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-foreground">
                <Activity className="h-4 w-4 animate-spin" />
                <span className="text-sm">RAG Processing: Semantic search in progress...</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          )}

          <div className="flex gap-2 flex-wrap">
            <Button
              variant={selectedKB === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedKB('all')}
              className="text-xs"
            >
              All Sources
            </Button>
            {knowledgeBases.map((kb) => (
              <Button
                key={kb.id}
                variant={selectedKB === kb.id ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedKB(kb.id)}
                className="text-xs"
              >
                {kb.name}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Knowledge Bases Status */}
      <Card className="bg-gradient-to-r from-blue-900/20 to-cyan-900/20 border-blue-500/30">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-blue-300">
            <Database className="h-5 w-5" />
            Knowledge Bases
          </CardTitle>
          <Button
            onClick={indexNewDocuments}
            disabled={isIndexing}
            variant="outline"
            size="sm"
            className="border-blue-500/30 text-blue-300"
          >
            {isIndexing ? (
              <>
                <Cpu className="h-4 w-4 mr-2 animate-spin" />
                Indexing...
              </>
            ) : (
              <>
                <Zap className="h-4 w-4 mr-2" />
                Reindex
              </>
            )}
          </Button>
        </CardHeader>
        <CardContent>
          {isIndexing && (
            <div className="mb-4 space-y-2">
              <div className="flex items-center gap-2 text-blue-300">
                <Network className="h-4 w-4 animate-pulse" />
                <span className="text-sm">Vector indexing in progress...</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          )}
          
          <div className="grid gap-3">
            {knowledgeBases.map((kb) => (
              <div
                key={kb.id}
                className="flex items-center justify-between p-3 rounded-lg bg-black/20 border border-blue-500/20"
              >
                <div className="flex items-center gap-3">
                  <FileText className="h-4 w-4 text-blue-400" />
                  <div>
                    <h4 className="font-medium text-blue-100">{kb.name}</h4>
                    <p className="text-xs text-blue-400/70">
                      {kb.documents} documents • Updated {kb.lastUpdated.toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <Badge className={getStatusColor(kb.status)}>
                  {kb.status}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Search Results */}
      {results.length > 0 && (
        <Card className="bg-gradient-to-r from-green-900/20 to-teal-900/20 border-green-500/30">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-300">
              <Search className="h-5 w-5" />
              RAG Search Results ({results.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[400px]">
              <div className="space-y-4">
                {results.map((result) => (
                  <div
                    key={result.id}
                    className="p-4 rounded-lg bg-black/20 border border-green-500/20 hover:border-green-500/40 transition-colors"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <Badge className="bg-green-600/20 text-green-300 border-green-500/50">
                        {(result.relevance * 100).toFixed(1)}% Match
                      </Badge>
                      <span className="text-xs text-green-400/70">{result.source}</span>
                    </div>
                    <p className="text-green-100 text-sm mb-2">{result.content}</p>
                    {result.metadata && (
                      <div className="text-xs text-green-400/60">
                        {Object.entries(result.metadata).map(([key, value]) => (
                          <span key={key} className="mr-3">
                            {key}: {String(value)}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
