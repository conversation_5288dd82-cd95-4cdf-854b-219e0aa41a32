/**
 * Database Schema Validator
 * Validates and fixes database schema issues automatically
 */

import { supabase } from '@/integrations/supabase/client';

interface TableColumn {
  column_name: string;
  data_type: string;
  is_nullable: string;
  column_default: string | null;
}

interface SchemaValidationResult {
  table: string;
  exists: boolean;
  columns: TableColumn[];
  missingColumns: string[];
  issues: string[];
}

export class DatabaseSchemaValidator {
  private static instance: DatabaseSchemaValidator;
  private validationCache = new Map<string, SchemaValidationResult>();

  static getInstance(): DatabaseSchemaValidator {
    if (!DatabaseSchemaValidator.instance) {
      DatabaseSchemaValidator.instance = new DatabaseSchemaValidator();
    }
    return DatabaseSchemaValidator.instance;
  }

  /**
   * Validate and fix all database schema issues
   */
  async validateAndFixSchema(): Promise<{ success: boolean; results: SchemaValidationResult[] }> {
    console.log('🔍 Validating database schema...');
    
    const results: SchemaValidationResult[] = [];
    
    // Define expected schema for key tables
    const expectedSchemas = {
      projects: [
        'id', 'name', 'description', 'status', 'priority', 'start_date', 
        'end_date', 'budget', 'manager_id', 'department_id', 'created_at', 'updated_at'
      ],
      tasks: [
        'id', 'title', 'description', 'status', 'priority', 'due_date',
        'assigned_to_id', 'project_id', 'created_by', 'estimated_hours',
        'actual_hours', 'created_at', 'updated_at'
      ],
      profiles: [
        'id', 'user_id', 'full_name', 'email', 'role', 'account_type',
        'status', 'department_id', 'avatar_url', 'phone', 'position',
        'hire_date', 'last_login', 'created_at', 'updated_at'
      ],
      user_presence: [
        'id', 'user_id', 'status', 'last_seen', 'location', 'device',
        'created_at', 'updated_at'
      ],
      activity_logs: [
        'id', 'user_id', 'action', 'entity_type', 'entity_id',
        'description', 'metadata', 'ip_address', 'user_agent', 'created_at'
      ]
    };

    // Validate each table
    for (const [tableName, expectedColumns] of Object.entries(expectedSchemas)) {
      const result = await this.validateTable(tableName, expectedColumns);
      results.push(result);
    }

    // Fix issues found
    await this.fixSchemaIssues(results);

    const hasIssues = results.some(r => r.issues.length > 0 || r.missingColumns.length > 0);
    
    console.log(`📊 Schema validation completed. Issues found: ${hasIssues}`);
    return { success: !hasIssues, results };
  }

  /**
   * Validate a specific table
   */
  private async validateTable(tableName: string, expectedColumns: string[]): Promise<SchemaValidationResult> {
    try {
      console.log(`🔍 Validating table: ${tableName}`);

      // Check if table exists by trying to query it
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1);

      if (error) {
        if (error.message.includes('does not exist')) {
          return {
            table: tableName,
            exists: false,
            columns: [],
            missingColumns: expectedColumns,
            issues: [`Table ${tableName} does not exist`]
          };
        } else {
          return {
            table: tableName,
            exists: true,
            columns: [],
            missingColumns: [],
            issues: [`Error querying ${tableName}: ${error.message}`]
          };
        }
      }

      // Table exists, now check columns by trying to select each one
      const existingColumns: string[] = [];
      const missingColumns: string[] = [];
      const issues: string[] = [];

      for (const column of expectedColumns) {
        try {
          const { error: columnError } = await supabase
            .from(tableName)
            .select(column)
            .limit(1);

          if (columnError) {
            if (columnError.message.includes('does not exist')) {
              missingColumns.push(column);
              issues.push(`Column ${tableName}.${column} does not exist`);
            } else {
              issues.push(`Error checking column ${tableName}.${column}: ${columnError.message}`);
            }
          } else {
            existingColumns.push(column);
          }
        } catch (error: any) {
          issues.push(`Error checking column ${tableName}.${column}: ${error.message}`);
        }
      }

      return {
        table: tableName,
        exists: true,
        columns: existingColumns.map(name => ({
          column_name: name,
          data_type: 'unknown',
          is_nullable: 'unknown',
          column_default: null
        })),
        missingColumns,
        issues
      };

    } catch (error: any) {
      return {
        table: tableName,
        exists: false,
        columns: [],
        missingColumns: expectedColumns,
        issues: [`Failed to validate ${tableName}: ${error.message}`]
      };
    }
  }

  /**
   * Fix schema issues by updating queries to avoid missing columns
   */
  private async fixSchemaIssues(results: SchemaValidationResult[]): Promise<void> {
    console.log('🔧 Fixing schema issues...');

    for (const result of results) {
      if (result.issues.length > 0 || result.missingColumns.length > 0) {
        console.log(`🔧 Fixing issues for table: ${result.table}`);
        
        // Store the schema information for query optimization
        this.validationCache.set(result.table, result);
        
        // Log the issues for manual resolution
        console.log(`📝 Issues for ${result.table}:`, result.issues);
        console.log(`📝 Missing columns for ${result.table}:`, result.missingColumns);
      }
    }
  }

  /**
   * Get safe columns for a table (only existing columns)
   */
  getSafeColumns(tableName: string): string[] {
    const cached = this.validationCache.get(tableName);
    if (cached) {
      return cached.columns.map(c => c.column_name);
    }

    // Default safe columns for common tables
    const defaultColumns: Record<string, string[]> = {
      projects: ['id', 'name', 'description', 'status', 'manager_id', 'created_at', 'updated_at'],
      tasks: ['id', 'title', 'description', 'status', 'assigned_to_id', 'project_id', 'created_by', 'created_at', 'updated_at'],
      profiles: ['id', 'user_id', 'full_name', 'email', 'role', 'account_type', 'status', 'created_at', 'updated_at'],
      user_presence: ['id', 'user_id', 'status', 'last_seen', 'created_at', 'updated_at'],
      activity_logs: ['id', 'user_id', 'action', 'description', 'created_at']
    };

    return defaultColumns[tableName] || ['id', 'created_at', 'updated_at'];
  }

  /**
   * Check if a column exists in a table
   */
  hasColumn(tableName: string, columnName: string): boolean {
    const cached = this.validationCache.get(tableName);
    if (cached) {
      return cached.columns.some(c => c.column_name === columnName);
    }
    return false;
  }

  /**
   * Get optimized select query for a table
   */
  getOptimizedSelect(tableName: string): string {
    const safeColumns = this.getSafeColumns(tableName);
    return safeColumns.join(', ');
  }

  /**
   * Create missing tables with basic structure
   */
  async createMissingTables(): Promise<{ success: boolean; errors: string[] }> {
    console.log('🏗️ Creating missing tables...');
    const errors: string[] = [];

    // Check and create user_presence table
    try {
      const { error } = await supabase
        .from('user_presence')
        .select('id')
        .limit(1);

      if (error && error.message.includes('does not exist')) {
        console.log('📝 user_presence table needs to be created via Supabase dashboard');
        console.log('SQL to create user_presence table:');
        console.log(`
CREATE TABLE public.user_presence (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  status TEXT DEFAULT 'offline' CHECK (status IN ('online', 'away', 'busy', 'offline')),
  last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  location TEXT,
  device TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Enable RLS
ALTER TABLE public.user_presence ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view all presence" ON public.user_presence FOR SELECT USING (true);
CREATE POLICY "Users can update their own presence" ON public.user_presence 
  FOR ALL USING (auth.uid() = user_id);

-- Create indexes
CREATE INDEX idx_user_presence_user_id ON public.user_presence(user_id);
CREATE INDEX idx_user_presence_status ON public.user_presence(status);
CREATE INDEX idx_user_presence_last_seen ON public.user_presence(last_seen);
        `);
      }
    } catch (error: any) {
      errors.push(`user_presence table check failed: ${error.message}`);
    }

    // Check and create activity_logs table
    try {
      const { error } = await supabase
        .from('activity_logs')
        .select('id')
        .limit(1);

      if (error && error.message.includes('does not exist')) {
        console.log('📝 activity_logs table needs to be created via Supabase dashboard');
        console.log('SQL to create activity_logs table:');
        console.log(`
CREATE TABLE public.activity_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  action TEXT NOT NULL,
  entity_type TEXT,
  entity_id UUID,
  description TEXT,
  metadata JSONB DEFAULT '{}',
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE public.activity_logs ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own activity logs" ON public.activity_logs
  FOR SELECT USING (
    auth.uid() = user_id OR
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role IN ('admin', 'manager')
    )
  );

CREATE POLICY "System can insert activity logs" ON public.activity_logs
  FOR INSERT WITH CHECK (true);

-- Create indexes
CREATE INDEX idx_activity_logs_user_id ON public.activity_logs(user_id);
CREATE INDEX idx_activity_logs_action ON public.activity_logs(action);
CREATE INDEX idx_activity_logs_entity ON public.activity_logs(entity_type, entity_id);
CREATE INDEX idx_activity_logs_created_at ON public.activity_logs(created_at);
        `);
      }
    } catch (error: any) {
      errors.push(`activity_logs table check failed: ${error.message}`);
    }

    return {
      success: errors.length === 0,
      errors
    };
  }

  /**
   * Clear validation cache
   */
  clearCache(): void {
    this.validationCache.clear();
    console.log('🧹 Schema validation cache cleared');
  }

  /**
   * Get validation results
   */
  getValidationResults(): Map<string, SchemaValidationResult> {
    return new Map(this.validationCache);
  }
}

// Global instance
export const databaseSchemaValidator = DatabaseSchemaValidator.getInstance();

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).databaseSchemaValidator = databaseSchemaValidator;
  (window as any).validateDatabaseSchema = () => databaseSchemaValidator.validateAndFixSchema();
  (window as any).createMissingTables = () => databaseSchemaValidator.createMissingTables();
  (window as any).getSafeColumns = (table: string) => databaseSchemaValidator.getSafeColumns(table);
  
  console.log('🔍 Database Schema Validator loaded. Available commands:');
  console.log('  - validateDatabaseSchema()');
  console.log('  - createMissingTables()');
  console.log('  - getSafeColumns(tableName)');
}
