import React, { useState, useRef } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Upload, 
  FileText, 
  MessageSquare, 
  Download, 
  BarChart3, 
  Brain,
  Send,
  Loader,
  AlertCircle,
  CheckCircle,
  FileSpreadsheet,
  File
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { handleAIError } from "@/lib/ai/diagnostics";

interface DocumentAnalysis {
  id: string;
  file_name: string;
  summary: string;
  key_points: string[];
  categories: string[];
  sentiment: string;
  word_count: number;
  reading_time: number;
  entities: { name: string; type: string; confidence: number }[];
  topics: { name: string; score: number }[];
}

interface ChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
  documentId?: string;
}

export const AIDocumentAnalyzer = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [chatInput, setChatInput] = useState("");
  const [isChatLoading, setIsChatLoading] = useState(false);
  const [activeDocument, setActiveDocument] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch document analyses
  const { data: analyses = [], isLoading } = useQuery({
    queryKey: ['document-analyses'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('document_analysis')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data;
    },
  });

  // Upload and analyze document mutation
  const uploadMutation = useMutation({
    mutationFn: async (file: File) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Create document analysis record
      const { data: analysisRecord, error: dbError } = await supabase
        .from('document_analysis')
        .insert({
          file_name: file.name,
          status: 'processing',
          created_by: user.id
        })
        .select()
        .single();

      if (dbError) throw dbError;

      // Read file content
      const fileContent = await file.text();

      // Call AI analysis function
      const { data: aiResult, error: aiError } = await supabase.functions.invoke('analyze-document', {
        body: {
          content: fileContent,
          fileName: file.name,
          analysisId: analysisRecord.id
        }
      });

      if (aiError) {
        console.error('AI Analysis Error:', aiError);
        throw new Error(`AI Analysis failed: ${aiError.message || 'Unknown error'}`);
      }

      // Update analysis record with results
      const { data: updatedRecord, error: updateError } = await supabase
        .from('document_analysis')
        .update({
          status: 'completed',
          analysis_result: aiResult
        })
        .eq('id', analysisRecord.id)
        .select()
        .single();

      if (updateError) throw updateError;

      return updatedRecord;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['document-analyses'] });
      toast({
        title: "Analysis Complete",
        description: "Document has been successfully analyzed",
      });
      setSelectedFile(null);
      setAnalysisProgress(0);
    },
    onError: (error) => {
      console.error('Document analysis error:', error);
      const errorInfo = handleAIError(error);

      toast({
        title: errorInfo.title,
        description: errorInfo.message,
        variant: "destructive",
      });
      setAnalysisProgress(0);
    },
  });

  // Chat with document mutation
  const chatMutation = useMutation({
    mutationFn: async ({ message, documentId }: { message: string; documentId: string }) => {
      const { data, error } = await supabase.functions.invoke('analyze-document', {
        body: {
          query: message,
          documentId: documentId,
          type: 'chat'
        }
      });

      if (error) throw error;
      return data;
    },
    onSuccess: (data, variables) => {
      const aiMessage: ChatMessage = {
        id: Date.now().toString() + '_ai',
        content: data.response,
        isUser: false,
        timestamp: new Date(),
        documentId: variables.documentId
      };
      setChatMessages(prev => [...prev, aiMessage]);
    },
    onError: (error) => {
      toast({
        title: "Chat Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        toast({
          title: "File Too Large",
          description: "Please select a file smaller than 10MB",
          variant: "destructive",
        });
        return;
      }
      setSelectedFile(file);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) return;
    
    setIsUploading(true);
    setAnalysisProgress(0);

    // Simulate progress
    const progressInterval = setInterval(() => {
      setAnalysisProgress(prev => Math.min(prev + 10, 90));
    }, 500);

    try {
      await uploadMutation.mutateAsync(selectedFile);
      setAnalysisProgress(100);
    } finally {
      clearInterval(progressInterval);
      setIsUploading(false);
    }
  };

  const handleChat = async () => {
    if (!chatInput.trim() || !activeDocument) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: chatInput,
      isUser: true,
      timestamp: new Date(),
      documentId: activeDocument
    };

    setChatMessages(prev => [...prev, userMessage]);
    setChatInput("");
    setIsChatLoading(true);

    try {
      await chatMutation.mutateAsync({
        message: chatInput,
        documentId: activeDocument
      });
    } finally {
      setIsChatLoading(false);
    }
  };

  const generateReport = async (format: string) => {
    try {
      const { data, error } = await supabase.functions.invoke('generate-report', {
        body: {
          format,
          documentId: activeDocument,
          type: 'document_analysis'
        }
      });

      if (error) throw error;

      // Create download link
      const blob = new Blob([data.content], { type: data.mimeType });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `analysis_report.${format}`;
      a.click();
      URL.revokeObjectURL(url);

      toast({
        title: "Report Generated",
        description: `${format.toUpperCase()} report downloaded successfully`,
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to generate report",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold">AI Document Analyzer</h2>
          <p className="text-muted-foreground">Upload, analyze, and chat with your documents</p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={() => fileInputRef.current?.click()}
            className="bg-primary hover:bg-primary/90"
          >
            <Upload className="h-4 w-4 mr-2" />
            Upload Document
          </Button>
        </div>
      </div>

      <input
        ref={fileInputRef}
        type="file"
        className="hidden"
        accept=".pdf,.doc,.docx,.txt,.md"
        onChange={handleFileSelect}
      />

      {selectedFile && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Ready to Analyze
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <p className="font-medium">{selectedFile.name}</p>
                <p className="text-sm text-muted-foreground">
                  Size: {(selectedFile.size / 1024).toFixed(1)} KB
                </p>
              </div>
              <Button
                onClick={handleUpload}
                disabled={isUploading}
                className="bg-green-600 hover:bg-green-700"
              >
                {isUploading ? (
                  <>
                    <Loader className="h-4 w-4 mr-2 animate-spin" />
                    Analyzing...
                  </>
                ) : (
                  <>
                    <Brain className="h-4 w-4 mr-2" />
                    Analyze Document
                  </>
                )}
              </Button>
            </div>
            {isUploading && (
              <div className="space-y-2">
                <Progress value={analysisProgress} />
                <p className="text-sm text-muted-foreground">
                  Processing document... {analysisProgress}%
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="analyses" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="analyses">Document Analyses</TabsTrigger>
          <TabsTrigger value="chat">AI Chat</TabsTrigger>
          <TabsTrigger value="exports">Export & Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="analyses" className="space-y-4">
          {isLoading ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {[1, 2, 3].map((i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <div className="animate-pulse space-y-4">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                      <div className="h-20 bg-gray-200 rounded"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : analyses.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <FileText className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Documents Analyzed</h3>
                <p className="text-muted-foreground mb-4">
                  Upload your first document to get started with AI analysis
                </p>
                <Button onClick={() => fileInputRef.current?.click()}>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Document
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {analyses.map((analysis) => (
                <Card 
                  key={analysis.id} 
                  className="cursor-pointer hover:shadow-lg transition-shadow"
                  onClick={() => setActiveDocument(analysis.id)}
                >
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-lg truncate">
                        {analysis.file_name}
                      </CardTitle>
                      <Badge variant={analysis.status === 'completed' ? 'default' : 'secondary'}>
                        {analysis.status === 'completed' ? (
                          <CheckCircle className="h-3 w-3 mr-1" />
                        ) : (
                          <AlertCircle className="h-3 w-3 mr-1" />
                        )}
                        {analysis.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {analysis.analysis_result && (
                      <div className="space-y-3">
                        <div className="text-sm text-muted-foreground line-clamp-3">
                          {typeof analysis.analysis_result === 'string' 
                            ? analysis.analysis_result.substring(0, 150) + '...'
                            : 'Analysis completed'
                          }
                        </div>
                        <div className="flex gap-2 flex-wrap">
                          <Badge variant="outline">
                            <Brain className="h-3 w-3 mr-1" />
                            AI Analyzed
                          </Badge>
                          <Badge variant="outline">
                            <MessageSquare className="h-3 w-3 mr-1" />
                            Chat Ready
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Created: {new Date(analysis.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="chat" className="space-y-4">
          {!activeDocument ? (
            <Card>
              <CardContent className="p-12 text-center">
                <MessageSquare className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">Select a Document</h3>
                <p className="text-muted-foreground">
                  Choose a document from the analyses tab to start chatting
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4 lg:grid-cols-3">
              <div className="lg:col-span-2">
                <Card className="h-[600px] flex flex-col">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MessageSquare className="h-5 w-5" />
                      Document Chat
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="flex-1 flex flex-col">
                    <ScrollArea className="flex-1 pr-4 mb-4">
                      <div className="space-y-4">
                        {chatMessages.length === 0 ? (
                          <div className="text-center text-muted-foreground py-8">
                            <Brain className="h-12 w-12 mx-auto mb-2 opacity-50" />
                            <p>Ask me anything about the document!</p>
                          </div>
                        ) : (
                          chatMessages.map((message) => (
                            <div
                              key={message.id}
                              className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}
                            >
                              <div
                                className={`max-w-[80%] rounded-lg p-3 ${
                                  message.isUser
                                    ? 'bg-primary text-primary-foreground'
                                    : 'bg-muted'
                                }`}
                              >
                                <p className="text-sm">{message.content}</p>
                                <p className="text-xs opacity-70 mt-1">
                                  {message.timestamp.toLocaleTimeString()}
                                </p>
                              </div>
                            </div>
                          ))
                        )}
                        {isChatLoading && (
                          <div className="flex justify-start">
                            <div className="bg-muted rounded-lg p-3">
                              <Loader className="h-4 w-4 animate-spin" />
                            </div>
                          </div>
                        )}
                      </div>
                    </ScrollArea>
                    <div className="flex gap-2">
                      <Input
                        placeholder="Ask about the document..."
                        value={chatInput}
                        onChange={(e) => setChatInput(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleChat()}
                        disabled={isChatLoading}
                      />
                      <Button
                        onClick={handleChat}
                        disabled={!chatInput.trim() || isChatLoading}
                      >
                        <Send className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
              <div>
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Quick Actions</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <Button
                      variant="outline"
                      className="w-full justify-start"
                      onClick={() => setChatInput("Summarize this document")}
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      Summarize Document
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full justify-start"
                      onClick={() => setChatInput("What are the key points?")}
                    >
                      <BarChart3 className="h-4 w-4 mr-2" />
                      Key Points
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full justify-start"
                      onClick={() => setChatInput("Generate action items")}
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Action Items
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}
        </TabsContent>

        <TabsContent value="exports" className="space-y-4">
          {!activeDocument ? (
            <Card>
              <CardContent className="p-12 text-center">
                <Download className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">Select a Document</h3>
                <p className="text-muted-foreground">
                  Choose a document to export analysis reports
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card className="cursor-pointer hover:shadow-lg transition-shadow">
                <CardContent className="p-6 text-center" onClick={() => generateReport('pdf')}>
                  <File className="h-12 w-12 mx-auto text-red-500 mb-3" />
                  <h3 className="font-semibold mb-2">PDF Report</h3>
                  <p className="text-sm text-muted-foreground">
                    Comprehensive analysis report
                  </p>
                </CardContent>
              </Card>
              <Card className="cursor-pointer hover:shadow-lg transition-shadow">
                <CardContent className="p-6 text-center" onClick={() => generateReport('xlsx')}>
                  <FileSpreadsheet className="h-12 w-12 mx-auto text-green-500 mb-3" />
                  <h3 className="font-semibold mb-2">Excel Report</h3>
                  <p className="text-sm text-muted-foreground">
                    Data analysis spreadsheet
                  </p>
                </CardContent>
              </Card>
              <Card className="cursor-pointer hover:shadow-lg transition-shadow">
                <CardContent className="p-6 text-center" onClick={() => generateReport('docx')}>
                  <FileText className="h-12 w-12 mx-auto text-blue-500 mb-3" />
                  <h3 className="font-semibold mb-2">Word Document</h3>
                  <p className="text-sm text-muted-foreground">
                    Formatted analysis document
                  </p>
                </CardContent>
              </Card>
              <Card className="cursor-pointer hover:shadow-lg transition-shadow">
                <CardContent className="p-6 text-center" onClick={() => generateReport('csv')}>
                  <BarChart3 className="h-12 w-12 mx-auto text-purple-500 mb-3" />
                  <h3 className="font-semibold mb-2">CSV Data</h3>
                  <p className="text-sm text-muted-foreground">
                    Raw analysis data
                  </p>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};
