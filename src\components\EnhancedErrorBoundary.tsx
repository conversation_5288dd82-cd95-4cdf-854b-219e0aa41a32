import React, { Component, ErrorInfo, ReactNode } from 'react';
import { showErrorToast, showWarningToast } from '@/utils/comprehensive-toast-system';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class EnhancedErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('❌ [ERROR] 🖥️ [UI] Error boundary caught error in', errorInfo.componentStack?.split('\n')[1]?.trim() || 'unknown component', error);

    // Handle auth-related errors gracefully
    if (this.isAuthRelatedError(error)) {
      console.warn('🔧 Auth-related error caught by boundary, handling gracefully');
      showWarningToast({
        title: 'Authentication Issue',
        description: 'There was an authentication issue. The page will recover automatically.',
        duration: 5000
      });

      // Don't reload for auth errors, just reset the error state after a delay
      setTimeout(() => {
        this.setState({ hasError: false, error: undefined });
      }, 3000);
      return;
    }

    // Clear caches on critical errors
    if (error.message.includes('Loading chunk') ||
        error.message.includes('ChunkLoadError') ||
        error.message.includes('Loading CSS chunk')) {
      this.clearCachesAndReload();
      return;
    }

    // Show error toast for other errors
    showErrorToast({
      title: 'Application Error',
      description: 'An unexpected error occurred. The page will recover automatically.',
      duration: 5000
    });
  }

  private isAuthRelatedError(error: Error): boolean {
    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || '';

    return message.includes('auth') ||
           message.includes('session') ||
           message.includes('token') ||
           message.includes('unauthorized') ||
           message.includes('supabase') ||
           stack.includes('auth') ||
           stack.includes('supabase');
  }

  clearCachesAndReload = () => {
    try {
      localStorage.clear();
      sessionStorage.clear();
      
      if ('caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => caches.delete(name));
        });
      }
      
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error('Error clearing caches:', error);
      window.location.reload();
    }
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center p-8 max-w-md">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Something went wrong
            </h1>
            <p className="text-gray-600 mb-6">
              The application encountered an error. We're clearing the cache and reloading.
            </p>
            <button
              onClick={this.clearCachesAndReload}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Clear Cache & Reload
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
