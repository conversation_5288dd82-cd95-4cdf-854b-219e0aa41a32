/**
 * Fix Tasks Table
 * Utility to create and fix the tasks table that's causing 400 errors
 */

import { supabase } from '@/integrations/supabase/client';

export interface TasksFixResult {
  success: boolean;
  message: string;
  details?: string[];
}

export class TasksTableFixer {
  private static instance: TasksTableFixer;
  private isFixed = false;

  static getInstance(): TasksTableFixer {
    if (!TasksTableFixer.instance) {
      TasksTableFixer.instance = new TasksTableFixer();
    }
    return TasksTableFixer.instance;
  }

  /**
   * Check if tasks table exists and is accessible
   */
  async checkTasksTable(): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('tasks')
        .select('id')
        .limit(1);

      if (error) {
        console.log('❌ tasks table check failed:', error.message);
        return false;
      }

      console.log('✅ tasks table exists and is accessible');
      return true;
    } catch (error) {
      console.log('❌ tasks table check error:', error);
      return false;
    }
  }

  /**
   * Check if projects table exists and is accessible
   */
  async checkProjectsTable(): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('projects')
        .select('id')
        .limit(1);

      if (error) {
        console.log('❌ projects table check failed:', error.message);
        return false;
      }

      console.log('✅ projects table exists and is accessible');
      return true;
    } catch (error) {
      console.log('❌ projects table check error:', error);
      return false;
    }
  }

  /**
   * Test the specific query that's failing
   */
  async testFailingQuery(userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🧪 Testing the failing tasks query...');

      const { data, error } = await supabase
        .from('tasks')
        .select(`
          *,
          project:projects(name),
          assignee:profiles!tasks_assigned_to_id_fkey(full_name, email),
          creator:profiles!tasks_created_by_fkey(full_name, email)
        `)
        .eq('assigned_to_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ Failing query error:', error.message);
        return { success: false, error: error.message };
      }

      console.log('✅ Failing query now works, returned', data?.length || 0, 'tasks');
      return { success: true };

    } catch (error: any) {
      console.error('❌ Test query failed:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create tasks and projects tables using RPC function
   */
  async createTables(): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🔧 Creating tasks and projects tables...');

      // Create the tables using RPC
      const { error } = await supabase.rpc('exec_sql', {
        sql: `
          -- Create projects table first (for foreign key)
          CREATE TABLE IF NOT EXISTS public.projects (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            name TEXT NOT NULL,
            description TEXT,
            status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled', 'on_hold')),
            created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
            start_date TIMESTAMP WITH TIME ZONE,
            end_date TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );

          -- Create tasks table
          CREATE TABLE IF NOT EXISTS public.tasks (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            title TEXT NOT NULL,
            description TEXT,
            status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled')),
            priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
            assigned_to_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
            created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
            project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
            due_date TIMESTAMP WITH TIME ZONE,
            estimated_hours NUMERIC DEFAULT 0,
            actual_hours NUMERIC DEFAULT 0,
            progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );

          -- Enable RLS
          ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
          ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;

          -- Create basic policies
          DROP POLICY IF EXISTS "Users can view their tasks" ON public.tasks;
          DROP POLICY IF EXISTS "Users can manage their tasks" ON public.tasks;
          DROP POLICY IF EXISTS "Users can view projects" ON public.projects;

          CREATE POLICY "Users can view their tasks" 
          ON public.tasks 
          FOR SELECT 
          TO authenticated 
          USING (assigned_to_id = auth.uid() OR created_by = auth.uid());

          CREATE POLICY "Users can manage their tasks" 
          ON public.tasks 
          FOR ALL 
          TO authenticated 
          USING (assigned_to_id = auth.uid() OR created_by = auth.uid());

          CREATE POLICY "Users can view projects" 
          ON public.projects 
          FOR SELECT 
          TO authenticated 
          USING (true);

          -- Create indexes
          CREATE INDEX IF NOT EXISTS idx_tasks_assigned_to_id ON public.tasks(assigned_to_id);
          CREATE INDEX IF NOT EXISTS idx_tasks_created_by ON public.tasks(created_by);
          CREATE INDEX IF NOT EXISTS idx_tasks_project_id ON public.tasks(project_id);
        `
      });

      if (error) {
        console.error('❌ Failed to create tasks tables:', error.message);
        return { success: false, error: error.message };
      }

      console.log('✅ Tasks and projects tables created successfully');
      return { success: true };

    } catch (error: any) {
      console.error('❌ Error creating tasks tables:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create sample data for testing
   */
  async createSampleData(): Promise<{ success: boolean; error?: string }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return { success: false, error: 'No authenticated user' };
      }

      console.log('🧪 Creating sample data...');

      // Create a sample project
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .upsert({
          name: 'Sample Project',
          description: 'A sample project for testing',
          status: 'active',
          created_by: user.id
        }, {
          onConflict: 'name'
        })
        .select()
        .single();

      if (projectError) {
        console.warn('⚠️ Could not create sample project:', projectError.message);
      }

      // Create a sample task
      const { error: taskError } = await supabase
        .from('tasks')
        .upsert({
          title: 'Sample Task',
          description: 'A sample task for testing',
          status: 'pending',
          priority: 'medium',
          assigned_to_id: user.id,
          created_by: user.id,
          project_id: project?.id
        }, {
          onConflict: 'title'
        });

      if (taskError) {
        console.warn('⚠️ Could not create sample task:', taskError.message);
        return { success: false, error: taskError.message };
      }

      console.log('✅ Sample data created successfully');
      return { success: true };

    } catch (error: any) {
      console.error('❌ Error creating sample data:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Fix all tasks-related issues
   */
  async fixAllIssues(): Promise<{ success: boolean; results: string[] }> {
    const results: string[] = [];

    try {
      console.log('🔧 Starting tasks table fixes...');

      // Step 1: Check if tables exist
      const tasksExist = await this.checkTasksTable();
      const projectsExist = await this.checkProjectsTable();

      if (!tasksExist || !projectsExist) {
        results.push('Tasks or projects table missing');

        // Step 2: Create tables
        const createResult = await this.createTables();
        if (createResult.success) {
          results.push('Tasks and projects tables created successfully');
        } else {
          results.push(`Failed to create tables: ${createResult.error}`);
          return { success: false, results };
        }
      } else {
        results.push('Tasks and projects tables already exist');
      }

      // Step 3: Test the failing query
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const testResult = await this.testFailingQuery(user.id);
        if (testResult.success) {
          results.push('Failing query now works correctly');
        } else {
          results.push(`Query still failing: ${testResult.error}`);
        }
      }

      // Step 4: Create sample data
      const sampleResult = await this.createSampleData();
      if (sampleResult.success) {
        results.push('Sample data created successfully');
      } else {
        results.push(`Sample data creation failed: ${sampleResult.error}`);
      }

      console.log('✅ Tasks table fixes completed');
      this.isFixed = true;
      return { success: true, results };

    } catch (error: any) {
      console.error('❌ Error during tasks fixes:', error.message);
      results.push(`Error during fixes: ${error.message}`);
      return { success: false, results };
    }
  }

  /**
   * Get the current fix status
   */
  isTableFixed(): boolean {
    return this.isFixed;
  }

  /**
   * Reset the fix status (for testing)
   */
  resetFixStatus(): void {
    this.isFixed = false;
  }
}

// Export singleton instance
export const tasksTableFixer = TasksTableFixer.getInstance();

// Export utility functions
export const fixTasksTable = () => tasksTableFixer.fixAllIssues();
export const checkTasksTable = () => tasksTableFixer.checkTasksTable();
export const testTasksQuery = (userId: string) => tasksTableFixer.testFailingQuery(userId);
