-- CRITICAL DATABASE SETUP FOR CT NIGERIA AI WORKBOARD
-- Run this ENTIRE script in your Supabase SQL Editor to fix all 400 errors
-- This script creates all missing tables, columns, and functions

-- =====================================================
-- 1. ADD MISSING COLUMNS TO PROFILES TABLE
-- =====================================================

-- Add missing columns to profiles table
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS position TEXT;
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS hire_date DATE;
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS location TEXT;

-- =====================================================
-- 2. CREATE USER_PRESENCE TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS public.user_presence (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    status TEXT DEFAULT 'offline' CHECK (status IN ('online', 'away', 'busy', 'offline')),
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    current_page TEXT,
    is_typing BOOLEAN DEFAULT FALSE,
    location_data JSONB,
    device_info JSONB,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Enable RLS for user_presence
ALTER TABLE public.user_presence ENABLE ROW LEVEL SECURITY;

-- Create policies for user_presence
DROP POLICY IF EXISTS "Users can manage their own presence" ON public.user_presence;
CREATE POLICY "Users can manage their own presence" 
ON public.user_presence FOR ALL TO authenticated 
USING (auth.uid() = user_id);

-- =====================================================
-- 3. CREATE TIME_LOGS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS public.time_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    clock_in_time TIMESTAMP WITH TIME ZONE NOT NULL,
    clock_out_time TIMESTAMP WITH TIME ZONE,
    break_start_time TIMESTAMP WITH TIME ZONE,
    break_end_time TIMESTAMP WITH TIME ZONE,
    total_hours NUMERIC(5,2) DEFAULT 0,
    break_hours NUMERIC(5,2) DEFAULT 0,
    overtime_hours NUMERIC(5,2) DEFAULT 0,
    status TEXT DEFAULT 'clocked_in' CHECK (status IN ('clocked_in', 'on_break', 'clocked_out', 'overtime')),
    location_data JSONB,
    device_info JSONB,
    ip_address INET,
    notes TEXT,
    approved_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    approved_at TIMESTAMP WITH TIME ZONE,
    is_approved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_clock_times CHECK (
        clock_out_time IS NULL OR clock_out_time > clock_in_time
    ),
    CONSTRAINT valid_break_times CHECK (
        (break_start_time IS NULL AND break_end_time IS NULL) OR
        (break_start_time IS NOT NULL AND break_end_time IS NOT NULL AND break_end_time > break_start_time)
    )
);

-- Enable RLS for time_logs
ALTER TABLE public.time_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for time_logs
DROP POLICY IF EXISTS "Users can manage their own time logs" ON public.time_logs;
CREATE POLICY "Users can manage their own time logs" 
ON public.time_logs FOR ALL TO authenticated 
USING (auth.uid() = user_id);

-- =====================================================
-- 4. CREATE CLOCK IN/OUT FUNCTIONS
-- =====================================================

-- First drop all existing clock_in functions using a dynamic query
DO $$ 
DECLARE 
    func record;
BEGIN
    FOR func IN (
        SELECT ns.nspname as schema_name, p.proname as proc_name, pg_get_function_identity_arguments(p.oid) as args
        FROM pg_proc p 
        INNER JOIN pg_namespace ns ON p.pronamespace = ns.oid
        WHERE p.proname = 'clock_in'
        AND ns.nspname = 'public'
    )
    LOOP
        EXECUTE format('DROP FUNCTION IF EXISTS %I.%I(%s) CASCADE', 
            func.schema_name, func.proc_name, func.args);
    END LOOP;
END $$;

-- Create function to clock in
CREATE OR REPLACE FUNCTION public.clock_in(
    p_user_id UUID,
    p_location_data JSONB DEFAULT NULL,
    p_device_info JSONB DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_notes TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_time_log_id UUID;
    v_active_log_exists BOOLEAN;
BEGIN
    -- Check if user already has an active time log
    SELECT EXISTS(
        SELECT 1 FROM public.time_logs 
        WHERE user_id = p_user_id 
        AND clock_out_time IS NULL
    ) INTO v_active_log_exists;
    
    IF v_active_log_exists THEN
        RAISE EXCEPTION 'User already has an active time log. Please clock out first.';
    END IF;
    
    -- Create new time log
    INSERT INTO public.time_logs (
        user_id,
        clock_in_time,
        location_data,
        device_info,
        ip_address,
        notes,
        status
    ) VALUES (
        p_user_id,
        NOW(),
        p_location_data,
        p_device_info,
        p_ip_address,
        p_notes,
        'clocked_in'
    ) RETURNING id INTO v_time_log_id;
    
    RETURN v_time_log_id;
END;
$$;

-- Create function to clock out
CREATE OR REPLACE FUNCTION public.clock_out(
    p_user_id UUID,
    p_notes TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_time_log_id UUID;
BEGIN
    -- Update the active time log
    UPDATE public.time_logs 
    SET 
        clock_out_time = NOW(),
        notes = COALESCE(p_notes, notes),
        status = 'clocked_out',
        updated_at = NOW()
    WHERE user_id = p_user_id 
    AND clock_out_time IS NULL
    RETURNING id INTO v_time_log_id;
    
    IF v_time_log_id IS NULL THEN
        RAISE EXCEPTION 'No active time log found for user.';
    END IF;
    
    RETURN v_time_log_id;
END;
$$;

-- =====================================================
-- 5. CREATE MISSING TABLES
-- =====================================================

-- Create memos table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.memos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT,
    created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    department_id UUID,
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'archived', 'deleted')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create departments table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.departments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    manager_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create projects table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.projects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    manager_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    department_id UUID REFERENCES public.departments(id) ON DELETE SET NULL,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'on_hold', 'cancelled')),
    start_date DATE,
    end_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create tasks table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.tasks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    assigned_to_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'blocked')),
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    due_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create document_archive table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.document_archive (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    file_url TEXT NOT NULL,
    file_type TEXT,
    file_size BIGINT,
    uploaded_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    department_id UUID REFERENCES public.departments(id) ON DELETE SET NULL,
    project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'archived', 'deleted')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create procurement_requests table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.procurement_requests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    requested_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    department_id UUID REFERENCES public.departments(id) ON DELETE SET NULL,
    project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'accountant_review')),
    total_cost NUMERIC(12,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create procurement_items table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.procurement_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    procurement_id UUID REFERENCES public.procurement_requests(id) ON DELETE CASCADE,
    item_name TEXT NOT NULL,
    description TEXT,
    quantity INTEGER NOT NULL DEFAULT 1,
    unit_cost NUMERIC(12,2) DEFAULT 0,
    estimated_total_cost NUMERIC(12,2) GENERATED ALWAYS AS (quantity * unit_cost) STORED,
    procurement_status TEXT DEFAULT 'pending' CHECK (procurement_status IN ('pending', 'approved', 'rejected', 'ordered', 'received')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create procurement_approvals table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.procurement_approvals (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    procurement_id UUID REFERENCES public.procurement_requests(id) ON DELETE CASCADE,
    approver_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    approver_role TEXT NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    comments TEXT,
    approval_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 6. GRANT PERMISSIONS
-- =====================================================

-- Grant permissions on all tables
GRANT ALL ON public.user_presence TO authenticated;
GRANT ALL ON public.time_logs TO authenticated;
GRANT ALL ON public.memos TO authenticated;
GRANT ALL ON public.procurement_requests TO authenticated;
GRANT ALL ON public.departments TO authenticated;
GRANT ALL ON public.projects TO authenticated;
GRANT ALL ON public.tasks TO authenticated;
GRANT ALL ON public.document_archive TO authenticated;
GRANT ALL ON public.procurement_items TO authenticated;
GRANT ALL ON public.procurement_approvals TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION public.clock_in TO authenticated;
GRANT EXECUTE ON FUNCTION public.clock_out TO authenticated;

-- =====================================================
-- 7. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Indexes for user_presence
CREATE INDEX IF NOT EXISTS idx_user_presence_user_id ON public.user_presence(user_id);
CREATE INDEX IF NOT EXISTS idx_user_presence_status ON public.user_presence(status);

-- Indexes for time_logs
CREATE INDEX IF NOT EXISTS idx_time_logs_user_id ON public.time_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_time_logs_clock_in_time ON public.time_logs(clock_in_time);
CREATE INDEX IF NOT EXISTS idx_time_logs_status ON public.time_logs(status);

-- =====================================================
-- 8. ENABLE RLS ON ALL TABLES
-- =====================================================

-- Enable RLS on memos
ALTER TABLE public.memos ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Users can view memos" ON public.memos;
CREATE POLICY "Users can view memos" ON public.memos FOR SELECT TO authenticated USING (true);
DROP POLICY IF EXISTS "Users can create memos" ON public.memos;
CREATE POLICY "Users can create memos" ON public.memos FOR INSERT TO authenticated WITH CHECK (auth.uid() = created_by);

-- Enable RLS on all tables
ALTER TABLE public.departments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.document_archive ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.procurement_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.procurement_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.procurement_approvals ENABLE ROW LEVEL SECURITY;

-- Policies for departments
DROP POLICY IF EXISTS "Users can view departments" ON public.departments;
DROP POLICY IF EXISTS "Department managers can update their departments" ON public.departments;
CREATE POLICY "Users can view departments" ON public.departments FOR SELECT TO authenticated USING (true);
CREATE POLICY "Department managers can update their departments" ON public.departments FOR UPDATE TO authenticated USING (auth.uid() = manager_id);

-- Policies for projects
DROP POLICY IF EXISTS "Users can view projects" ON public.projects;
DROP POLICY IF EXISTS "Project managers can update their projects" ON public.projects;
DROP POLICY IF EXISTS "Users can create projects" ON public.projects;
CREATE POLICY "Users can view projects" ON public.projects FOR SELECT TO authenticated USING (true);
CREATE POLICY "Project managers can update their projects" ON public.projects FOR UPDATE TO authenticated USING (auth.uid() = manager_id);
CREATE POLICY "Users can create projects" ON public.projects FOR INSERT TO authenticated WITH CHECK (auth.uid() = created_by);

-- Policies for tasks
DROP POLICY IF EXISTS "Users can view tasks" ON public.tasks;
DROP POLICY IF EXISTS "Task owners can update their tasks" ON public.tasks;
DROP POLICY IF EXISTS "Users can create tasks" ON public.tasks;
CREATE POLICY "Users can view tasks" ON public.tasks FOR SELECT TO authenticated USING (true);
CREATE POLICY "Task owners can update their tasks" ON public.tasks FOR UPDATE TO authenticated USING (auth.uid() = assigned_to_id OR auth.uid() = created_by);
CREATE POLICY "Users can create tasks" ON public.tasks FOR INSERT TO authenticated WITH CHECK (auth.uid() = created_by);

-- Policies for document_archive
DROP POLICY IF EXISTS "Users can view documents" ON public.document_archive;
DROP POLICY IF EXISTS "Users can upload documents" ON public.document_archive;
DROP POLICY IF EXISTS "Document owners can update their documents" ON public.document_archive;
CREATE POLICY "Users can view documents" ON public.document_archive FOR SELECT TO authenticated USING (true);
CREATE POLICY "Users can upload documents" ON public.document_archive FOR INSERT TO authenticated WITH CHECK (auth.uid() = uploaded_by);
CREATE POLICY "Document owners can update their documents" ON public.document_archive FOR UPDATE TO authenticated USING (auth.uid() = uploaded_by);

-- Policies for procurement_requests
DROP POLICY IF EXISTS "Users can view procurement requests" ON public.procurement_requests;
DROP POLICY IF EXISTS "Users can create procurement requests" ON public.procurement_requests;
DROP POLICY IF EXISTS "Request owners can update their requests" ON public.procurement_requests;
CREATE POLICY "Users can view procurement requests" ON public.procurement_requests FOR SELECT TO authenticated USING (true);
CREATE POLICY "Users can create procurement requests" ON public.procurement_requests FOR INSERT TO authenticated WITH CHECK (auth.uid() = requested_by);
CREATE POLICY "Request owners can update their requests" ON public.procurement_requests FOR UPDATE TO authenticated USING (auth.uid() = requested_by);

-- Policies for procurement_items
DROP POLICY IF EXISTS "Users can view procurement items" ON public.procurement_items;
DROP POLICY IF EXISTS "Users can manage procurement items" ON public.procurement_items;
CREATE POLICY "Users can view procurement items" ON public.procurement_items FOR SELECT TO authenticated USING (true);
CREATE POLICY "Users can manage procurement items" ON public.procurement_items FOR ALL TO authenticated USING (
    EXISTS (
        SELECT 1 FROM public.procurement_requests pr 
        WHERE pr.id = procurement_items.procurement_id 
        AND pr.requested_by = auth.uid()
    )
);

-- Policies for procurement_approvals
DROP POLICY IF EXISTS "Users can view procurement approvals" ON public.procurement_approvals;
DROP POLICY IF EXISTS "Approvers can manage approvals" ON public.procurement_approvals;
CREATE POLICY "Users can view procurement approvals" ON public.procurement_approvals FOR SELECT TO authenticated USING (true);
CREATE POLICY "Approvers can manage approvals" ON public.procurement_approvals FOR ALL TO authenticated USING (auth.uid() = approver_id);

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

-- Insert a test record to verify everything works
DO $$
BEGIN
    RAISE NOTICE 'Database setup completed successfully!';
    RAISE NOTICE 'All tables, functions, and policies have been created.';
    RAISE NOTICE 'The application should now work without 400 errors.';
END $$;
