/**
 * LangChain Agent System
 * Intelligent agents with tool usage, memory, and context awareness
 */

import { supabase } from '@/integrations/supabase/client';
import { lang<PERSON>hainConfig } from './config';
import { lang<PERSON>hainMemory } from './memory';
import { lang<PERSON>hainRAG } from './rag-system';

export interface AgentTool {
  name: string;
  description: string;
  parameters: Record<string, any>;
  execute: (params: Record<string, any>) => Promise<any>;
}

export interface AgentAction {
  tool: string;
  parameters: Record<string, any>;
  reasoning: string;
}

export interface AgentResponse {
  message: string;
  actions: AgentAction[];
  confidence: number;
  reasoning: string;
  sources?: any[];
  executionTime: number;
}

export class LangChainAgentSystem {
  private static tools = new Map<string, AgentTool>();
  private static initialized = false;

  /**
   * Initialize agent system with tools
   */
  static async initialize(): Promise<void> {
    if (this.initialized) return;

    // Register built-in tools
    await this.registerBuiltInTools();
    this.initialized = true;
  }

  /**
   * Register built-in tools
   */
  private static async registerBuiltInTools(): Promise<void> {
    // Database Query Tool
    this.registerTool({
      name: 'database_query',
      description: 'Query the database for information about users, projects, tasks, etc.',
      parameters: {
        table: { type: 'string', description: 'Table name to query' },
        filters: { type: 'object', description: 'Filters to apply' },
        limit: { type: 'number', description: 'Maximum results to return' },
      },
      execute: async (params) => {
        try {
          let query = supabase.from(params.table).select('*');
          
          if (params.filters) {
            Object.entries(params.filters).forEach(([key, value]) => {
              query = query.eq(key, value);
            });
          }
          
          if (params.limit) {
            query = query.limit(params.limit);
          }
          
          const { data, error } = await query;
          if (error) throw error;
          
          return { success: true, data, count: data?.length || 0 };
        } catch (error) {
          return { success: false, error: error.message };
        }
      },
    });

    // Create Task Tool
    this.registerTool({
      name: 'create_task',
      description: 'Create a new task for a user or project',
      parameters: {
        title: { type: 'string', description: 'Task title' },
        description: { type: 'string', description: 'Task description' },
        assignee_id: { type: 'string', description: 'User ID to assign task to' },
        project_id: { type: 'string', description: 'Project ID (optional)' },
        priority: { type: 'string', description: 'Task priority (low, medium, high)' },
        due_date: { type: 'string', description: 'Due date (ISO string)' },
      },
      execute: async (params) => {
        try {
          const { data, error } = await supabase
            .from('tasks')
            .insert({
              title: params.title,
              description: params.description,
              assignee_id: params.assignee_id,
              project_id: params.project_id,
              priority: params.priority || 'medium',
              due_date: params.due_date,
              status: 'pending',
              created_by: params.created_by,
            })
            .select()
            .single();

          if (error) throw error;
          return { success: true, task: data };
        } catch (error) {
          return { success: false, error: error.message };
        }
      },
    });

    // Search Documents Tool
    this.registerTool({
      name: 'search_documents',
      description: 'Search through documents and knowledge base',
      parameters: {
        query: { type: 'string', description: 'Search query' },
        limit: { type: 'number', description: 'Maximum results' },
      },
      execute: async (params) => {
        try {
          const results = await langChainRAG.query({
            question: params.query,
            maxResults: params.limit || 5,
          });
          
          return {
            success: true,
            results: results.sources,
            confidence: results.confidence,
          };
        } catch (error) {
          return { success: false, error: error.message };
        }
      },
    });

    // Get User Info Tool
    this.registerTool({
      name: 'get_user_info',
      description: 'Get information about a user',
      parameters: {
        user_id: { type: 'string', description: 'User ID to get info for' },
      },
      execute: async (params) => {
        try {
          const { data, error } = await supabase
            .from('profiles')
            .select('*, department:departments(name)')
            .eq('id', params.user_id)
            .single();

          if (error) throw error;
          return { success: true, user: data };
        } catch (error) {
          return { success: false, error: error.message };
        }
      },
    });

    // Send Notification Tool
    this.registerTool({
      name: 'send_notification',
      description: 'Send a notification to a user',
      parameters: {
        user_id: { type: 'string', description: 'User ID to notify' },
        title: { type: 'string', description: 'Notification title' },
        message: { type: 'string', description: 'Notification message' },
        type: { type: 'string', description: 'Notification type' },
      },
      execute: async (params) => {
        try {
          const { data, error } = await supabase.functions.invoke('send-notification', {
            body: {
              userId: params.user_id,
              title: params.title,
              message: params.message,
              type: params.type || 'info',
            },
          });

          if (error) throw error;
          return { success: true, notification: data };
        } catch (error) {
          return { success: false, error: error.message };
        }
      },
    });

    // Get Project Status Tool
    this.registerTool({
      name: 'get_project_status',
      description: 'Get status and progress of a project',
      parameters: {
        project_id: { type: 'string', description: 'Project ID' },
      },
      execute: async (params) => {
        try {
          const { data: project, error: projectError } = await supabase
            .from('projects')
            .select('*, tasks(*)')
            .eq('id', params.project_id)
            .single();

          if (projectError) throw projectError;

          const totalTasks = project.tasks?.length || 0;
          const completedTasks = project.tasks?.filter(t => t.status === 'completed').length || 0;
          const progress = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

          return {
            success: true,
            project: {
              ...project,
              progress,
              totalTasks,
              completedTasks,
            },
          };
        } catch (error) {
          return { success: false, error: error.message };
        }
      },
    });
  }

  /**
   * Register a new tool
   */
  static registerTool(tool: AgentTool): void {
    this.tools.set(tool.name, tool);
  }

  /**
   * Process agent query
   */
  static async processQuery(
    query: string,
    userId: string,
    sessionId?: string
  ): Promise<AgentResponse> {
    const startTime = Date.now();
    
    try {
      await this.initialize();

      // Get user context
      const userContext = await langChainMemory.getUserContext(userId);
      
      // Determine if tools are needed
      const toolAnalysis = await this.analyzeToolNeeds(query, userContext);
      
      let actions: AgentAction[] = [];
      let toolResults: any[] = [];

      // Execute tools if needed
      if (toolAnalysis.needsTools) {
        for (const action of toolAnalysis.suggestedActions) {
          const result = await this.executeTool(action.tool, action.parameters);
          actions.push(action);
          toolResults.push(result);
        }
      }

      // Generate final response
      const response = await this.generateResponse(query, userContext, actions, toolResults);
      
      // Save to memory if session provided
      if (sessionId) {
        await langChainMemory.addMessage(sessionId, 'user', query);
        await langChainMemory.addMessage(sessionId, 'assistant', response.message, {
          actions,
          confidence: response.confidence,
        });
      }

      return {
        message: response.message,
        actions,
        confidence: response.confidence,
        reasoning: response.reasoning,
        sources: response.sources,
        executionTime: Date.now() - startTime,
      };
    } catch (error) {
      console.error('Agent query processing failed:', error);
      return {
        message: "I encountered an error while processing your request. Please try again.",
        actions: [],
        confidence: 0.1,
        reasoning: "Error occurred during processing",
        executionTime: Date.now() - startTime,
      };
    }
  }

  /**
   * Analyze if tools are needed for the query
   */
  private static async analyzeToolNeeds(
    query: string,
    userContext: any
  ): Promise<{ needsTools: boolean; suggestedActions: AgentAction[] }> {
    try {
      const { data, error } = await supabase.functions.invoke('analyze-agent-query', {
        body: {
          query,
          userContext,
          availableTools: Array.from(this.tools.keys()),
        },
      });

      if (error) {
        console.warn('Tool analysis failed, using fallback');
        return this.fallbackToolAnalysis(query);
      }

      return {
        needsTools: data.needsTools || false,
        suggestedActions: data.suggestedActions || [],
      };
    } catch (error) {
      console.warn('Tool analysis error:', error);
      return this.fallbackToolAnalysis(query);
    }
  }

  /**
   * Fallback tool analysis using simple keyword matching
   */
  private static fallbackToolAnalysis(query: string): { needsTools: boolean; suggestedActions: AgentAction[] } {
    const lowerQuery = query.toLowerCase();
    const actions: AgentAction[] = [];

    // Simple keyword-based tool detection
    if (lowerQuery.includes('create task') || lowerQuery.includes('new task')) {
      actions.push({
        tool: 'create_task',
        parameters: {},
        reasoning: 'User wants to create a task',
      });
    }

    if (lowerQuery.includes('search') || lowerQuery.includes('find')) {
      actions.push({
        tool: 'search_documents',
        parameters: { query: query },
        reasoning: 'User wants to search for information',
      });
    }

    if (lowerQuery.includes('user') || lowerQuery.includes('profile')) {
      actions.push({
        tool: 'database_query',
        parameters: { table: 'profiles', limit: 10 },
        reasoning: 'User asking about users or profiles',
      });
    }

    return {
      needsTools: actions.length > 0,
      suggestedActions: actions,
    };
  }

  /**
   * Execute a tool
   */
  private static async executeTool(toolName: string, parameters: Record<string, any>): Promise<any> {
    const tool = this.tools.get(toolName);
    if (!tool) {
      return { success: false, error: `Tool ${toolName} not found` };
    }

    try {
      return await tool.execute(parameters);
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Generate final response
   */
  private static async generateResponse(
    query: string,
    userContext: any,
    actions: AgentAction[],
    toolResults: any[]
  ): Promise<{ message: string; confidence: number; reasoning: string; sources?: any[] }> {
    try {
      const { data, error } = await supabase.functions.invoke('generate-agent-response', {
        body: {
          query,
          userContext,
          actions,
          toolResults,
        },
      });

      if (error) {
        throw error;
      }

      return {
        message: data.message || "I processed your request.",
        confidence: data.confidence || 0.8,
        reasoning: data.reasoning || "Generated response based on available information",
        sources: data.sources,
      };
    } catch (error) {
      console.error('Response generation failed:', error);
      return {
        message: this.generateFallbackResponse(query, toolResults),
        confidence: 0.6,
        reasoning: "Generated fallback response",
      };
    }
  }

  /**
   * Generate fallback response
   */
  private static generateFallbackResponse(query: string, toolResults: any[]): string {
    if (toolResults.length === 0) {
      return "I understand your request, but I don't have specific information to help with that right now.";
    }

    const successfulResults = toolResults.filter(r => r.success);
    if (successfulResults.length === 0) {
      return "I tried to help with your request, but encountered some issues. Please try again or be more specific.";
    }

    return `I found some information that might help. I executed ${successfulResults.length} action(s) and gathered relevant data for your request.`;
  }

  /**
   * Get available tools
   */
  static getAvailableTools(): AgentTool[] {
    return Array.from(this.tools.values());
  }

  /**
   * Get tool by name
   */
  static getTool(name: string): AgentTool | undefined {
    return this.tools.get(name);
  }
}

export const langChainAgent = LangChainAgentSystem;
