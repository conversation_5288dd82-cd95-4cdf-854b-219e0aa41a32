import { useState } from 'react';
import { Navigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
// Removed Tabs imports - using custom implementation to avoid Radix Presence conflicts
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAuth } from '@/components/auth/AuthProvider';
import { Loader2, Building, Users, Shield, Clock } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { showActionToast, showSuccessToast, showErrorToast } from '@/utils/comprehensive-toast-system';
import { ThemeSwitcher } from '@/components/ThemeSwitcher';
import { useEffect } from 'react';

export const AuthPage = () => {
  const [isSignUp, setIsSignUp] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [phone, setPhone] = useState('');
  const [position, setPosition] = useState('');
  const [departmentId, setDepartmentId] = useState('');
  const [role, setRole] = useState('staff');
  const [loading, setLoading] = useState(false);

  const { signIn, signUp, isAuthenticated, userProfile } = useAuth();

  // Removed AOS initialization to prevent DOM manipulation conflicts with React

  // Fetch departments for signup
  const { data: departments } = useQuery({
    queryKey: ['departments'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('departments')
        .select('*')
        .order('name');
      if (error) throw error;
      return data;
    },
  });

  // Redirect if already authenticated
  if (isAuthenticated && userProfile) {
    const roleRoutes = {
      admin: '/dashboard/admin',
      manager: '/dashboard/manager', 
      staff: '/dashboard/staff',
      accountant: '/dashboard/accountant',
      'staff-admin': '/dashboard/staff-admin'
    };
    return <Navigate to={roleRoutes[userProfile.role as keyof typeof roleRoutes] || '/dashboard/staff'} replace />;
  }

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      await signIn(email, password);
      showActionToast('login', true, 'Welcome back! Redirecting to your dashboard...');
    } catch (error: any) {
      console.error('❌ Sign in failed:', error);

      // Handle specific auth errors gracefully
      let errorMessage = "Please check your credentials and try again.";

      if (error.message?.includes('Invalid login credentials')) {
        errorMessage = "Invalid email or password. Please check your credentials.";
      } else if (error.message?.includes('Email not confirmed')) {
        errorMessage = "Please check your email and click the confirmation link.";
      } else if (error.message?.includes('Too many requests')) {
        errorMessage = "Too many login attempts. Please wait a moment and try again.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      showActionToast('login', false, errorMessage, error);
    } finally {
      setLoading(false);
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      await signUp(email, password, {
        full_name: fullName,
        phone,
        position,
        department_id: departmentId,
        role: role,
        account_type: role
      });
      showActionToast('signup', true, 'Welcome to CTNL! Please check your email to verify your account.');
      setIsSignUp(false);
    } catch (error: any) {
      console.error('❌ Sign up failed:', error);
      showActionToast('signup', false, error.message || "Please try again with different details.", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="dashboard-container relative overflow-hidden min-h-screen">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Gradient Orbs */}
        <div className="absolute -top-40 -right-40 w-96 h-96 bg-gradient-to-br from-[#ff1c04]/30 to-[#0FA0CE]/20 rounded-full blur-3xl animate-pulse floating-animation"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-tr from-[#0FA0CE]/30 to-[#ff1c04]/20 rounded-full blur-3xl animate-pulse floating-animation"></div>
        
        {/* Geometric Patterns */}
        <div className="absolute top-20 left-10 w-32 h-32 border border-[#ff1c04]/20 rounded-full animate-spin" style={{ animationDuration: '20s' }}></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 border border-[#0FA0CE]/20 rounded-full animate-spin" style={{ animationDuration: '15s', animationDirection: 'reverse' }}></div>
      </div>

      {/* Theme Switcher */}
      <div className="fixed top-6 right-6 z-50">
        <ThemeSwitcher />
      </div>

      {/* Clock In Access Button */}
      <div className="fixed top-6 left-6 z-50">
        <Button
          onClick={() => window.location.href = '/clock-in'}
          variant="outline"
          className="glassmorphism border-[#ff1c04]/30 hover:bg-[#ff1c04]/10 hover:border-[#ff1c04]/50 transition-all duration-300"
        >
          <Clock className="h-4 w-4 mr-2 text-[#ff1c04]" />
          Clock In
        </Button>
      </div>

      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          {/* Logo Section */}
          <div className="text-center mb-8">
            <div className="relative">
              <img 
                src="/lovable-uploads/491c7e61-a4fb-46a3-a002-904b84354e48.png" 
                alt="CTNL Logo" 
                className="h-16 w-auto mx-auto drop-shadow-2xl mb-4"
              />
              <div className="absolute inset-0 bg-gradient-to-r from-[#ff1c04]/20 to-[#0FA0CE]/20 blur-xl -z-10 animate-pulse"></div>
            </div>
            <h1 className="text-3xl font-bold modern-heading mb-2">CTNL Platform</h1>
            <p className="text-muted-foreground">AI-Powered Workflow Management System</p>
          </div>

          <Card className="glass-card border-0 shadow-2xl">
            <CardHeader className="text-center space-y-2">
              <CardTitle className="text-2xl font-bold modern-heading">
                {isSignUp ? 'Create Account' : 'Welcome Back'}
              </CardTitle>
              <CardDescription className="text-muted-foreground">
                {isSignUp ? 'Join the CTNL team today' : 'Sign in to access your workspace'}
              </CardDescription>
            </CardHeader>
            
            <CardContent>
              {/* Custom Tab Implementation without Radix Presence */}
              <div className="w-full">
                <div className="grid w-full grid-cols-2 mb-6 glassmorphism rounded-md p-1">
                  <button
                    type="button"
                    onClick={() => setIsSignUp(false)}
                    className={`inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
                      !isSignUp 
                        ? "bg-gradient-to-r from-[#ff1c04] to-[#e01703] text-white shadow-sm" 
                        : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                    }`}
                  >
                    Sign In
                  </button>
                  <button
                    type="button"
                    onClick={() => setIsSignUp(true)}
                    className={`inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
                      isSignUp 
                        ? "bg-gradient-to-r from-[#0FA0CE] to-[#0FA0CE]/80 text-white shadow-sm" 
                        : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                    }`}
                  >
                    Sign Up
                  </button>
                </div>
                
                {/* Sign In Form */}
                {!isSignUp && (
                  <form onSubmit={handleSignIn} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="signin-email" className="font-medium">Email Address</Label>
                      <Input
                        id="signin-email"
                        type="email"
                        placeholder="Enter your email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="glassmorphism border-white/20 dark:border-gray-700/20 focus:border-[#ff1c04]/50 transition-all duration-300"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="signin-password" className="font-medium">Password</Label>
                      <Input
                        id="signin-password"
                        type="password"
                        placeholder="Enter your password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="glassmorphism border-white/20 dark:border-gray-700/20 focus:border-[#ff1c04]/50 transition-all duration-300"
                        required
                      />
                    </div>
                    <Button 
                      type="submit" 
                      className="modern-btn w-full text-lg py-6 rounded-xl" 
                      disabled={loading}
                    >
                      {loading ? (
                        <>
                          <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                          Signing in...
                        </>
                      ) : (
                        <>
                          <Shield className="mr-2 h-5 w-5" />
                          Sign In to Dashboard
                        </>
                      )}
                    </Button>
                  </form>
                )}
                
                {/* Sign Up Form */}
                {isSignUp && (
                  <form onSubmit={handleSignUp} className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="fullName" className="font-medium">Full Name</Label>
                        <Input
                          id="fullName"
                          placeholder="John Doe"
                          value={fullName}
                          onChange={(e) => setFullName(e.target.value)}
                          className="glassmorphism border-white/20 dark:border-gray-700/20 focus:border-[#0FA0CE]/50 transition-all duration-300"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phone" className="font-medium">Phone</Label>
                        <Input
                          id="phone"
                          placeholder="+234..."
                          value={phone}
                          onChange={(e) => setPhone(e.target.value)}
                          className="glassmorphism border-white/20 dark:border-gray-700/20 focus:border-[#0FA0CE]/50 transition-all duration-300"
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="signup-email" className="font-medium">Email Address</Label>
                      <Input
                        id="signup-email"
                        type="email"
                        placeholder="Enter your email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="glassmorphism border-white/20 dark:border-gray-700/20 focus:border-[#0FA0CE]/50 transition-all duration-300"
                        required
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="signup-password" className="font-medium">Password</Label>
                      <Input
                        id="signup-password"
                        type="password"
                        placeholder="Choose a secure password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="glassmorphism border-white/20 dark:border-gray-700/20 focus:border-[#0FA0CE]/50 transition-all duration-300"
                        required
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="position" className="font-medium">Position</Label>
                      <Input
                        id="position"
                        placeholder="Your job title"
                        value={position}
                        onChange={(e) => setPosition(e.target.value)}
                        className="glassmorphism border-white/20 dark:border-gray-700/20 focus:border-[#0FA0CE]/50 transition-all duration-300"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="role" className="font-medium">Role</Label>
                      <Select value={role} onValueChange={setRole}>
                        <SelectTrigger className="glassmorphism border-white/20 dark:border-gray-700/20 focus:border-[#0FA0CE]/50 transition-all duration-300">
                          <SelectValue placeholder="Select your role" />
                        </SelectTrigger>
                        <SelectContent className="glassmorphism border-white/20 dark:border-gray-700/20">
                          <SelectItem value="staff">Staff</SelectItem>
                          <SelectItem value="manager">Manager</SelectItem>
                          <SelectItem value="admin">Admin</SelectItem>
                          <SelectItem value="accountant">Accountant</SelectItem>
                          <SelectItem value="staff-admin">Staff Admin</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="department" className="font-medium">Department</Label>
                      <Select value={departmentId} onValueChange={setDepartmentId}>
                        <SelectTrigger className="glassmorphism border-white/20 dark:border-gray-700/20 focus:border-[#0FA0CE]/50 transition-all duration-300">
                          <SelectValue placeholder="Select your department" />
                        </SelectTrigger>
                        <SelectContent className="glassmorphism border-white/20 dark:border-gray-700/20">
                          {departments?.map((dept) => (
                            <SelectItem key={dept.id} value={dept.id}>
                              {dept.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <Button 
                      type="submit" 
                      className="w-full text-lg py-6 rounded-xl bg-gradient-to-r from-[#0FA0CE] to-[#0FA0CE]/80 hover:from-[#0FA0CE]/90 hover:to-[#0FA0CE]/70 text-white font-semibold shadow-xl shadow-[#0FA0CE]/30 hover:shadow-2xl hover:shadow-[#0FA0CE]/50 transform hover:scale-105 transition-all duration-300" 
                      disabled={loading}
                    >
                      {loading ? (
                        <>
                          <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                          Creating account...
                        </>
                      ) : (
                        <>
                          <Users className="mr-2 h-5 w-5" />
                          Create Account
                        </>
                      )}
                    </Button>
                  </form>
                )}
              </div>
              
              {/* Additional Info */}
              <div className="mt-6 text-center">
                <div className="glassmorphism rounded-xl p-4">
                  <p className="text-sm text-muted-foreground mb-2">
                    Secure authentication with role-based access control
                  </p>
                  <div className="flex items-center justify-center gap-4 text-xs text-muted-foreground">
                    <span>🔒 Encrypted</span>
                    <span>⚡ Fast</span>
                    <span>🛡️ Secure</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
