/**
 * Supabase Realtime Manager
 * Comprehensive real-time connection management based on Supabase best practices
 */

import { supabase } from '@/integrations/supabase/client';
import type { RealtimeChannel, RealtimeChannelSendResponse } from '@supabase/supabase-js';

interface RealtimeConfig {
  heartbeatIntervalMs: number;
  reconnectAfterMs: number[];
  timeout: number;
  transport: string;
  logger?: (kind: string, msg: string, data?: any) => void;
}

interface ChannelConfig {
  name: string;
  config?: {
    broadcast?: { self?: boolean; ack?: boolean };
    presence?: { key?: string };
    postgres_changes?: Array<{
      event: string;
      schema: string;
      table?: string;
      filter?: string;
    }>;
  };
}

export class SupabaseRealtimeManager {
  private static instance: SupabaseRealtimeManager;
  private channels: Map<string, RealtimeChannel> = new Map();
  private connectionState: 'disconnected' | 'connecting' | 'connected' | 'error' = 'disconnected';
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private isInitialized = false;
  private heartbeatInterval?: NodeJS.Timeout;

  // Supabase Realtime configuration based on best practices
  private realtimeConfig: RealtimeConfig = {
    heartbeatIntervalMs: 30000, // 30 seconds
    reconnectAfterMs: [1000, 2000, 5000, 10000], // Progressive backoff
    timeout: 10000, // 10 seconds
    transport: 'websocket',
    logger: (kind: string, msg: string, data?: any) => {
      console.log(`🔌 Realtime [${kind}]:`, msg, data);
    }
  };

  static getInstance(): SupabaseRealtimeManager {
    if (!SupabaseRealtimeManager.instance) {
      SupabaseRealtimeManager.instance = new SupabaseRealtimeManager();
    }
    return SupabaseRealtimeManager.instance;
  }

  /**
   * Initialize Supabase Realtime with proper configuration
   */
  async initialize(): Promise<{ success: boolean; message: string }> {
    if (this.isInitialized) {
      return { success: true, message: 'Already initialized' };
    }

    try {
      console.log('🚀 Initializing Supabase Realtime Manager...');

      // Check authentication first
      const { data: { session }, error: authError } = await supabase.auth.getSession();
      if (authError) {
        console.error('❌ Auth error during realtime init:', authError);
        return { success: false, message: `Auth error: ${authError.message}` };
      }

      if (!session) {
        console.log('ℹ️ No active session, skipping realtime initialization');
        return { success: true, message: 'No session - realtime skipped' };
      }

      // Configure Supabase client for realtime
      this.configureRealtimeClient();

      // Set up connection monitoring
      this.setupConnectionMonitoring();

      // Initialize essential channels
      await this.initializeEssentialChannels();

      this.isInitialized = true;
      this.connectionState = 'connected';
      console.log('✅ Supabase Realtime Manager initialized successfully');

      return { success: true, message: 'Realtime initialized successfully' };
    } catch (error: any) {
      console.error('❌ Realtime initialization failed:', error);
      this.connectionState = 'error';
      return { success: false, message: `Initialization failed: ${error.message}` };
    }
  }

  /**
   * Configure Supabase client for optimal realtime performance
   */
  private configureRealtimeClient(): void {
    // Note: Supabase client configuration is typically done during client creation
    // But we can set up event listeners for connection state changes
    console.log('⚙️ Configuring realtime client settings...');
    
    // Set up global error handling for realtime
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.cleanup();
      });

      // Handle visibility changes (tab switching)
      document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
          console.log('📱 Tab hidden - maintaining connections');
        } else {
          console.log('📱 Tab visible - checking connections');
          this.checkConnectionHealth();
        }
      });
    }
  }

  /**
   * Set up connection monitoring and heartbeat
   */
  private setupConnectionMonitoring(): void {
    console.log('💓 Setting up connection monitoring...');
    
    // Clear existing heartbeat
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    // Set up heartbeat to monitor connection health
    this.heartbeatInterval = setInterval(() => {
      this.checkConnectionHealth();
    }, this.realtimeConfig.heartbeatIntervalMs);
  }

  /**
   * Check connection health and reconnect if needed
   */
  private async checkConnectionHealth(): Promise<void> {
    try {
      // Test connection with a simple query
      const { error } = await supabase
        .from('profiles')
        .select('id')
        .limit(1);

      if (error) {
        console.warn('⚠️ Connection health check failed:', error.message);
        this.handleConnectionError();
      } else {
        // Reset reconnect attempts on successful health check
        this.reconnectAttempts = 0;
        if (this.connectionState !== 'connected') {
          this.connectionState = 'connected';
          console.log('✅ Connection health restored');
        }
      }
    } catch (error) {
      console.warn('⚠️ Health check error:', error);
      this.handleConnectionError();
    }
  }

  /**
   * Initialize essential channels for the application
   */
  private async initializeEssentialChannels(): Promise<void> {
    console.log('📡 Initializing essential channels...');

    const essentialChannels: ChannelConfig[] = [
      {
        name: 'user-presence',
        config: {
          presence: { key: 'user_presence' },
          broadcast: { self: true }
        }
      },
      {
        name: 'notifications',
        config: {
          broadcast: { self: false, ack: true }
        }
      },
      {
        name: 'system-updates',
        config: {
          broadcast: { self: false }
        }
      }
    ];

    for (const channelConfig of essentialChannels) {
      try {
        await this.createChannel(channelConfig);
      } catch (error) {
        console.warn(`⚠️ Failed to create channel ${channelConfig.name}:`, error);
        // Continue with other channels even if one fails
      }
    }
  }

  /**
   * Create and subscribe to a channel
   */
  async createChannel(config: ChannelConfig): Promise<RealtimeChannel | null> {
    try {
      console.log(`📡 Creating channel: ${config.name}`);

      // Remove existing channel if it exists
      if (this.channels.has(config.name)) {
        await this.removeChannel(config.name);
      }

      // Create new channel
      const channel = supabase.channel(config.name, config.config);

      // Set up event handlers
      this.setupChannelEventHandlers(channel, config.name);

      // Subscribe to the channel
      const subscriptionResult = await new Promise<string>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Subscription timeout'));
        }, this.realtimeConfig.timeout);

        channel.subscribe((status) => {
          clearTimeout(timeout);
          console.log(`📡 Channel ${config.name} status:`, status);
          
          if (status === 'SUBSCRIBED') {
            resolve(status);
          } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
            reject(new Error(`Subscription failed: ${status}`));
          }
        });
      });

      if (subscriptionResult === 'SUBSCRIBED') {
        this.channels.set(config.name, channel);
        console.log(`✅ Channel ${config.name} created and subscribed`);
        return channel;
      } else {
        throw new Error(`Unexpected subscription result: ${subscriptionResult}`);
      }
    } catch (error: any) {
      console.error(`❌ Failed to create channel ${config.name}:`, error);
      return null;
    }
  }

  /**
   * Set up event handlers for a channel
   */
  private setupChannelEventHandlers(channel: RealtimeChannel, channelName: string): void {
    // Handle broadcast messages
    channel.on('broadcast', { event: '*' }, (payload) => {
      console.log(`📨 Broadcast received on ${channelName}:`, payload);
      this.handleBroadcastMessage(channelName, payload);
    });

    // Handle presence events
    channel.on('presence', { event: 'sync' }, () => {
      console.log(`👥 Presence sync on ${channelName}`);
      this.handlePresenceSync(channelName, channel);
    });

    channel.on('presence', { event: 'join' }, ({ key, newPresences }) => {
      console.log(`👋 User joined ${channelName}:`, key, newPresences);
    });

    channel.on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
      console.log(`👋 User left ${channelName}:`, key, leftPresences);
    });

    // Handle postgres changes if configured
    channel.on('postgres_changes', { event: '*', schema: 'public' }, (payload) => {
      console.log(`🗄️ Database change on ${channelName}:`, payload);
      this.handleDatabaseChange(channelName, payload);
    });
  }

  /**
   * Handle broadcast messages
   */
  private handleBroadcastMessage(channelName: string, payload: any): void {
    // Emit custom events for the application to listen to
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent(`realtime-broadcast-${channelName}`, {
        detail: payload
      }));
    }
  }

  /**
   * Handle presence sync
   */
  private handlePresenceSync(channelName: string, channel: RealtimeChannel): void {
    const presenceState = channel.presenceState();
    console.log(`👥 Current presence on ${channelName}:`, presenceState);
    
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent(`realtime-presence-${channelName}`, {
        detail: presenceState
      }));
    }
  }

  /**
   * Handle database changes
   */
  private handleDatabaseChange(channelName: string, payload: any): void {
    console.log(`🗄️ Database change on ${channelName}:`, payload);
    
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent(`realtime-db-change-${channelName}`, {
        detail: payload
      }));
    }
  }

  /**
   * Handle connection errors
   */
  private handleConnectionError(): void {
    this.connectionState = 'error';
    this.reconnectAttempts++;

    if (this.reconnectAttempts <= this.maxReconnectAttempts) {
      const delay = this.realtimeConfig.reconnectAfterMs[
        Math.min(this.reconnectAttempts - 1, this.realtimeConfig.reconnectAfterMs.length - 1)
      ];

      console.log(`🔄 Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);
      
      setTimeout(() => {
        this.reconnect();
      }, delay);
    } else {
      console.error('❌ Max reconnection attempts reached, disabling realtime');
      this.disable();
    }
  }

  /**
   * Reconnect to realtime
   */
  private async reconnect(): Promise<void> {
    try {
      console.log('🔄 Reconnecting to realtime...');
      this.connectionState = 'connecting';

      // Clean up existing connections
      await this.cleanup();

      // Reinitialize
      this.isInitialized = false;
      const result = await this.initialize();

      if (result.success) {
        console.log('✅ Reconnection successful');
      } else {
        console.error('❌ Reconnection failed:', result.message);
        this.handleConnectionError();
      }
    } catch (error) {
      console.error('❌ Reconnection error:', error);
      this.handleConnectionError();
    }
  }

  /**
   * Remove a specific channel
   */
  async removeChannel(channelName: string): Promise<void> {
    const channel = this.channels.get(channelName);
    if (channel) {
      try {
        await supabase.removeChannel(channel);
        this.channels.delete(channelName);
        console.log(`🗑️ Channel ${channelName} removed`);
      } catch (error) {
        console.warn(`⚠️ Failed to remove channel ${channelName}:`, error);
      }
    }
  }

  /**
   * Disable realtime features
   */
  async disable(): Promise<void> {
    console.log('🔇 Disabling realtime features...');
    await this.cleanup();
    this.connectionState = 'disconnected';
    localStorage.setItem('realtime-disabled', 'true');
  }

  /**
   * Enable realtime features
   */
  async enable(): Promise<void> {
    console.log('🔊 Enabling realtime features...');
    localStorage.removeItem('realtime-disabled');
    await this.initialize();
  }

  /**
   * Check if realtime is disabled
   */
  isDisabled(): boolean {
    return localStorage.getItem('realtime-disabled') === 'true';
  }

  /**
   * Get connection status
   */
  getStatus(): {
    state: string;
    channelCount: number;
    reconnectAttempts: number;
    isInitialized: boolean;
  } {
    return {
      state: this.connectionState,
      channelCount: this.channels.size,
      reconnectAttempts: this.reconnectAttempts,
      isInitialized: this.isInitialized
    };
  }

  /**
   * Cleanup all connections
   */
  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up realtime connections...');

    // Clear heartbeat
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = undefined;
    }

    // Remove all channels
    const channelNames = Array.from(this.channels.keys());
    for (const channelName of channelNames) {
      await this.removeChannel(channelName);
    }

    this.channels.clear();
    this.isInitialized = false;
    console.log('✅ Realtime cleanup completed');
  }
}

// Global instance
export const supabaseRealtimeManager = SupabaseRealtimeManager.getInstance();

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).supabaseRealtimeManager = supabaseRealtimeManager;
  (window as any).initRealtime = () => supabaseRealtimeManager.initialize();
  (window as any).getRealtimeStatus = () => supabaseRealtimeManager.getStatus();
  (window as any).disableRealtime = () => supabaseRealtimeManager.disable();
  (window as any).enableRealtime = () => supabaseRealtimeManager.enable();
  
  console.log('🔌 Supabase Realtime Manager loaded. Available commands:');
  console.log('  - initRealtime()');
  console.log('  - getRealtimeStatus()');
  console.log('  - disableRealtime()');
  console.log('  - enableRealtime()');
}
