
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { Timer } from "lucide-react";
import { useAuth } from "@/components/auth/AuthProvider";
import { supabase } from "@/integrations/supabase/client";
import { showActionToast, showInfoToast } from "@/utils/comprehensive-toast-system";

interface LocationData {
  latitude: number;
  longitude: number;
  address?: string;
}

export const ClockInButton = () => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();

  const getCurrentLocation = (): Promise<LocationData> => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported by this browser'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const { latitude, longitude } = position.coords;

          // Try to get address from coordinates using a CORS-friendly service
          try {
            const response = await fetch(
              `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${latitude}&longitude=${longitude}&localityLanguage=en`
            );

            if (response.ok) {
              const data = await response.json();
              const address = data.locality || data.city || data.principalSubdivision ||
                            `${latitude.toFixed(4)}, ${longitude.toFixed(4)}`;
              resolve({ latitude, longitude, address });
            } else {
              // Fallback to coordinates if geocoding fails
              resolve({
                latitude,
                longitude,
                address: `${latitude.toFixed(4)}, ${longitude.toFixed(4)}`
              });
            }
          } catch (error) {
            console.warn('Geocoding failed:', error);
            // Fallback to coordinates if geocoding fails
            resolve({
              latitude,
              longitude,
              address: `${latitude.toFixed(4)}, ${longitude.toFixed(4)}`
            });
          }
        },
        (error) => {
          reject(new Error(`Location error: ${error.message}`));
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000
        }
      );
    });
  };

  const performClockIn = async () => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please log in first to clock in.",
        variant: "destructive",
      });
      return;
    }

    try {
      const location = await getCurrentLocation();

      const { data, error } = await supabase
        .from('time_logs')
        .insert({
          user_id: user.id,
          clock_in: new Date().toISOString(),
          clock_in_timestamp: new Date().toISOString(),
          latitude: location.latitude,
          longitude: location.longitude,
          location_address: location.address,
          date: new Date().toISOString().split('T')[0],
          clock_in_method: 'manual',
          activity_type: 'work',
          status: 'active'
        })
        .select()
        .single();

      if (error) throw error;

      showActionToast('clock-in', true, `Location: ${location.address}`);

      // Navigate to dashboard after successful clock-in
      navigate('/dashboard');

      return data;
    } catch (error: any) {
      console.error('❌ Clock in failed:', error);
      showActionToast('clock-in', false, undefined, error);
      throw error;
    }
  };

  const handleClockIn = async () => {
    setLoading(true);
    try {
      if (isAuthenticated && user) {
        // User is logged in, perform actual clock-in
        await performClockIn();
      } else {
        // User is not logged in, redirect to auth page
        showInfoToast({
          title: "Login Required",
          description: "Please log in to clock in.",
          duration: 3000
        });
        navigate("/auth");
      }
    } catch (error) {
      console.error("Clock in error:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="relative">
      <div className="loader">
        {[1, 2, 3, 4, 5].map((index) => (
          <div
            key={index}
            className="box"
            style={{
              '--size': '250px',
              '--duration': '3s',
              '--background': `linear-gradient(0deg, rgba(255, 28, 4, ${(6-index) * 0.05}) 0%, rgba(255, 28, 4, ${(6-index) * 0.1}) 100%)`,
              inset: `${(index - 1) * 10}%`,
              zIndex: 99 - index,
              borderColor: `rgba(255, 28, 4, ${1 - (index - 1) * 0.2})`,
              animationDelay: `${(index - 1) * 0.2}s`,
              position: 'absolute',
              borderRadius: '50%',
              borderTop: '1px solid',
              boxShadow: 'rgba(255, 28, 4, 0.3) 0px 10px 10px -0px',
              backdropFilter: 'blur(5px)',
              animation: 'ripple var(--duration) infinite ease-in-out'
            } as React.CSSProperties}
          />
        ))}
        <div className="logo absolute inset-0 grid place-content-center" style={{ padding: '25%', zIndex: 100 }}>
          <div className="relative w-32 h-32 rounded-full bg-white p-2 shadow-lg">
            <img 
              src="/lovable-uploads/491c7e61-a4fb-46a3-a002-904b84354e48.png"
              alt="CT Communication Towers Logo"
              className="w-20 h-20 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
            />
            <Button
              onClick={handleClockIn}
              disabled={loading}
              className="w-full h-full rounded-full 
                bg-gradient-to-br from-[#ff1c04] via-[#ff1c04]/90 to-[#ff1c04]/80
                hover:bg-[#ff1c04]/90 text-white
                shadow-[0_0_30px_rgba(255,28,4,0.3)]
                hover:shadow-[0_0_35px_rgba(255,28,4,0.4)]
                active:shadow-[0_0_25px_rgba(255,28,4,0.3)]
                active:transform active:scale-95
                transition-all duration-300 ease-in-out
                border-2 border-[#ff1c04]/20
                backdrop-blur-sm
                relative
                flex flex-col items-center justify-center gap-2
                group
                z-10"
            >
              <Timer className="h-8 w-8 group-hover:scale-110 transition-transform duration-300" />
              <span className="font-semibold tracking-wide text-sm">
                {loading ? "Processing..." : "Clock In"}
              </span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
