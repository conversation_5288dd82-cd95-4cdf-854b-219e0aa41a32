import { DatabaseTest } from '@/components/debug/DatabaseTest'
import { FrontendComponentTest } from '@/components/debug/FrontendComponentTest'
import { AuthDebug } from '@/components/debug/AuthDebug'
import { EmergencySignIn } from '@/components/debug/EmergencySignIn'
import { LoginDebugger } from '@/components/debug/LoginDebugger'
import { SimpleLoginTest } from '@/components/debug/SimpleLoginTest'
import { DatabaseConnectionTest } from '@/components/debug/DatabaseConnectionTest'
import { TasksDebug } from '@/components/debug/TasksDebug'
import { TasksForeignKeyFix } from '@/components/debug/TasksForeignKeyFix';
import { ComprehensiveForeignKeyFix } from '@/components/debug/ComprehensiveForeignKeyFix';
import { NetworkConnectivityFix } from '@/components/debug/NetworkConnectivityFix';
import { TimeLogsSchemaFix } from '@/components/debug/TimeLogsSchemaFix';
import { EdgeFunctionsFix } from '@/components/debug/EdgeFunctionsFix';
import { DatabaseAccessDebug } from '@/components/debug/DatabaseAccessDebug'
import { JWTDebugPanel } from '@/components/debug/JWTDebugPanel'
import { WorldMapTest } from '@/components/debug/WorldMapTest'
import { TokenExpiryWarning } from '@/components/auth/TokenExpiryWarning'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useAuth } from '@/components/auth/AuthProvider'

export const DebugPage = () => {
  const { isAuthenticated } = useAuth()

  return (
    <div className='container mx-auto py-8'>
      <h1 className='text-3xl font-bold mb-8'>System Debug & Testing</h1>

      {/* Token Expiry Warning */}
      {isAuthenticated && (
        <TokenExpiryWarning showAlways className='mb-6' />
      )}

      <Tabs defaultValue='jwt' className='space-y-6'>
        <TabsList className='grid w-full grid-cols-8'>
          <TabsTrigger value='jwt'>JWT Debug</TabsTrigger>
          <TabsTrigger value='login'>Login Debug</TabsTrigger>
          <TabsTrigger value='emergency'>Emergency Sign-In</TabsTrigger>
          <TabsTrigger value='auth'>Authentication Debug</TabsTrigger>
          <TabsTrigger value='tasks'>Tasks Debug</TabsTrigger>
          <TabsTrigger value='access'>Database Access</TabsTrigger>
          <TabsTrigger value='database'>Database & API Testing</TabsTrigger>
          <TabsTrigger value='frontend'>Frontend Component Testing</TabsTrigger>
        </TabsList>

        <TabsContent value='jwt'>
          <JWTDebugPanel />
        </TabsContent>

        <TabsContent value='login'>
          <div className='space-y-6'>
            <LoginDebugger />
            <SimpleLoginTest />
            <DatabaseConnectionTest />
          </div>
        </TabsContent>

        <TabsContent value='emergency'>
          <EmergencySignIn />
        </TabsContent>

        <TabsContent value='auth'>
          <AuthDebug />
        </TabsContent>

        <TabsContent value='tasks'>
          <div className="space-y-4">
              <NetworkConnectivityFix />
              <TimeLogsSchemaFix />
              <EdgeFunctionsFix />
              <ComprehensiveForeignKeyFix />
              <TasksForeignKeyFix />
              <TasksDebug />
            </div>
        </TabsContent>

        <TabsContent value='access'>
          <DatabaseAccessDebug />
        </TabsContent>

        <TabsContent value='database'>
          <DatabaseTest />
        </TabsContent>

        <TabsContent value='frontend'>
          <div className='space-y-6'>
            <FrontendComponentTest />
            <WorldMapTest />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
