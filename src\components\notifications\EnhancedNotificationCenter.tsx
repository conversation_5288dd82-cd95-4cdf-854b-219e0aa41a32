import { useState, useEffect } from "react";
import { Bell, Mail, Video, Check, AlertCircle, Loader2, FileText, CheckCircle, XCircle } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";

interface Notification {
  id: string;
  title: string;
  message: string;
  type: "memo_sent" | "memo_approved" | "memo_rejected" | "task_assigned" | "system" | "meeting" | "success" | "warning" | "info";
  time: string;
  read: boolean;
  metadata?: {
    memo_id?: string;
    task_id?: string;
    project_id?: string;
    from_user?: string;
    to_user?: string;
  };
  action?: {
    label: string;
    onClick: () => void;
  };
}

export const EnhancedNotificationCenter = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [unreadCount, setUnreadCount] = useState(0);
  const { toast } = useToast();
  const { userProfile } = useAuth();

  const fetchNotifications = async () => {
    if (!userProfile?.id) return;
    
    setIsLoading(true);
    setProgress(0);

    try {
      setProgress(25);

      // Fetch memo notifications for the current user
      const { data: memoNotifications, error: memoError } = await supabase
        .from('notifications')
        .select(`
          id,
          title,
          message,
          type,
          read,
          metadata,
          created_at,
          user:user_id (
            id,
            email
          )
        `)
        .eq('user_id', userProfile.id)
        .in('type', ['memo_published', 'memo_sent', 'memo_approved', 'memo_rejected'])
        .order('created_at', { ascending: false })
        .limit(15);

      setProgress(50);

      // Fetch system activities for notifications
      const { data: activities, error: activitiesError } = await supabase
        .from('system_activities')
        .select('*')
        .or(`user_id.eq.${userProfile.id},description.ilike.%${userProfile.full_name}%`)
        .order('created_at', { ascending: false })
        .limit(10);

      setProgress(75);

      // Fetch task assignments for the user
      const { data: taskNotifications, error: taskError } = await supabase
        .from('tasks')
        .select(`
          id,
          title,
          status,
          priority,
          created_at,
          assigned_to_id,
          created_by:created_by_id (
            id,
            full_name
          )
        `)
        .eq('assigned_to_id', userProfile.id)
        .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()) // Last 7 days
        .order('created_at', { ascending: false })
        .limit(10);

      setProgress(90);

      if (memoError) console.error('Error fetching memo notifications:', memoError);
      if (activitiesError) console.error('Error fetching activities:', activitiesError);
      if (taskError) console.error('Error fetching task notifications:', taskError);

      // Transform memo notifications
      const memoNotifs: Notification[] = (memoNotifications || []).map(notification => ({
        id: notification.id,
        title: getMemoNotificationTitle(notification.notification_type, notification.memo?.title || notification.memo?.subject),
        message: getMemoNotificationMessage(notification.notification_type, notification.from_user?.full_name),
        type: notification.notification_type as Notification['type'],
        time: new Date(notification.created_at).toLocaleString(),
        read: notification.read_at !== null,
        metadata: {
          memo_id: notification.memo_id,
          from_user: notification.from_user?.full_name,
          to_user: notification.to_user?.full_name
        }
      }));

      // Transform system activities
      const systemNotifications: Notification[] = (activities || []).map(activity => ({
        id: activity.id,
        title: "System Activity",
        message: activity.description,
        type: activity.severity === 'error' ? 'warning' : 'system',
        time: new Date(activity.created_at).toLocaleString(),
        read: false, // System notifications are always shown as new
      }));

      // Transform task notifications
      const taskNotifs: Notification[] = (taskNotifications || []).map(task => ({
        id: `task-${task.id}`,
        title: "New Task Assigned",
        message: `"${task.title}" assigned by ${task.created_by?.full_name || 'System'}`,
        type: 'task_assigned',
        time: new Date(task.created_at).toLocaleString(),
        read: false, // Task assignments are always shown as new
        metadata: {
          task_id: task.id,
          from_user: task.created_by?.full_name
        }
      }));

      const allNotifications = [...memoNotifs, ...systemNotifications, ...taskNotifs]
        .sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime());

      setNotifications(allNotifications);
      setUnreadCount(allNotifications.filter(n => !n.read).length);
      setProgress(100);
      
      setTimeout(() => {
        setIsLoading(false);
        setProgress(0);
      }, 500);

    } catch (error) {
      console.error('Error fetching notifications:', error);
      setIsLoading(false);
      setProgress(0);
      toast({
        title: "Error",
        description: "Failed to load notifications",
        variant: "destructive",
      });
    }
  };

  const getMemoNotificationTitle = (type: string, memoTitle?: string) => {
    switch (type) {
      case 'memo_sent':
        return `Memo Sent: ${memoTitle || 'Untitled Memo'}`;
      case 'memo_approved':
        return `Memo Approved: ${memoTitle || 'Untitled Memo'}`;
      case 'memo_rejected':
        return `Memo Rejected: ${memoTitle || 'Untitled Memo'}`;
      default:
        return 'Memo Notification';
    }
  };

  const getMemoNotificationMessage = (type: string, fromUser?: string) => {
    switch (type) {
      case 'memo_sent':
        return `Your memo has been successfully sent and is pending review.`;
      case 'memo_approved':
        return `Your memo has been approved by ${fromUser || 'management'}.`;
      case 'memo_rejected':
        return `Your memo has been rejected by ${fromUser || 'management'}. Please review and resubmit if necessary.`;
      default:
        return 'Memo status update';
    }
  };

  useEffect(() => {
    fetchNotifications();
    
    // Set up real-time subscription for new notifications
    const channel = supabase
      .channel('user-notifications')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userProfile?.id}`
        },
        () => {
          fetchNotifications();
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'system_activities'
        },
        () => {
          fetchNotifications();
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'tasks',
          filter: `assigned_to_id=eq.${userProfile?.id}`
        },
        () => {
          fetchNotifications();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [userProfile?.id]);

  const markAsRead = async (id: string, type: string) => {
    // Update local state immediately
    setNotifications(prev =>
      prev.map(n => (n.id === id ? { ...n, read: true } : n))
    );
    setUnreadCount(prev => Math.max(0, prev - 1));

    // Update database for memo notifications
    if (type.startsWith('memo_')) {
      try {
        const { error } = await supabase
          .from('notifications')
          .update({ 
            read: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', id);

        if (error) {
          console.error('Error marking notification as read:', error);
        }
      } catch (error) {
        console.error('Error updating read status:', error);
      }
    }
  };

  const markAllAsRead = async () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
    setUnreadCount(0);

    try {
      // Mark all memo notifications as read
      const { error } = await supabase
        .from('notifications')
        .update({ read: true, updated_at: new Date().toISOString() })
        .eq('user_id', userProfile?.id)
        .eq('read', false)
        .in('type', ['memo_published', 'memo_sent', 'memo_approved', 'memo_rejected']);

      if (error) {
        console.error('Error marking all notifications as read:', error);
      }
    } catch (error) {
      console.error('Error updating read status:', error);
    }
  };

  const getNotificationIcon = (type: Notification["type"]) => {
    switch (type) {
      case "memo_sent":
        return <Mail className="h-5 w-5 text-blue-500" />;
      case "memo_approved":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "memo_rejected":
        return <XCircle className="h-5 w-5 text-red-500" />;
      case "task_assigned":
        return <FileText className="h-5 w-5 text-orange-500" />;
      case "meeting":
        return <Video className="h-5 w-5 text-blue-500" />;
      case "success":
        return <Check className="h-5 w-5 text-green-500" />;
      case "warning":
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      default:
        return <Bell className="h-5 w-5 text-gray-500" />;
    }
  };

  const getNotificationColor = (type: Notification["type"]) => {
    switch (type) {
      case "memo_sent":
        return "border-l-blue-500 bg-blue-50/50 dark:bg-blue-950/50";
      case "memo_approved":
        return "border-l-green-500 bg-green-50/50 dark:bg-green-950/50";
      case "memo_rejected":
        return "border-l-red-500 bg-red-50/50 dark:bg-red-950/50";
      case "task_assigned":
        return "border-l-orange-500 bg-orange-50/50 dark:bg-orange-950/50";
      case "meeting":
        return "border-l-blue-500 bg-blue-50/50 dark:bg-blue-950/50";
      case "success":
        return "border-l-green-500 bg-green-50/50 dark:bg-green-950/50";
      case "warning":
        return "border-l-yellow-500 bg-yellow-50/50 dark:bg-yellow-950/50";
      default:
        return "border-l-gray-500 bg-gray-50/50 dark:bg-gray-950/50";
    }
  };

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative hover:bg-background/10"
          onClick={fetchNotifications}
        >
          {isLoading ? (
            <Loader2 className="h-5 w-5 animate-spin" />
          ) : (
            <>
              <Bell className="h-5 w-5" />
              {unreadCount > 0 && (
                <Badge 
                  className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs bg-destructive text-destructive-foreground"
                >
                  {unreadCount > 9 ? '9+' : unreadCount}
                </Badge>
              )}
            </>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent className="w-full sm:w-[420px] overflow-hidden">
        <SheetHeader className="space-y-2">
          <div className="flex items-center justify-between">
            <SheetTitle className="text-xl font-bold flex items-center gap-2">
              <Bell className="h-5 w-5 text-primary" />
              Notifications
            </SheetTitle>
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={markAllAsRead}
                className="text-xs"
              >
                Mark all read
              </Button>
            )}
          </div>
          {isLoading && (
            <div className="space-y-2">
              <Progress value={progress} className="w-full h-2" />
              <p className="text-sm text-muted-foreground">Loading notifications...</p>
            </div>
          )}
        </SheetHeader>
        <ScrollArea className="h-[calc(100vh-8rem)] mt-4 pr-4">
          <div className="space-y-4">
            {notifications.length === 0 && !isLoading ? (
              <Card className="p-4 text-center">
                <Bell className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">No notifications found</p>
                <p className="text-xs text-muted-foreground mt-1">
                  You'll see memo updates, task assignments, and system alerts here
                </p>
              </Card>
            ) : (
              notifications.map((notification) => (
                <Card
                  key={notification.id}
                  className={`p-4 transition-all hover:shadow-md cursor-pointer border-l-4 ${
                    notification.read
                      ? "opacity-70 border-l-muted"
                      : getNotificationColor(notification.type)
                  }`}
                  onClick={() => markAsRead(notification.id, notification.type)}
                >
                  <div className="flex items-start gap-3">
                    {getNotificationIcon(notification.type)}
                    <div className="flex-1 space-y-1">
                      <p className="font-medium text-sm">{notification.title}</p>
                      <p className="text-sm text-muted-foreground">
                        {notification.message}
                      </p>
                      {notification.metadata && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {notification.metadata.from_user && (
                            <Badge variant="outline" className="text-xs">
                              From: {notification.metadata.from_user}
                            </Badge>
                          )}
                          {notification.type.startsWith('memo_') && (
                            <Badge variant="secondary" className="text-xs">
                              Memo
                            </Badge>
                          )}
                          {notification.type === 'task_assigned' && (
                            <Badge variant="secondary" className="text-xs">
                              Task
                            </Badge>
                          )}
                        </div>
                      )}
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-xs text-muted-foreground">
                          {notification.time}
                        </span>
                        {!notification.read && (
                          <div className="h-2 w-2 bg-primary rounded-full"></div>
                        )}
                        {notification.action && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              notification.action?.onClick();
                            }}
                          >
                            {notification.action.label}
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </Card>
              ))
            )}
          </div>
        </ScrollArea>
      </SheetContent>
    </Sheet>
  );
};
