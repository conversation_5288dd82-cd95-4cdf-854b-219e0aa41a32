/**
 * Lang<PERSON>hain Memory Management
 * Handles conversation history, context, and user-specific memory
 */

import { supabase } from '@/integrations/supabase/client';
import { langChainConfig } from './config';

export interface ConversationMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface ConversationSession {
  id: string;
  userId: string;
  title?: string;
  messages: ConversationMessage[];
  context: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface MemoryContext {
  userId: string;
  sessionId: string;
  userProfile?: any;
  organizationContext?: any;
  recentActivities?: any[];
}

export class LangChainMemoryService {
  private static sessions = new Map<string, ConversationSession>();
  private static userContexts = new Map<string, MemoryContext>();

  /**
   * Create a new conversation session
   */
  static async createSession(userId: string, title?: string): Promise<string> {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
    
    const session: ConversationSession = {
      id: sessionId,
      userId,
      title: title || `Conversation ${new Date().toLocaleDateString()}`,
      messages: [],
      context: {},
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Save to database
    const { error } = await supabase
      .from('langchain_conversations')
      .insert({
        id: sessionId,
        user_id: userId,
        title: session.title,
        messages: [],
        context: session.context,
      });

    if (error) {
      console.error('Failed to create conversation session:', error);
      throw error;
    }

    this.sessions.set(sessionId, session);
    return sessionId;
  }

  /**
   * Add message to conversation
   */
  static async addMessage(
    sessionId: string,
    role: 'user' | 'assistant' | 'system',
    content: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }

    const message: ConversationMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`,
      role,
      content,
      timestamp: new Date(),
      metadata,
    };

    session.messages.push(message);
    session.updatedAt = new Date();

    // Keep only recent messages based on config
    const config = langChainConfig.getConfig();
    if (session.messages.length > config.memory.returnMessages) {
      session.messages = session.messages.slice(-config.memory.returnMessages);
    }

    // Update database
    await this.saveSession(session);
  }

  /**
   * Get conversation history
   */
  static async getConversationHistory(
    sessionId: string,
    limit?: number
  ): Promise<ConversationMessage[]> {
    let session = this.sessions.get(sessionId);
    
    if (!session) {
      // Load from database
      session = await this.loadSession(sessionId);
    }

    if (!session) {
      return [];
    }

    const messages = session.messages;
    return limit ? messages.slice(-limit) : messages;
  }

  /**
   * Get user context for enhanced responses
   */
  static async getUserContext(userId: string): Promise<MemoryContext> {
    let context = this.userContexts.get(userId);
    
    if (!context) {
      context = await this.buildUserContext(userId);
      this.userContexts.set(userId, context);
    }

    return context;
  }

  /**
   * Build comprehensive user context
   */
  private static async buildUserContext(userId: string): Promise<MemoryContext> {
    try {
      // Get user profile
      const { data: userProfile } = await supabase
        .from('profiles')
        .select('*, department:departments(name)')
        .eq('id', userId)
        .single();

      // Get recent activities
      const { data: recentActivities } = await supabase
        .from('system_activities')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(10);

      // Get organization context
      const { data: orgStats } = await supabase
        .from('profiles')
        .select('role, department_id')
        .limit(100);

      const organizationContext = {
        totalUsers: orgStats?.length || 0,
        departments: [...new Set(orgStats?.map(u => u.department_id).filter(Boolean))].length,
        userRole: userProfile?.role,
        userDepartment: userProfile?.department?.name,
      };

      return {
        userId,
        sessionId: '', // Will be set when used
        userProfile,
        organizationContext,
        recentActivities: recentActivities || [],
      };
    } catch (error) {
      console.error('Failed to build user context:', error);
      return {
        userId,
        sessionId: '',
      };
    }
  }

  /**
   * Load session from database
   */
  private static async loadSession(sessionId: string): Promise<ConversationSession | null> {
    try {
      const { data, error } = await supabase
        .from('langchain_conversations')
        .select('*')
        .eq('id', sessionId)
        .single();

      if (error || !data) {
        return null;
      }

      const session: ConversationSession = {
        id: data.id,
        userId: data.user_id,
        title: data.title,
        messages: data.messages || [],
        context: data.context || {},
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
      };

      this.sessions.set(sessionId, session);
      return session;
    } catch (error) {
      console.error('Failed to load session:', error);
      return null;
    }
  }

  /**
   * Save session to database
   */
  private static async saveSession(session: ConversationSession): Promise<void> {
    try {
      const { error } = await supabase
        .from('langchain_conversations')
        .upsert({
          id: session.id,
          user_id: session.userId,
          title: session.title,
          messages: session.messages,
          context: session.context,
          updated_at: session.updatedAt.toISOString(),
        });

      if (error) {
        console.error('Failed to save session:', error);
      }
    } catch (error) {
      console.error('Failed to save session:', error);
    }
  }

  /**
   * Clear old sessions
   */
  static async clearOldSessions(daysOld: number = 30): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    try {
      const { error } = await supabase
        .from('langchain_conversations')
        .delete()
        .lt('updated_at', cutoffDate.toISOString());

      if (error) {
        console.error('Failed to clear old sessions:', error);
      }
    } catch (error) {
      console.error('Failed to clear old sessions:', error);
    }
  }

  /**
   * Get user's conversation sessions
   */
  static async getUserSessions(userId: string): Promise<ConversationSession[]> {
    try {
      const { data, error } = await supabase
        .from('langchain_conversations')
        .select('id, title, created_at, updated_at')
        .eq('user_id', userId)
        .order('updated_at', { ascending: false })
        .limit(20);

      if (error) {
        console.error('Failed to get user sessions:', error);
        return [];
      }

      return data.map(session => ({
        id: session.id,
        userId,
        title: session.title,
        messages: [], // Don't load messages for list view
        context: {},
        createdAt: new Date(session.created_at),
        updatedAt: new Date(session.updated_at),
      }));
    } catch (error) {
      console.error('Failed to get user sessions:', error);
      return [];
    }
  }
}

export const langChainMemory = LangChainMemoryService;
