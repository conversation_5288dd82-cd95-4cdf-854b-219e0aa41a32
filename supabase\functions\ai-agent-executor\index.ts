// supabase/functions/ai-agent-executor/index.ts

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { withCors, createCorsResponse } from "../_shared/cors.ts";
import { aiWorkboardAgent } from "../lib/Langchain/agent.ts";

serve(
  withCors(async (req: Request): Promise<Response> => {
    if (req.method !== "POST") {
      return createCorsResponse({ error: "Only POST method allowed" }, 405);
    }

    let body: { input?: string };

    try {
      body = await req.json();
    } catch (err) {
      return createCorsResponse({ error: "Invalid JSON body" }, 400);
    }

    const input = body.input?.trim();
    if (!input) {
      return createCorsResponse({ error: 'Missing "input" field' }, 400);
    }

    try {
      // Initialize agent if not already
      if (!aiWorkboardAgent.agent) {
        await aiWorkboardAgent.initialize();
      }

      const result = await aiWorkboardAgent.execute(input);
      return createCorsResponse({ result });
    } catch (err) {
      console.error("Agent execution failed:", err);
      return createCorsResponse(
        {
          error: `Agent execution failed: ${err instanceof Error ? err.message : String(err)}`,
        },
        500
      );
    }
  })
);
