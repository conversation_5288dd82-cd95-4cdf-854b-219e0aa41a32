/**
 * Module Analyzer
 * Comprehensive analysis of all modules, components, and services
 */

import { supabase } from '@/integrations/supabase/client';

export interface ModuleAnalysisResult {
  module: string;
  type: 'component' | 'service' | 'utility' | 'hook' | 'integration';
  status: 'working' | 'error' | 'missing' | 'incomplete';
  issues: string[];
  dependencies: string[];
  exports: string[];
  imports: string[];
  coverage: number; // 0-100%
}

export interface SystemModuleReport {
  totalModules: number;
  workingModules: number;
  errorModules: number;
  missingModules: number;
  incompleteModules: number;
  overallHealth: number;
  criticalIssues: string[];
  recommendations: string[];
}

export class ModuleAnalyzer {
  private static results: ModuleAnalysisResult[] = [];

  /**
   * Run comprehensive module analysis
   */
  static async runFullAnalysis(): Promise<ModuleAnalysisResult[]> {
    this.results = [];
    console.log('🔍 Running comprehensive module analysis...');

    await this.analyzeComponents();
    await this.analyzeServices();
    await this.analyzeUtilities();
    await this.analyzeHooks();
    await this.analyzeIntegrations();

    console.log(`✅ Module analysis complete. Analyzed ${this.results.length} modules.`);
    return this.results;
  }

  /**
   * Analyze React components
   */
  private static async analyzeComponents(): Promise<void> {
    const components = [
      // Core UI Components
      { name: 'EnhancedAppSidebar', path: 'src/components/navigation/EnhancedAppSidebar.tsx' },
      { name: 'ModernDashboardGrid', path: 'src/components/dashboard/ModernDashboardGrid.tsx' },
      { name: 'ModernDocumentCard', path: 'src/components/documents/ModernDocumentCard.tsx' },
      
      // AI Components
      { name: 'AIDocumentAnalyzer', path: 'src/components/ai/AIDocumentAnalyzer.tsx' },
      { name: 'ModernAIInterface', path: 'src/components/ai/ModernAIInterface.tsx' },
      { name: 'ProjectManagerAssistant', path: 'src/components/project/ProjectManagerAssistant.tsx' },
      
      // Business Logic Components
      { name: 'TaskManagement', path: 'src/components/tasks/TaskManagement.tsx' },
      { name: 'ProjectManagement', path: 'src/components/projects/ProjectManagement.tsx' },
      { name: 'InvoiceManagement', path: 'src/components/accountant/InvoiceManagement.tsx' },
      
      // Layout Components
      { name: 'UnifiedDashboardLayout', path: 'src/components/layout/UnifiedDashboardLayout.tsx' },
      { name: 'EnhancedAppLayout', path: 'src/components/layout/EnhancedAppLayout.tsx' },
      
      // Error Handling
      { name: 'ErrorBoundary', path: 'src/components/ErrorBoundary.tsx' },
      { name: 'EnhancedErrorBoundary', path: 'src/components/EnhancedErrorBoundary.tsx' }
    ];

    for (const component of components) {
      await this.analyzeModule(component.name, 'component', component.path);
    }
  }

  /**
   * Analyze services
   */
  private static async analyzeServices(): Promise<void> {
    const services = [
      { name: 'TaskAssignmentService', path: 'src/services/task-assignment-service.ts' },
      { name: 'ComprehensiveAPI', path: 'src/services/comprehensive-api.ts' },
      { name: 'UserActivitiesService', path: 'src/services/user-activities-service.ts' },
      { name: 'SystemLogsService', path: 'src/services/system-logs-service.ts' },
      { name: 'AIService', path: 'src/lib/ai/ai-service.ts' },
      { name: 'ProjectAPI', path: 'src/lib/project-api.ts' },
      { name: 'API', path: 'src/lib/api.ts' }
    ];

    for (const service of services) {
      await this.analyzeModule(service.name, 'service', service.path);
    }
  }

  /**
   * Analyze utilities
   */
  private static async analyzeUtilities(): Promise<void> {
    const utilities = [
      { name: 'DatabaseFixUtility', path: 'src/utils/database-fix-utility.ts' },
      { name: 'TaskProjectFixUtility', path: 'src/utils/task-project-fix.ts' },
      { name: 'SystemAnalysisUtility', path: 'src/utils/system-analysis.ts' },
      { name: 'EnhancedDatabaseAnalyzer', path: 'src/utils/enhanced-database-analyzer.ts' },
      { name: 'SystemHealthChecker', path: 'src/utils/system-health-check.ts' },
      { name: 'ErrorHandler', path: 'src/lib/error-handler.ts' },
      { name: 'Logger', path: 'src/lib/logger.ts' }
    ];

    for (const utility of utilities) {
      await this.analyzeModule(utility.name, 'utility', utility.path);
    }
  }

  /**
   * Analyze hooks
   */
  private static async analyzeHooks(): Promise<void> {
    const hooks = [
      { name: 'useTasks', path: 'src/hooks/useTasks.ts' },
      { name: 'useProjects', path: 'src/hooks/useProjects.ts' },
      { name: 'useProjectManagerAgent', path: 'src/hooks/useProjectManagerAgent.ts' },
      { name: 'useAuth', path: 'src/components/auth/AuthProvider.tsx' },
      { name: 'useToast', path: 'src/hooks/use-toast.ts' }
    ];

    for (const hook of hooks) {
      await this.analyzeModule(hook.name, 'hook', hook.path);
    }
  }

  /**
   * Analyze integrations
   */
  private static async analyzeIntegrations(): Promise<void> {
    const integrations = [
      { name: 'SupabaseClient', path: 'src/integrations/supabase/client.ts' },
      { name: 'LangChainRealtimeProvider', path: 'src/components/integration/LangChainRealtimeProvider.tsx' },
      { name: 'BMDDatabaseClient', path: 'src/integrations/bmd-database/client.ts' }
    ];

    for (const integration of integrations) {
      await this.analyzeModule(integration.name, 'integration', integration.path);
    }
  }

  /**
   * Analyze individual module
   */
  private static async analyzeModule(
    name: string, 
    type: ModuleAnalysisResult['type'], 
    path: string
  ): Promise<void> {
    const result: ModuleAnalysisResult = {
      module: name,
      type,
      status: 'working',
      issues: [],
      dependencies: [],
      exports: [],
      imports: [],
      coverage: 100
    };

    try {
      // Test if module can be imported/accessed
      if (type === 'component') {
        await this.testComponent(name, result);
      } else if (type === 'service') {
        await this.testService(name, result);
      } else if (type === 'utility') {
        await this.testUtility(name, result);
      } else if (type === 'hook') {
        await this.testHook(name, result);
      } else if (type === 'integration') {
        await this.testIntegration(name, result);
      }

      // Check for common issues
      await this.checkCommonIssues(name, result);

    } catch (error: any) {
      result.status = 'error';
      result.issues.push(`Module analysis failed: ${error.message}`);
      result.coverage = 0;
    }

    this.results.push(result);
  }

  /**
   * Test component functionality
   */
  private static async testComponent(name: string, result: ModuleAnalysisResult): Promise<void> {
    // Check if component is rendered in DOM
    const element = document.querySelector(`[data-component="${name}"]`);
    
    if (!element) {
      // Check if component exists in common containers
      const commonSelectors = [
        `[class*="${name}"]`,
        `[data-testid*="${name.toLowerCase()}"]`,
        `.${name.toLowerCase()}`,
        `#${name.toLowerCase()}`
      ];

      let found = false;
      for (const selector of commonSelectors) {
        if (document.querySelector(selector)) {
          found = true;
          break;
        }
      }

      if (!found) {
        result.issues.push('Component not found in DOM');
        result.coverage -= 30;
      }
    }

    // Check for React-specific issues
    const reactErrors = this.checkReactErrors(name);
    if (reactErrors.length > 0) {
      result.issues.push(...reactErrors);
      result.coverage -= 20;
    }
  }

  /**
   * Test service functionality
   */
  private static async testService(name: string, result: ModuleAnalysisResult): Promise<void> {
    // Test if service methods are accessible
    try {
      if (name === 'TaskAssignmentService') {
        // Test basic service availability
        result.exports.push('createTask', 'updateTask', 'assignTask');
      } else if (name === 'ComprehensiveAPI') {
        result.exports.push('createProject', 'getCurrentUserProfile');
      } else if (name === 'AIService') {
        // Test AI service
        try {
          const { data, error } = await supabase.functions.invoke('ai-assistant', {
            body: { message: 'test', context: { task: 'health_check' } }
          });
          
          if (error) {
            result.issues.push(`AI service error: ${error.message}`);
            result.coverage -= 50;
          }
        } catch (error: any) {
          result.issues.push(`AI service test failed: ${error.message}`);
          result.coverage -= 50;
        }
      }
    } catch (error: any) {
      result.issues.push(`Service test failed: ${error.message}`);
      result.coverage -= 40;
    }
  }

  /**
   * Test utility functionality
   */
  private static async testUtility(name: string, result: ModuleAnalysisResult): Promise<void> {
    try {
      if (name === 'DatabaseFixUtility') {
        // Test database utility
        if (typeof (window as any).DatabaseFixUtility !== 'undefined') {
          result.exports.push('runHealthCheck', 'autoFixIssues', 'getSafeQuery');
        } else {
          result.issues.push('DatabaseFixUtility not available globally');
          result.coverage -= 30;
        }
      } else if (name === 'SystemAnalysisUtility') {
        if (typeof (window as any).SystemAnalysisUtility !== 'undefined') {
          result.exports.push('runFullAnalysis', 'generateSystemReport');
        } else {
          result.issues.push('SystemAnalysisUtility not available globally');
          result.coverage -= 30;
        }
      }
    } catch (error: any) {
      result.issues.push(`Utility test failed: ${error.message}`);
      result.coverage -= 40;
    }
  }

  /**
   * Test hook functionality
   */
  private static async testHook(name: string, result: ModuleAnalysisResult): Promise<void> {
    // Hooks are harder to test directly, so we check for common patterns
    if (name === 'useTasks' || name === 'useProjects') {
      // Check if hook is being used in components
      const hookUsage = this.checkHookUsage(name);
      if (!hookUsage) {
        result.issues.push('Hook usage not detected in components');
        result.coverage -= 20;
      }
    }
  }

  /**
   * Test integration functionality
   */
  private static async testIntegration(name: string, result: ModuleAnalysisResult): Promise<void> {
    try {
      if (name === 'SupabaseClient') {
        // Test Supabase connection
        const { data, error } = await supabase.from('profiles').select('id').limit(1);
        if (error) {
          result.issues.push(`Supabase connection error: ${error.message}`);
          result.coverage -= 50;
        } else {
          result.exports.push('supabase client', 'database queries', 'auth');
        }
      }
    } catch (error: any) {
      result.issues.push(`Integration test failed: ${error.message}`);
      result.coverage -= 40;
    }
  }

  /**
   * Check for common issues
   */
  private static async checkCommonIssues(name: string, result: ModuleAnalysisResult): Promise<void> {
    // Check for TypeScript errors
    const tsErrors = this.checkTypeScriptErrors(name);
    if (tsErrors.length > 0) {
      result.issues.push(...tsErrors);
      result.coverage -= 10;
    }

    // Check for missing dependencies
    const missingDeps = this.checkMissingDependencies(name);
    if (missingDeps.length > 0) {
      result.issues.push(...missingDeps);
      result.coverage -= 15;
    }

    // Update status based on coverage
    if (result.coverage < 50) {
      result.status = 'error';
    } else if (result.coverage < 80) {
      result.status = 'incomplete';
    } else if (result.issues.length > 0) {
      result.status = 'incomplete';
    }
  }

  /**
   * Check for React-specific errors
   */
  private static checkReactErrors(componentName: string): string[] {
    const errors: string[] = [];
    
    // Check console for React errors
    const consoleErrors = this.getConsoleErrors();
    const reactErrors = consoleErrors.filter(error => 
      error.includes(componentName) && 
      (error.includes('React') || error.includes('component'))
    );
    
    return reactErrors;
  }

  /**
   * Check for TypeScript errors
   */
  private static checkTypeScriptErrors(moduleName: string): string[] {
    const errors: string[] = [];
    
    // Check console for TypeScript errors
    const consoleErrors = this.getConsoleErrors();
    const tsErrors = consoleErrors.filter(error => 
      error.includes(moduleName) && 
      (error.includes('TypeScript') || error.includes('TS'))
    );
    
    return tsErrors;
  }

  /**
   * Check for missing dependencies
   */
  private static checkMissingDependencies(moduleName: string): string[] {
    const errors: string[] = [];
    
    // Check console for import/dependency errors
    const consoleErrors = this.getConsoleErrors();
    const depErrors = consoleErrors.filter(error => 
      error.includes(moduleName) && 
      (error.includes('import') || error.includes('dependency') || error.includes('module'))
    );
    
    return depErrors;
  }

  /**
   * Check if hook is being used
   */
  private static checkHookUsage(hookName: string): boolean {
    // Simple check - in a real implementation, this would analyze the codebase
    return true; // Assume hooks are being used
  }

  /**
   * Get console errors
   */
  private static getConsoleErrors(): string[] {
    // In a real implementation, this would capture console errors
    // For now, return empty array
    return [];
  }

  /**
   * Generate comprehensive module report
   */
  static generateModuleReport(results: ModuleAnalysisResult[]): SystemModuleReport {
    const totalModules = results.length;
    const workingModules = results.filter(r => r.status === 'working').length;
    const errorModules = results.filter(r => r.status === 'error').length;
    const missingModules = results.filter(r => r.status === 'missing').length;
    const incompleteModules = results.filter(r => r.status === 'incomplete').length;

    const overallHealth = Math.round((workingModules / totalModules) * 100);

    const criticalIssues: string[] = [];
    const recommendations: string[] = [];

    // Identify critical issues
    results.forEach(result => {
      if (result.status === 'error') {
        criticalIssues.push(`${result.module}: ${result.issues.join(', ')}`);
      }
    });

    // Generate recommendations
    if (errorModules > 0) {
      recommendations.push(`Fix ${errorModules} modules with errors`);
    }
    if (incompleteModules > 0) {
      recommendations.push(`Complete implementation of ${incompleteModules} modules`);
    }
    if (overallHealth < 80) {
      recommendations.push('System health is below 80% - prioritize fixing critical modules');
    }

    return {
      totalModules,
      workingModules,
      errorModules,
      missingModules,
      incompleteModules,
      overallHealth,
      criticalIssues,
      recommendations
    };
  }
}

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).ModuleAnalyzer = ModuleAnalyzer;
  (window as any).runModuleAnalysis = () => ModuleAnalyzer.runFullAnalysis();
  
  console.log('🔍 Module Analyzer loaded. Available commands:');
  console.log('  - runModuleAnalysis()');
}
