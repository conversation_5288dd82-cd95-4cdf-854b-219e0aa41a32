/**
 * Improved Location Service
 * Handles GPS timeouts gracefully and provides better fallback mechanisms
 */

import { LocationData } from '@/types/timeTracking';

export interface LocationOptions {
  enableHighAccuracy?: boolean;
  timeout?: number;
  maximumAge?: number;
  fallbackToIP?: boolean;
  silent?: boolean; // Don't log warnings for expected failures
}

export class ImprovedLocationService {
  private static instance: ImprovedLocationService;
  private lastKnownLocation: LocationData | null = null;
  private locationCache = new Map<string, { data: LocationData; timestamp: number }>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  static getInstance(): ImprovedLocationService {
    if (!ImprovedLocationService.instance) {
      ImprovedLocationService.instance = new ImprovedLocationService();
    }
    return ImprovedLocationService.instance;
  }

  /**
   * Get current location with improved error handling and fallbacks
   */
  async getCurrentLocation(options: LocationOptions = {}): Promise<LocationData | null> {
    const {
      enableHighAccuracy = false,
      timeout = 5000, // Reduced from 10000 to prevent long waits
      maximumAge = 5 * 60 * 1000,
      fallbackToIP = true,
      silent = false
    } = options;

    try {
      // Check cache first
      const cached = this.getCachedLocation();
      if (cached && !silent) {
        console.log('📍 Using cached location');
        return cached;
      }

      // Try GPS with shorter timeout
      const gpsLocation = await this.getGPSLocation({
        enableHighAccuracy,
        timeout,
        maximumAge,
        silent
      });

      if (gpsLocation) {
        this.lastKnownLocation = gpsLocation;
        this.cacheLocation(gpsLocation);
        return gpsLocation;
      }

      // Fallback to IP-based location
      if (fallbackToIP) {
        if (!silent) {
          console.log('📍 GPS failed, trying IP-based location...');
        }
        const ipLocation = await this.getIPLocation(silent);
        if (ipLocation) {
          this.lastKnownLocation = ipLocation;
          return ipLocation;
        }
      }

      // Return last known location if available
      if (this.lastKnownLocation) {
        if (!silent) {
          console.log('📍 Using last known location');
        }
        return this.lastKnownLocation;
      }

      // Final fallback to default location
      return this.getDefaultLocation();

    } catch (error) {
      if (!silent) {
        console.warn('⚠️ Location service error:', error);
      }
      return this.lastKnownLocation || this.getDefaultLocation();
    }
  }

  /**
   * Get GPS location with improved timeout handling
   */
  private async getGPSLocation(options: LocationOptions): Promise<LocationData | null> {
    const { enableHighAccuracy, timeout, maximumAge, silent } = options;

    return new Promise((resolve) => {
      if (!navigator.geolocation) {
        if (!silent) {
          console.warn('⚠️ Geolocation not supported');
        }
        resolve(null);
        return;
      }

      let resolved = false;
      const timeoutId = setTimeout(() => {
        if (!resolved) {
          resolved = true;
          if (!silent) {
            console.warn('⚠️ GPS timeout - using fallback');
          }
          resolve(null);
        }
      }, timeout);

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          if (resolved) return;
          resolved = true;
          clearTimeout(timeoutId);

          try {
            const { latitude, longitude, accuracy } = position.coords;
            
            if (!silent) {
              console.log('✅ GPS position obtained');
            }

            // Try to get address (with timeout)
            const address = await this.reverseGeocode(latitude, longitude, silent);

            const locationData: LocationData = {
              latitude,
              longitude,
              address: address || `${latitude.toFixed(4)}, ${longitude.toFixed(4)}`,
              accuracy: accuracy || 0,
              method: 'gps'
            };

            resolve(locationData);
          } catch (error) {
            if (!silent) {
              console.warn('⚠️ Error processing GPS position:', error);
            }
            resolve(null);
          }
        },
        (error) => {
          if (resolved) return;
          resolved = true;
          clearTimeout(timeoutId);

          if (!silent) {
            // Only log specific errors, not generic ones
            if (error.code === error.PERMISSION_DENIED) {
              console.warn('⚠️ GPS permission denied');
            } else if (error.code === error.POSITION_UNAVAILABLE) {
              console.warn('⚠️ GPS position unavailable');
            }
            // Don't log timeout errors as they're expected
          }
          resolve(null);
        },
        {
          enableHighAccuracy,
          timeout: timeout - 500, // Slightly less than our timeout
          maximumAge
        }
      );
    });
  }

  /**
   * Get IP-based location
   */
  private async getIPLocation(silent = false): Promise<LocationData | null> {
    const services = [
      {
        name: 'ipapi',
        url: 'https://ipapi.co/json/',
        parser: (data: any) => ({
          latitude: data.latitude,
          longitude: data.longitude,
          address: `${data.city}, ${data.region}, ${data.country_name}`,
          accuracy: 10000,
          method: 'network' as const
        })
      },
      {
        name: 'ipify',
        url: 'https://api.ipify.org?format=json',
        parser: (data: any) => ({
          latitude: 0,
          longitude: 0,
          address: `IP: ${data.ip}`,
          accuracy: 50000,
          method: 'network' as const
        })
      }
    ];

    for (const service of services) {
      try {
        if (!silent) {
          console.log(`🌐 Trying ${service.name} IP location service...`);
        }

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 3000);

        const response = await fetch(service.url, {
          signal: controller.signal,
          mode: 'cors',
          cache: 'no-cache'
        });

        clearTimeout(timeoutId);

        if (!response.ok) continue;

        const data = await response.json();
        const locationData = service.parser(data);

        if (locationData.latitude && locationData.longitude) {
          if (!silent) {
            console.log(`✅ IP location from ${service.name}:`, locationData.address);
          }
          return locationData;
        }
      } catch (error: any) {
        if (!silent && !error.name?.includes('Abort')) {
          console.warn(`❌ ${service.name} failed:`, error.message);
        }
      }
    }

    return null;
  }

  /**
   * Reverse geocode coordinates to address
   */
  private async reverseGeocode(lat: number, lng: number, silent = false): Promise<string | null> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 2000);

      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`,
        {
          signal: controller.signal,
          headers: {
            'User-Agent': 'CT Nigeria AI Workboard'
          }
        }
      );

      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json();
        if (data.display_name) {
          return data.display_name;
        }
      }
    } catch (error) {
      // Silently fail for geocoding errors
    }

    return null;
  }

  /**
   * Cache location data
   */
  private cacheLocation(location: LocationData): void {
    const key = `${location.latitude}_${location.longitude}`;
    this.locationCache.set(key, {
      data: location,
      timestamp: Date.now()
    });
  }

  /**
   * Get cached location if available and fresh
   */
  private getCachedLocation(): LocationData | null {
    for (const [key, cached] of this.locationCache.entries()) {
      if (Date.now() - cached.timestamp < this.CACHE_DURATION) {
        return cached.data;
      } else {
        this.locationCache.delete(key);
      }
    }
    return null;
  }

  /**
   * Get default location (fallback)
   */
  private getDefaultLocation(): LocationData {
    return {
      latitude: 9.0765,
      longitude: 7.3986,
      address: 'Abuja, Nigeria',
      accuracy: 50000,
      method: 'default'
    };
  }

  /**
   * Clear location cache
   */
  clearCache(): void {
    this.locationCache.clear();
    this.lastKnownLocation = null;
  }

  /**
   * Get location silently (no console logs)
   */
  async getLocationSilently(): Promise<LocationData | null> {
    return this.getCurrentLocation({ silent: true, timeout: 3000 });
  }
}

// Export singleton instance
export const improvedLocationService = ImprovedLocationService.getInstance();
