/**
 * Lang<PERSON>hain Document Processor
 * Handles document loading, chunking, embedding, and vector store management
 */

import { supabase } from '@/integrations/supabase/client';
import { langChainConfig } from './config';

export interface DocumentChunk {
  id: string;
  content: string;
  metadata: {
    source: string;
    title?: string;
    author?: string;
    uploadedBy?: string;
    uploadedAt?: string;
    chunkIndex: number;
    totalChunks: number;
    [key: string]: any;
  };
  embedding?: number[];
}

export interface ProcessedDocument {
  id: string;
  title: string;
  chunks: DocumentChunk[];
  metadata: Record<string, any>;
  processingTime: number;
  totalTokens: number;
}

export class LangChainDocumentProcessor {
  /**
   * Process document content into chunks and embeddings
   */
  static async processDocument(
    content: string,
    metadata: Record<string, any> = {}
  ): Promise<ProcessedDocument> {
    const startTime = Date.now();
    const documentId = `doc_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
    
    try {
      // Split document into chunks
      const chunks = await this.splitDocument(content, metadata);
      
      // Generate embeddings for chunks
      const chunksWithEmbeddings = await this.generateEmbeddings(chunks);
      
      // Store in vector database
      await this.storeInVectorDB(chunksWithEmbeddings);
      
      const processingTime = Date.now() - startTime;
      
      const processedDoc: ProcessedDocument = {
        id: documentId,
        title: metadata.title || 'Untitled Document',
        chunks: chunksWithEmbeddings,
        metadata,
        processingTime,
        totalTokens: this.estimateTokens(content),
      };

      // Save document metadata
      await this.saveDocumentMetadata(processedDoc);
      
      return processedDoc;
    } catch (error) {
      console.error('Document processing failed:', error);
      throw error;
    }
  }

  /**
   * Split document into chunks
   */
  private static async splitDocument(
    content: string,
    metadata: Record<string, any>
  ): Promise<DocumentChunk[]> {
    const config = langChainConfig.getConfig();
    const { chunkSize, chunkOverlap } = config.rag;
    
    const chunks: DocumentChunk[] = [];
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    
    let currentChunk = '';
    let chunkIndex = 0;
    
    for (const sentence of sentences) {
      const trimmedSentence = sentence.trim();
      if (!trimmedSentence) continue;
      
      const potentialChunk = currentChunk + (currentChunk ? '. ' : '') + trimmedSentence;
      
      if (potentialChunk.length > chunkSize && currentChunk.length > 0) {
        // Create chunk
        chunks.push({
          id: `chunk_${Date.now()}_${chunkIndex}`,
          content: currentChunk.trim(),
          metadata: {
            ...metadata,
            chunkIndex,
            totalChunks: 0, // Will be updated later
          },
        });
        
        // Start new chunk with overlap
        const overlapWords = currentChunk.split(' ').slice(-Math.floor(chunkOverlap / 10));
        currentChunk = overlapWords.join(' ') + '. ' + trimmedSentence;
        chunkIndex++;
      } else {
        currentChunk = potentialChunk;
      }
    }
    
    // Add final chunk
    if (currentChunk.trim()) {
      chunks.push({
        id: `chunk_${Date.now()}_${chunkIndex}`,
        content: currentChunk.trim(),
        metadata: {
          ...metadata,
          chunkIndex,
          totalChunks: 0,
        },
      });
    }
    
    // Update total chunks count
    chunks.forEach(chunk => {
      chunk.metadata.totalChunks = chunks.length;
    });
    
    return chunks;
  }

  /**
   * Generate embeddings for chunks using Supabase Edge Function
   */
  private static async generateEmbeddings(chunks: DocumentChunk[]): Promise<DocumentChunk[]> {
    try {
      const { data, error } = await supabase.functions.invoke('generate-embeddings', {
        body: {
          texts: chunks.map(chunk => chunk.content),
          model: langChainConfig.getConfig().embeddings.model,
        },
      });

      if (error) {
        console.warn('Edge function embedding failed, using fallback');
        return chunks; // Return without embeddings
      }

      // Attach embeddings to chunks
      return chunks.map((chunk, index) => ({
        ...chunk,
        embedding: data.embeddings[index],
      }));
    } catch (error) {
      console.warn('Embedding generation failed:', error);
      return chunks; // Return without embeddings
    }
  }

  /**
   * Store chunks in vector database
   */
  private static async storeInVectorDB(chunks: DocumentChunk[]): Promise<void> {
    try {
      const config = langChainConfig.getConfig();
      
      for (const chunk of chunks) {
        const { error } = await supabase
          .from(config.vectorStore.tableName)
          .insert({
            id: chunk.id,
            content: chunk.content,
            metadata: chunk.metadata,
            embedding: chunk.embedding,
          });

        if (error) {
          console.error('Failed to store chunk:', error);
        }
      }
    } catch (error) {
      console.error('Vector storage failed:', error);
    }
  }

  /**
   * Save document metadata
   */
  private static async saveDocumentMetadata(doc: ProcessedDocument): Promise<void> {
    try {
      const { error } = await supabase
        .from('langchain_document_metadata')
        .insert({
          id: doc.id,
          title: doc.title,
          metadata: doc.metadata,
          chunk_count: doc.chunks.length,
          processing_time: doc.processingTime,
          total_tokens: doc.totalTokens,
        });

      if (error) {
        console.error('Failed to save document metadata:', error);
      }
    } catch (error) {
      console.error('Failed to save document metadata:', error);
    }
  }

  /**
   * Search similar documents
   */
  static async searchSimilar(
    query: string,
    limit: number = 5,
    threshold: number = 0.7
  ): Promise<DocumentChunk[]> {
    try {
      // Generate embedding for query
      const { data: embeddingData, error: embeddingError } = await supabase.functions.invoke('generate-embeddings', {
        body: {
          texts: [query],
          model: langChainConfig.getConfig().embeddings.model,
        },
      });

      if (embeddingError) {
        console.error('Query embedding failed:', embeddingError);
        return [];
      }

      const queryEmbedding = embeddingData.embeddings[0];
      const config = langChainConfig.getConfig();

      // Search vector database
      const { data, error } = await supabase.rpc(config.vectorStore.queryName, {
        query_embedding: queryEmbedding,
        match_threshold: threshold,
        match_count: limit,
      });

      if (error) {
        console.error('Vector search failed:', error);
        return [];
      }

      return data.map((item: any) => ({
        id: item.id,
        content: item.content,
        metadata: item.metadata,
        similarity: item.similarity,
      }));
    } catch (error) {
      console.error('Similarity search failed:', error);
      return [];
    }
  }

  /**
   * Delete document and its chunks
   */
  static async deleteDocument(documentId: string): Promise<void> {
    try {
      const config = langChainConfig.getConfig();
      
      // Delete chunks from vector store
      await supabase
        .from(config.vectorStore.tableName)
        .delete()
        .eq('metadata->>source', documentId);

      // Delete metadata
      await supabase
        .from('langchain_document_metadata')
        .delete()
        .eq('id', documentId);
    } catch (error) {
      console.error('Document deletion failed:', error);
    }
  }

  /**
   * Get document by ID
   */
  static async getDocument(documentId: string): Promise<ProcessedDocument | null> {
    try {
      const { data: metadata, error: metaError } = await supabase
        .from('langchain_document_metadata')
        .select('*')
        .eq('id', documentId)
        .single();

      if (metaError || !metadata) {
        return null;
      }

      const config = langChainConfig.getConfig();
      const { data: chunks, error: chunksError } = await supabase
        .from(config.vectorStore.tableName)
        .select('*')
        .eq('metadata->>source', documentId)
        .order('metadata->>chunkIndex');

      if (chunksError) {
        console.error('Failed to load chunks:', chunksError);
        return null;
      }

      return {
        id: metadata.id,
        title: metadata.title,
        chunks: chunks || [],
        metadata: metadata.metadata,
        processingTime: metadata.processing_time,
        totalTokens: metadata.total_tokens,
      };
    } catch (error) {
      console.error('Failed to get document:', error);
      return null;
    }
  }

  /**
   * Estimate token count
   */
  private static estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
  }

  /**
   * List all documents
   */
  static async listDocuments(limit: number = 50): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('langchain_document_metadata')
        .select('id, title, metadata, chunk_count, created_at')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Failed to list documents:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Failed to list documents:', error);
      return [];
    }
  }
}

export const langChainDocumentProcessor = LangChainDocumentProcessor;
