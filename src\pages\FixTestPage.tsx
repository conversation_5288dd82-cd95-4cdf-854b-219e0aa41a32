/**
 * Fix Test Page
 * A page to test and verify all the fixes are working correctly
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, AlertCircle, RefreshCw, MapPin, Database, Wifi } from 'lucide-react';
import { comprehensiveAppFixer, FixResult } from '@/utils/comprehensive-app-fixer';
import { userPresenceTableFixer } from '@/utils/fix-user-presence-table';
import { tasksTableFixer } from '@/utils/fix-tasks-table';
import { profilesTableFixer } from '@/utils/fix-profiles-table';
import { timeLogsTableFixer } from '@/utils/fix-time-logs-table';
import { improvedLocationService } from '@/services/improved-location-service';
import { supabase } from '@/integrations/supabase/client';

export const FixTestPage: React.FC = () => {
  const [fixResults, setFixResults] = useState<FixResult[]>([]);
  const [isRunningFixes, setIsRunningFixes] = useState(false);
  const [testResults, setTestResults] = useState<any>({});

  useEffect(() => {
    // Run initial tests
    runInitialTests();
  }, []);

  const runInitialTests = async () => {
    console.log('🧪 Running initial tests...');
    
    const results: any = {};

    // Test 1: Check user_presence table
    try {
      const tableExists = await userPresenceTableFixer.checkTableExists();
      results.userPresenceTable = {
        success: tableExists,
        message: tableExists ? 'user_presence table accessible' : 'user_presence table not accessible'
      };
    } catch (error: any) {
      results.userPresenceTable = {
        success: false,
        message: `user_presence test failed: ${error.message}`
      };
    }

    // Test 2: Check tasks table
    try {
      const tasksExists = await tasksTableFixer.checkTasksTable();
      results.tasksTable = {
        success: tasksExists,
        message: tasksExists ? 'tasks table accessible' : 'tasks table not accessible'
      };
    } catch (error: any) {
      results.tasksTable = {
        success: false,
        message: `tasks test failed: ${error.message}`
      };
    }

    // Test 3: Check profiles table
    try {
      const profilesColumns = await profilesTableFixer.checkProfilesColumns();
      const missingColumns = Object.entries(profilesColumns)
        .filter(([_, exists]) => !exists)
        .map(([column, _]) => column);

      results.profilesTable = {
        success: missingColumns.length === 0,
        message: missingColumns.length === 0 ? 'All profiles columns exist' : `Missing: ${missingColumns.join(', ')}`
      };
    } catch (error: any) {
      results.profilesTable = {
        success: false,
        message: `Profiles test failed: ${error.message}`
      };
    }

    // Test 4: Check time_logs table
    try {
      const timeLogsExists = await timeLogsTableFixer.checkTimeLogsTable();
      results.timeLogsTable = {
        success: timeLogsExists,
        message: timeLogsExists ? 'time_logs table accessible' : 'time_logs table not accessible'
      };
    } catch (error: any) {
      results.timeLogsTable = {
        success: false,
        message: `time_logs test failed: ${error.message}`
      };
    }

    // Test 5: Check location service
    try {
      const location = await improvedLocationService.getLocationSilently();
      results.locationService = {
        success: !!location,
        message: location ? `Location: ${location.address}` : 'Location service failed'
      };
    } catch (error: any) {
      results.locationService = {
        success: false,
        message: `Location test failed: ${error.message}`
      };
    }

    // Test 6: Check Supabase connection
    try {
      const { data, error } = await supabase.from('profiles').select('id').limit(1);
      results.supabaseConnection = {
        success: !error,
        message: error ? `Supabase error: ${error.message}` : 'Supabase connection working'
      };
    } catch (error: any) {
      results.supabaseConnection = {
        success: false,
        message: `Supabase test failed: ${error.message}`
      };
    }

    // Test 7: Check routes
    const currentPath = window.location.pathname;
    results.routeTest = {
      success: true,
      message: `Current route: ${currentPath}`
    };

    setTestResults(results);
  };

  const runAllFixes = async () => {
    setIsRunningFixes(true);
    try {
      const result = await comprehensiveAppFixer.runAllFixes();
      setFixResults(result.results);
      
      // Re-run tests after fixes
      await runInitialTests();
    } catch (error) {
      console.error('Failed to run fixes:', error);
    } finally {
      setIsRunningFixes(false);
    }
  };

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="h-4 w-4 text-green-600" />
    ) : (
      <XCircle className="h-4 w-4 text-red-600" />
    );
  };

  const getStatusBadge = (success: boolean) => {
    return (
      <Badge variant={success ? "default" : "destructive"}>
        {success ? "PASS" : "FAIL"}
      </Badge>
    );
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Fix Test Page</h1>
          <p className="text-gray-600 mt-1">
            Test and verify all application fixes
          </p>
        </div>
        <Button 
          onClick={runAllFixes} 
          disabled={isRunningFixes}
          className="flex items-center space-x-2"
        >
          <RefreshCw className={`h-4 w-4 ${isRunningFixes ? 'animate-spin' : ''}`} />
          <span>{isRunningFixes ? 'Running Fixes...' : 'Run All Fixes'}</span>
        </Button>
      </div>

      {/* Test Results */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Initial Tests */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5" />
              <span>System Tests</span>
            </CardTitle>
            <CardDescription>
              Basic system functionality tests
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {Object.entries(testResults).map(([key, result]: [string, any]) => (
              <div key={key} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(result.success)}
                  <div>
                    <p className="font-medium capitalize">{key.replace(/([A-Z])/g, ' $1')}</p>
                    <p className="text-sm text-gray-600">{result.message}</p>
                  </div>
                </div>
                {getStatusBadge(result.success)}
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Fix Results */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5" />
              <span>Fix Results</span>
            </CardTitle>
            <CardDescription>
              Results from running comprehensive fixes
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {fixResults.length === 0 ? (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  No fixes have been run yet. Click "Run All Fixes" to start.
                </AlertDescription>
              </Alert>
            ) : (
              fixResults.map((result, index) => (
                <div key={index} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(result.success)}
                      <span className="font-medium">{result.message}</span>
                    </div>
                    {getStatusBadge(result.success)}
                  </div>
                  {result.details && result.details.length > 0 && (
                    <ul className="text-sm text-gray-600 ml-6 space-y-1">
                      {result.details.map((detail, detailIndex) => (
                        <li key={detailIndex} className="list-disc">{detail}</li>
                      ))}
                    </ul>
                  )}
                </div>
              ))
            )}
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Test specific components and features
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button 
              variant="outline" 
              className="flex items-center space-x-2"
              onClick={() => window.location.href = '/dashboard/staff/time'}
            >
              <MapPin className="h-4 w-4" />
              <span>Test Time Tracking</span>
            </Button>
            
            <Button 
              variant="outline" 
              className="flex items-center space-x-2"
              onClick={() => window.location.href = '/dashboard/staff/leave'}
            >
              <Database className="h-4 w-4" />
              <span>Test Leave Request</span>
            </Button>
            
            <Button 
              variant="outline" 
              className="flex items-center space-x-2"
              onClick={() => window.location.href = '/dashboard/staff/tasks'}
            >
              <Wifi className="h-4 w-4" />
              <span>Test Task Management</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Summary */}
      {fixResults.length > 0 && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            Fix Summary: {comprehensiveAppFixer.getFixSummary()}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};
