/**
 * Database Fix Utility
 * Comprehensive database error detection and fixing
 */

import { supabase } from '@/integrations/supabase/client';

export interface DatabaseIssue {
  type: 'missing_table' | 'foreign_key_error' | 'permission_error' | 'schema_mismatch';
  table: string;
  column?: string;
  error: string;
  severity: 'critical' | 'warning' | 'info';
  fix?: string;
}

export class DatabaseFixUtility {
  private static issues: DatabaseIssue[] = [];

  /**
   * Run comprehensive database health check
   */
  static async runHealthCheck(): Promise<DatabaseIssue[]> {
    this.issues = [];
    console.log('🔍 Running comprehensive database health check...');

    await this.checkCoreTablesExist();
    await this.checkForeignKeyRelationships();
    await this.checkPermissions();
    await this.checkSchemaConsistency();

    console.log(`✅ Health check complete. Found ${this.issues.length} issues.`);
    return this.issues;
  }

  /**
   * Check if core tables exist
   */
  private static async checkCoreTablesExist(): Promise<void> {
    const coreTables = [
      'profiles',
      'departments', 
      'projects',
      'tasks',
      'invoices',
      'memos',
      'time_logs',
      'project_assignments'
    ];

    for (const table of coreTables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);

        if (error) {
          if (error.code === 'PGRST116' || error.message.includes('does not exist')) {
            this.addIssue({
              type: 'missing_table',
              table,
              error: `Table ${table} does not exist`,
              severity: 'critical',
              fix: `CREATE TABLE ${table} with proper schema`
            });
          } else {
            this.addIssue({
              type: 'permission_error',
              table,
              error: error.message,
              severity: 'warning'
            });
          }
        }
      } catch (err) {
        this.addIssue({
          type: 'missing_table',
          table,
          error: `Failed to query table ${table}`,
          severity: 'critical'
        });
      }
    }
  }

  /**
   * Check foreign key relationships
   */
  private static async checkForeignKeyRelationships(): Promise<void> {
    const relationships = [
      {
        table: 'profiles',
        query: 'id, full_name, department:departments(id, name)',
        description: 'Profiles -> Departments'
      },
      {
        table: 'projects',
        query: 'id, name, manager:profiles!manager_id(id, full_name), department:departments!department_id(id, name)',
        description: 'Projects -> Profiles/Departments'
      },
      {
        table: 'tasks',
        query: 'id, title, assignee:profiles!assigned_to_id(id, full_name), creator:profiles!created_by(id, full_name)',
        description: 'Tasks -> Profiles'
      },
      {
        table: 'project_assignments',
        query: 'id, project:projects!project_id(id, name), assigned_to_profile:profiles!assigned_to(id, full_name)',
        description: 'Project Assignments -> Projects/Profiles'
      },
      {
        table: 'invoices',
        query: 'id, invoice_number, creator:profiles!created_by(id, full_name)',
        description: 'Invoices -> Profiles'
      }
    ];

    for (const rel of relationships) {
      try {
        const { data, error } = await supabase
          .from(rel.table)
          .select(rel.query)
          .limit(1);

        if (error && error.message.includes('relationship')) {
          this.addIssue({
            type: 'foreign_key_error',
            table: rel.table,
            error: `Foreign key relationship error: ${error.message}`,
            severity: 'warning',
            fix: `Check foreign key constraints for ${rel.description}`
          });
        }
      } catch (err) {
        this.addIssue({
          type: 'foreign_key_error',
          table: rel.table,
          error: `Failed to test relationship: ${rel.description}`,
          severity: 'warning'
        });
      }
    }
  }

  /**
   * Check permissions and RLS policies
   */
  private static async checkPermissions(): Promise<void> {
    const tables = ['profiles', 'projects', 'tasks', 'invoices'];

    for (const table of tables) {
      try {
        // Test read permission
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);

        if (error && (error.code === 'PGRST301' || error.message.includes('permission denied'))) {
          this.addIssue({
            type: 'permission_error',
            table,
            error: `Permission denied for table ${table}`,
            severity: 'critical',
            fix: `Check RLS policies for ${table} table`
          });
        }

        // Test write permission (for non-critical tables)
        if (!error && ['tasks', 'projects'].includes(table)) {
          const { error: insertError } = await supabase
            .from(table)
            .insert({})
            .select()
            .limit(0); // Don't actually insert

          if (insertError && insertError.message.includes('permission denied')) {
            this.addIssue({
              type: 'permission_error',
              table,
              error: `Write permission denied for table ${table}`,
              severity: 'warning',
              fix: `Check INSERT RLS policies for ${table} table`
            });
          }
        }
      } catch (err) {
        // Ignore test errors
      }
    }
  }

  /**
   * Check schema consistency
   */
  private static async checkSchemaConsistency(): Promise<void> {
    const expectedColumns = {
      profiles: ['id', 'full_name', 'email', 'role', 'department_id'],
      projects: ['id', 'name', 'description', 'manager_id', 'department_id', 'status'],
      tasks: ['id', 'title', 'description', 'assigned_to_id', 'created_by', 'status'],
      invoices: ['id', 'invoice_number', 'client_name', 'created_by', 'payment_status']
    };

    for (const [table, expectedCols] of Object.entries(expectedColumns)) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);

        if (!error && data && data.length > 0) {
          const actualColumns = Object.keys(data[0]);
          const missingColumns = expectedCols.filter(col => !actualColumns.includes(col));

          if (missingColumns.length > 0) {
            this.addIssue({
              type: 'schema_mismatch',
              table,
              error: `Missing columns: ${missingColumns.join(', ')}`,
              severity: 'warning',
              fix: `Add missing columns to ${table} table`
            });
          }
        }
      } catch (err) {
        // Table doesn't exist, already handled in checkCoreTablesExist
      }
    }
  }

  /**
   * Attempt to fix common database issues
   */
  static async autoFixIssues(): Promise<{ fixed: number; failed: number; errors: string[] }> {
    console.log('🔧 Attempting to auto-fix database issues...');
    
    let fixed = 0;
    let failed = 0;
    const errors: string[] = [];

    const issues = await this.runHealthCheck();

    for (const issue of issues) {
      try {
        if (issue.type === 'missing_table' && issue.table === 'departments') {
          await this.createDepartmentsTable();
          fixed++;
        } else if (issue.type === 'foreign_key_error') {
          // For foreign key errors, we'll use fallback queries instead of fixing the schema
          console.log(`ℹ️ Foreign key issue noted for ${issue.table}, using fallback queries`);
          fixed++;
        } else {
          console.log(`⚠️ Cannot auto-fix: ${issue.error}`);
        }
      } catch (error) {
        failed++;
        errors.push(`Failed to fix ${issue.table}: ${error}`);
      }
    }

    console.log(`✅ Auto-fix complete: ${fixed} fixed, ${failed} failed`);
    return { fixed, failed, errors };
  }

  /**
   * Create departments table if missing
   */
  private static async createDepartmentsTable(): Promise<void> {
    const { error } = await supabase.rpc('create_departments_table');
    if (error) {
      throw new Error(`Failed to create departments table: ${error.message}`);
    }
  }

  /**
   * Get safe query for table with fallback
   */
  static async getSafeQuery(table: string, columns: string = '*', filters?: any): Promise<any> {
    try {
      let query = supabase.from(table).select(columns);
      
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      const { data, error } = await query;

      if (error) {
        console.warn(`Query failed for ${table}, using fallback:`, error.message);
        
        // Fallback to basic query without foreign key relationships
        const basicQuery = supabase.from(table).select('*');
        if (filters) {
          Object.entries(filters).forEach(([key, value]) => {
            basicQuery.eq(key, value);
          });
        }
        
        const { data: fallbackData, error: fallbackError } = await basicQuery;
        
        if (fallbackError) {
          throw fallbackError;
        }
        
        return fallbackData || [];
      }

      return data || [];
    } catch (error) {
      console.error(`Safe query failed for ${table}:`, error);
      return [];
    }
  }

  private static addIssue(issue: DatabaseIssue): void {
    this.issues.push(issue);
    console.log(`${issue.severity === 'critical' ? '🚨' : '⚠️'} ${issue.table}: ${issue.error}`);
  }

  /**
   * Generate database health report
   */
  static generateHealthReport(issues: DatabaseIssue[]): string {
    const critical = issues.filter(i => i.severity === 'critical');
    const warnings = issues.filter(i => i.severity === 'warning');
    const info = issues.filter(i => i.severity === 'info');

    let report = '📊 Database Health Report\n';
    report += '========================\n\n';
    
    if (critical.length === 0 && warnings.length === 0) {
      report += '✅ All systems operational!\n';
    } else {
      if (critical.length > 0) {
        report += `🚨 Critical Issues (${critical.length}):\n`;
        critical.forEach(issue => {
          report += `  - ${issue.table}: ${issue.error}\n`;
          if (issue.fix) report += `    Fix: ${issue.fix}\n`;
        });
        report += '\n';
      }

      if (warnings.length > 0) {
        report += `⚠️ Warnings (${warnings.length}):\n`;
        warnings.forEach(issue => {
          report += `  - ${issue.table}: ${issue.error}\n`;
          if (issue.fix) report += `    Fix: ${issue.fix}\n`;
        });
        report += '\n';
      }
    }

    return report;
  }
}

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).DatabaseFixUtility = DatabaseFixUtility;
  (window as any).runDatabaseHealthCheck = () => DatabaseFixUtility.runHealthCheck();
  (window as any).autoFixDatabase = () => DatabaseFixUtility.autoFixIssues();
  
  console.log('🛠️ Database Fix Utility loaded. Available commands:');
  console.log('  - runDatabaseHealthCheck()');
  console.log('  - autoFixDatabase()');
}
