/**
 * Supabase Database Service
 * Optimized database operations following Supabase best practices
 */

import { supabase } from '@/integrations/supabase/client';
import type { PostgrestError } from '@supabase/supabase-js';

interface QueryOptions {
  useCache?: boolean;
  cacheKey?: string;
  cacheDuration?: number;
  retryAttempts?: number;
  timeout?: number;
}

interface DatabaseResult<T> {
  data: T | null;
  error: PostgrestError | Error | null;
  count?: number;
  cached?: boolean;
}

export class SupabaseDatabaseService {
  private static instance: SupabaseDatabaseService;
  private queryCache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private readonly DEFAULT_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  private readonly DEFAULT_TIMEOUT = 10000; // 10 seconds
  private readonly DEFAULT_RETRY_ATTEMPTS = 3;

  static getInstance(): SupabaseDatabaseService {
    if (!SupabaseDatabaseService.instance) {
      SupabaseDatabaseService.instance = new SupabaseDatabaseService();
    }
    return SupabaseDatabaseService.instance;
  }

  /**
   * Execute a query with enhanced error handling and caching
   */
  async executeQuery<T>(
    queryFn: () => Promise<{ data: T | null; error: PostgrestError | null; count?: number }>,
    options: QueryOptions = {}
  ): Promise<DatabaseResult<T>> {
    const {
      useCache = false,
      cacheKey,
      cacheDuration = this.DEFAULT_CACHE_DURATION,
      retryAttempts = this.DEFAULT_RETRY_ATTEMPTS,
      timeout = this.DEFAULT_TIMEOUT
    } = options;

    // Check cache first
    if (useCache && cacheKey) {
      const cached = this.getFromCache<T>(cacheKey);
      if (cached) {
        console.log(`📦 Cache hit for: ${cacheKey}`);
        return { data: cached, error: null, cached: true };
      }
    }

    // Execute query with retry logic
    let lastError: PostgrestError | Error | null = null;
    
    for (let attempt = 1; attempt <= retryAttempts; attempt++) {
      try {
        console.log(`🔍 Executing query (attempt ${attempt}/${retryAttempts})`);
        
        // Create timeout promise
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error('Query timeout')), timeout);
        });

        // Execute query with timeout
        const result = await Promise.race([queryFn(), timeoutPromise]);

        if (result.error) {
          lastError = result.error;
          console.warn(`⚠️ Query error (attempt ${attempt}):`, result.error.message);
          
          // Don't retry certain types of errors
          if (this.isNonRetryableError(result.error)) {
            break;
          }
          
          // Wait before retry (exponential backoff)
          if (attempt < retryAttempts) {
            const delay = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s...
            await new Promise(resolve => setTimeout(resolve, delay));
          }
          continue;
        }

        // Success - cache result if requested
        if (useCache && cacheKey && result.data) {
          this.setCache(cacheKey, result.data, cacheDuration);
        }

        console.log(`✅ Query executed successfully`);
        return {
          data: result.data,
          error: null,
          count: result.count,
          cached: false
        };

      } catch (error: any) {
        lastError = error;
        console.warn(`⚠️ Query execution error (attempt ${attempt}):`, error.message);
        
        if (attempt < retryAttempts) {
          const delay = Math.pow(2, attempt - 1) * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    console.error(`❌ Query failed after ${retryAttempts} attempts:`, lastError);
    return { data: null, error: lastError, cached: false };
  }

  /**
   * Get projects with optimized query
   */
  async getProjects(filters?: { status?: string; managerId?: string }): Promise<DatabaseResult<any[]>> {
    return this.executeQuery(
      async () => {
        console.log('📋 Fetching projects...');
        
        // Build query step by step to avoid foreign key issues
        // Use only safe columns that exist in the database
        let query = supabase
          .from('projects')
          .select(`
            id,
            name,
            description,
            status,
            manager_id,
            created_at,
            updated_at
          `);

        // Apply filters
        if (filters?.status) {
          query = query.eq('status', filters.status);
        }
        if (filters?.managerId) {
          query = query.eq('manager_id', filters.managerId);
        }

        const { data: projects, error: projectsError, count } = await query
          .order('created_at', { ascending: false });

        if (projectsError) {
          return { data: null, error: projectsError, count };
        }

        // Fetch manager information separately if we have projects
        if (projects && projects.length > 0) {
          const managerIds = [...new Set(projects
            .map(p => p.manager_id)
            .filter(id => id !== null && id !== undefined)
          )];

          if (managerIds.length > 0) {
            const { data: managers, error: managersError } = await supabase
              .from('profiles')
              .select('id, full_name, email')
              .in('id', managerIds);

            if (!managersError && managers) {
              // Manually join the data
              const projectsWithManagers = projects.map(project => ({
                ...project,
                manager: managers.find(m => m.id === project.manager_id) || null
              }));

              return { data: projectsWithManagers, error: null, count };
            }
          }
        }

        return { data: projects || [], error: null, count };
      },
      {
        useCache: true,
        cacheKey: `projects_${JSON.stringify(filters || {})}`,
        cacheDuration: 2 * 60 * 1000 // 2 minutes for projects
      }
    );
  }

  /**
   * Get tasks with optimized query
   */
  async getTasks(filters?: { assignedTo?: string; projectId?: string; status?: string }): Promise<DatabaseResult<any[]>> {
    return this.executeQuery(
      async () => {
        console.log('📋 Fetching tasks...');
        
        let query = supabase
          .from('tasks')
          .select(`
            id,
            title,
            description,
            status,
            assigned_to_id,
            project_id,
            created_by,
            created_at,
            updated_at
          `);

        // Apply filters
        if (filters?.assignedTo) {
          query = query.eq('assigned_to_id', filters.assignedTo);
        }
        if (filters?.projectId) {
          query = query.eq('project_id', filters.projectId);
        }
        if (filters?.status) {
          query = query.eq('status', filters.status);
        }

        const { data: tasks, error: tasksError, count } = await query
          .order('created_at', { ascending: false });

        if (tasksError) {
          return { data: null, error: tasksError, count };
        }

        // Fetch creator information separately if we have tasks
        if (tasks && tasks.length > 0) {
          const userIds = [...new Set([
            ...tasks.map(t => t.created_by),
            ...tasks.map(t => t.assigned_to_id)
          ].filter(id => id !== null && id !== undefined))];

          if (userIds.length > 0) {
            const { data: users, error: usersError } = await supabase
              .from('profiles')
              .select('id, full_name, email')
              .in('id', userIds);

            if (!usersError && users) {
              // Manually join the data
              const tasksWithUsers = tasks.map(task => ({
                ...task,
                creator: users.find(u => u.id === task.created_by) || null,
                assignee: users.find(u => u.id === task.assigned_to_id) || null
              }));

              return { data: tasksWithUsers, error: null, count };
            }
          }
        }

        return { data: tasks || [], error: null, count };
      },
      {
        useCache: true,
        cacheKey: `tasks_${JSON.stringify(filters || {})}`,
        cacheDuration: 1 * 60 * 1000 // 1 minute for tasks
      }
    );
  }

  /**
   * Get user profile with caching
   */
  async getUserProfile(userId: string): Promise<DatabaseResult<any>> {
    return this.executeQuery(
      async () => {
        console.log(`👤 Fetching user profile: ${userId}`);
        
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .single();

        return { data, error };
      },
      {
        useCache: true,
        cacheKey: `profile_${userId}`,
        cacheDuration: 10 * 60 * 1000 // 10 minutes for profiles
      }
    );
  }

  /**
   * Subscribe to real-time changes for a table
   */
  subscribeToTable(
    table: string,
    callback: (payload: any) => void,
    filter?: string
  ): () => void {
    console.log(`📡 Subscribing to table: ${table}`);
    
    const channelName = `db-changes-${table}-${Date.now()}`;
    const channel = supabase.channel(channelName);

    // Configure postgres changes subscription
    const config: any = {
      event: '*',
      schema: 'public',
      table: table
    };

    if (filter) {
      config.filter = filter;
    }

    channel
      .on('postgres_changes', config, (payload) => {
        console.log(`🗄️ Database change in ${table}:`, payload);
        callback(payload);
      })
      .subscribe((status) => {
        console.log(`📡 Subscription status for ${table}:`, status);
      });

    // Return unsubscribe function
    return () => {
      console.log(`🔌 Unsubscribing from ${table}`);
      supabase.removeChannel(channel);
    };
  }

  /**
   * Check if error is non-retryable
   */
  private isNonRetryableError(error: PostgrestError): boolean {
    const nonRetryableCodes = [
      'PGRST116', // JSON object requested, multiple (or no) rows returned
      'PGRST200', // Could not find relationship
      '42P01',    // Table does not exist
      '42703',    // Column does not exist
      '23505',    // Unique violation
      '23503',    // Foreign key violation
    ];

    return nonRetryableCodes.includes(error.code || '');
  }

  /**
   * Get data from cache
   */
  private getFromCache<T>(key: string): T | null {
    const cached = this.queryCache.get(key);
    if (!cached) return null;

    const now = Date.now();
    if (now > cached.timestamp + cached.ttl) {
      this.queryCache.delete(key);
      return null;
    }

    return cached.data;
  }

  /**
   * Set data in cache
   */
  private setCache(key: string, data: any, ttl: number): void {
    this.queryCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });

    // Clean up old cache entries periodically
    if (this.queryCache.size > 100) {
      this.cleanupCache();
    }
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupCache(): void {
    const now = Date.now();
    for (const [key, cached] of this.queryCache.entries()) {
      if (now > cached.timestamp + cached.ttl) {
        this.queryCache.delete(key);
      }
    }
    console.log(`🧹 Cache cleanup completed. Entries: ${this.queryCache.size}`);
  }

  /**
   * Clear all cache
   */
  clearCache(): void {
    this.queryCache.clear();
    console.log('🧹 All cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.queryCache.size,
      keys: Array.from(this.queryCache.keys())
    };
  }
}

// Global instance
export const supabaseDatabaseService = SupabaseDatabaseService.getInstance();

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).supabaseDatabaseService = supabaseDatabaseService;
  (window as any).getProjects = (filters?: any) => supabaseDatabaseService.getProjects(filters);
  (window as any).getTasks = (filters?: any) => supabaseDatabaseService.getTasks(filters);
  (window as any).clearDbCache = () => supabaseDatabaseService.clearCache();
  (window as any).getDbCacheStats = () => supabaseDatabaseService.getCacheStats();
  
  console.log('🗄️ Supabase Database Service loaded. Available commands:');
  console.log('  - getProjects(filters)');
  console.log('  - getTasks(filters)');
  console.log('  - clearDbCache()');
  console.log('  - getDbCacheStats()');
}
