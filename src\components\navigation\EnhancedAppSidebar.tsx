import { useSupabaseAuth } from "@/hooks/useSupabaseAuth";
import { LogoutButton } from "@/components/auth/LogoutButton";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { QuickCacheClearButton } from "@/components/ui/QuickCacheClearButton";
import {
    Sidebar,
    SidebarContent,
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    useSidebar
} from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import {
    BarChart3,
    Battery,
    Bell,
    Brain,
    Building2,
    Calendar,
    Car,
    CheckSquare,
    ChevronDown, ChevronRight,
    Clipboard,
    Clock,
    Code,
    Construction,
    Database,
    DollarSign,
    FileCheck,
    FileImage,
    FileText,
    Key,
    LayoutDashboard,
    Menu,
    Network,
    Package,
    Settings,
    Shield,
    ShoppingCart,
    StickyNote,
    TrendingUp,
    User,
    <PERSON>,
    Wrench
} from "lucide-react";
import React, { useState } from "react";
import { NavLink, useLocation } from "react-router-dom";

interface MenuItem {
  title: string;
  href: string;
  icon: any;
  subItems?: MenuItem[];
  badge?: string;
}

export function EnhancedAppSidebar() {
  const { userProfile } = useSupabaseAuth();
  const { state, toggleSidebar } = useSidebar();
  const location = useLocation();
  const [expandedGroups, setExpandedGroups] = useState<string[]>(["main", "operations"]);

  const toggleGroup = (groupName: string) => {
    setExpandedGroups(prev =>
      prev.includes(groupName)
        ? prev.filter(g => g !== groupName)
        : [...prev, groupName]
    );
  };

  const handleUserManual = () => {
    // Open user manual in new tab
    window.open('/user-manual.html', '_blank');
  };

  // Simplified and consolidated menu items based on role
  const getMenuItems = (): MenuItem[] => {
    const role = userProfile?.role || userProfile?.account_type;
    
    const commonItems = [
      { title: "AI Assistant", href: "/dashboard/ai", icon: Brain },
      { title: "Documents", href: "/dashboard/documents", icon: FileImage },
      { title: "Settings", href: "/dashboard/settings", icon: Settings },
    ];
    
    switch (role) {
      case 'admin':
        return [
          { title: "Dashboard", href: "/dashboard/admin", icon: LayoutDashboard },
          { title: "User Management", href: "/dashboard/admin/users", icon: Users },
          { title: "System Diagnostics", href: "/dashboard/admin/diagnostics", icon: BarChart3 },
          { title: "API Keys", href: "/dashboard/admin/api-keys", icon: Key },
          { title: "Database Management", href: "/dashboard/admin/database", icon: Database },
          { title: "Integrations", href: "/dashboard/admin/integrations", icon: Shield },
          { title: "AI Management", href: "/dashboard/admin/ai", icon: Brain },
          { title: "Settings", href: "/dashboard/admin/settings", icon: Settings },
          { title: "Departments", href: "/dashboard/admin/departments", icon: Building2 },
          { title: "Projects", href: "/dashboard/admin/projects", icon: Clipboard },
          { title: "Reports", href: "/dashboard/admin/reports", icon: BarChart3 },
          { title: "Activities", href: "/dashboard/admin/activities", icon: Calendar },
          { title: "Communication", href: "/dashboard/admin/communication", icon: StickyNote },
          { title: "Construction", href: "/dashboard/construction", icon: Construction },
          { title: "Fleet", href: "/dashboard/fleet", icon: Car },
          { title: "Battery", href: "/dashboard/battery", icon: Battery },
          { title: "Battery Setup", href: "/dashboard/admin/battery-setup", icon: Settings },
          { title: "Auth Debug", href: "/dashboard/admin/auth-debug", icon: Shield },
          { title: "Database Fix", href: "/dashboard/admin/database-fix", icon: Database },
          { title: "RPC Setup", href: "/dashboard/admin/rpc-setup", icon: Code },
          { title: "Assets", href: "/dashboard/assets", icon: Package },
          { title: "Financial", href: "/dashboard/financial", icon: DollarSign },
          { title: "Procurement", href: "/dashboard/procurement", icon: ShoppingCart },
          { title: "Notification Management", href: "/dashboard/admin/notification-management", icon: Bell },
          ...commonItems,
        ];
      
      case 'manager':
        return [
          { title: "Dashboard", href: "/dashboard/manager", icon: LayoutDashboard },
          { title: "Projects", href: "/dashboard/manager/projects", icon: Clipboard },
          { title: "Team", href: "/dashboard/manager/team", icon: Users },
          { title: "Time Tracking", href: "/dashboard/manager/time", icon: Clock },
          { title: "Work Board", href: "/dashboard/manager/workboard", icon: Clipboard },
          { title: "Leave Requests", href: "/dashboard/manager/leave", icon: FileCheck },
          { title: "Sites", href: "/dashboard/manager/sites", icon: Network },
          { title: "Memos", href: "/dashboard/manager/memos", icon: StickyNote },
          { title: "Reports", href: "/dashboard/manager/reports", icon: BarChart3 },
          { title: "Invoices", href: "/dashboard/manager/invoices", icon: FileText },
          { title: "Meetings", href: "/dashboard/manager/meetings", icon: Calendar },
          { title: "Fleet", href: "/dashboard/fleet", icon: Car },
          { title: "Construction", href: "/dashboard/construction", icon: Construction },
          { title: "Financial", href: "/dashboard/financial", icon: DollarSign },
          { title: "Procurement", href: "/dashboard/procurement", icon: ShoppingCart },
          ...commonItems,
        ];
      
      case 'accountant':
        return [
          { title: "Dashboard", href: "/dashboard/accountant", icon: LayoutDashboard },
          { title: "Invoices", href: "/dashboard/accountant/invoices", icon: FileText },
          { title: "Financial", href: "/dashboard/financial", icon: DollarSign },
          { title: "Assets", href: "/dashboard/assets", icon: Package },
          { title: "Procurement", href: "/dashboard/procurement", icon: ShoppingCart },
          ...commonItems,
        ];
      
      case 'staff':
        return [
          { title: "Dashboard", href: "/dashboard/staff", icon: LayoutDashboard },
          { title: "Tasks", href: "/dashboard/staff/tasks", icon: CheckSquare },
          { title: "Time Tracking", href: "/dashboard/staff/time", icon: Clock },
          { title: "Leave Requests", href: "/dashboard/staff/leave", icon: FileCheck },
          { title: "Memos", href: "/dashboard/staff/memos", icon: StickyNote },
          { title: "Reports", href: "/dashboard/staff/reports", icon: BarChart3 },
          ...commonItems,
        ];
      
      default:
        return [
          { title: "Dashboard", href: "/dashboard", icon: LayoutDashboard },
          ...commonItems,
        ];
    }
  };

  const menuItems = getMenuItems();

  const isActive = (href: string) => {
    return location.pathname === href || location.pathname.startsWith(href + '/');
  };

  return (
    <Sidebar className="border-r border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <SidebarContent className="gap-0">
        {/* Header */}
        <div className="flex h-14 items-center border-b border-border/40 px-4">
          <div className="flex items-center gap-2">
            <span className="font-bold text-foreground uppercase tracking-wider text-lg">CTNL</span>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="ml-auto h-8 w-8"
            onClick={toggleSidebar}
          >
            <Menu className="h-4 w-4" />
          </Button>
        </div>

        {/* User Profile */}
        <div className="border-b border-border/40 p-4">
          <div className="flex items-center gap-3">
            <Avatar className="h-9 w-9">
              <AvatarImage src={userProfile?.avatar_url} />
              <AvatarFallback className="bg-primary/10 text-primary">
                {userProfile?.full_name?.charAt(0) || userProfile?.email?.charAt(0) || 'U'}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-bold text-foreground truncate uppercase tracking-wide">
                {(userProfile?.full_name || userProfile?.email || 'User').toUpperCase()}
              </p>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs font-semibold uppercase tracking-wider">
                  {(userProfile?.role || userProfile?.account_type || 'user').toUpperCase()}
                </Badge>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Menu */}
        <SidebarGroup className="px-0">
          <SidebarGroupContent>
            <SidebarMenu className="gap-1">
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.href}>
                  <SidebarMenuButton
                    asChild
                    isActive={isActive(item.href)}
                    className={cn(
                      "w-full justify-start gap-3 px-4 py-2.5 text-sm font-semibold transition-all duration-300",
                      "hover:bg-accent hover:text-accent-foreground",
                      "focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring",
                      "border-[0.8px] border-[#ff1c04] rounded-[5px]",
                      "bg-gradient-to-br from-[#1a1a1a] to-[#0f0f0f] text-[#e5e5e5]",
                      "shadow-[4px_4px_8px_rgba(0,0,0,0.4),-4px_-4px_8px_rgba(255,28,4,0.1)]",
                      "hover:shadow-[2px_2px_4px_rgba(0,0,0,0.6),-2px_-2px_4px_rgba(255,28,4,0.2)]",
                      "hover:transform hover:-translate-y-0.5 hover:text-[#ff1c04] hover:border-[#ff1c04]",
                      "hover:bg-gradient-to-br hover:from-[#2a0a0a] hover:to-[#1a0505]",
                      "relative overflow-hidden uppercase tracking-wide",
                      isActive(item.href) && "bg-gradient-to-br from-[#2a0a0a] to-[#1a0505] text-[#ff1c04] shadow-inner"
                    )}
                  >
                    <NavLink to={item.href} className="flex items-center gap-3 w-full">
                      <item.icon className="h-4 w-4 shrink-0" />
                      <span className="truncate">{item.title}</span>
                      {item.badge && (
                        <Badge variant="secondary" className="ml-auto text-xs">
                          {item.badge}
                        </Badge>
                      )}
                    </NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Footer Actions */}
        <div className="mt-auto border-t border-border/40 p-4 space-y-2">
          <Button
            variant="outline"
            size="sm"
            className={cn(
              "w-full justify-start gap-2 transition-all duration-300 font-semibold uppercase tracking-wide",
              "border-[0.8px] border-[#ff1c04] rounded-[5px] text-[#e5e5e5]",
              "bg-gradient-to-br from-[#1a1a1a] to-[#0f0f0f]",
              "shadow-[4px_4px_8px_rgba(0,0,0,0.4),-4px_-4px_8px_rgba(255,28,4,0.1)]",
              "hover:shadow-[2px_2px_4px_rgba(0,0,0,0.6),-2px_-2px_4px_rgba(255,28,4,0.2)]",
              "hover:transform hover:-translate-y-0.5 hover:text-[#ff1c04] hover:border-[#ff1c04]",
              "hover:bg-gradient-to-br hover:from-[#2a0a0a] hover:to-[#1a0505]"
            )}
            onClick={handleUserManual}
          >
            <FileText className="h-4 w-4" />
            User Manual
          </Button>
          <QuickCacheClearButton className={cn(
            "border-[0.8px] border-[#ff1c04] rounded-[5px] font-semibold uppercase tracking-wide text-[#e5e5e5]",
            "bg-gradient-to-br from-[#1a1a1a] to-[#0f0f0f]",
            "shadow-[4px_4px_8px_rgba(0,0,0,0.4),-4px_-4px_8px_rgba(255,28,4,0.1)]",
            "hover:shadow-[2px_2px_4px_rgba(0,0,0,0.6),-2px_-2px_4px_rgba(255,28,4,0.2)]",
            "hover:transform hover:-translate-y-0.5 hover:text-[#ff1c04] hover:border-[#ff1c04]",
            "hover:bg-gradient-to-br hover:from-[#2a0a0a] hover:to-[#1a0505]"
          )} />
          <LogoutButton className={cn(
            "border-[0.8px] border-[#ff1c04] rounded-[5px] font-semibold uppercase tracking-wide text-[#e5e5e5]",
            "bg-gradient-to-br from-[#1a1a1a] to-[#0f0f0f]",
            "shadow-[4px_4px_8px_rgba(0,0,0,0.4),-4px_-4px_8px_rgba(255,28,4,0.1)]",
            "hover:shadow-[2px_2px_4px_rgba(0,0,0,0.6),-2px_-2px_4px_rgba(255,28,4,0.2)]",
            "hover:transform hover:-translate-y-0.5 hover:text-[#ff1c04] hover:border-[#ff1c04]",
            "hover:bg-gradient-to-br hover:from-[#2a0a0a] hover:to-[#1a0505]"
          )} />
        </div>
      </SidebarContent>
    </Sidebar>
  );
}
