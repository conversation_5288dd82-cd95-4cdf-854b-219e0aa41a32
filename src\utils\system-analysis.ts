/**
 * Comprehensive System Analysis Utility
 * Analyzes the entire system for missing implementations and errors
 */

import { supabase } from '@/integrations/supabase/client';
import { DatabaseFixUtility } from './database-fix-utility';
import { TaskProjectFixUtility } from './task-project-fix';

export interface SystemAnalysisResult {
  category: string;
  component: string;
  status: 'working' | 'error' | 'missing' | 'warning';
  message: string;
  fix?: string;
}

export class SystemAnalysisUtility {
  private static results: SystemAnalysisResult[] = [];

  /**
   * Run comprehensive system analysis
   */
  static async runFullAnalysis(): Promise<SystemAnalysisResult[]> {
    this.results = [];
    console.log('🔍 Running comprehensive system analysis...');

    await this.analyzeAuthentication();
    await this.analyzeDatabaseConnectivity();
    await this.analyzeUserInterface();
    await this.analyzeBusinessLogic();
    await this.analyzeIntegrations();
    await this.analyzePerformance();

    console.log(`✅ System analysis complete. Found ${this.results.length} items.`);
    return this.results;
  }

  /**
   * Analyze authentication system
   */
  private static async analyzeAuthentication(): Promise<void> {
    try {
      // Test current user session
      const { data: { user }, error } = await supabase.auth.getUser();
      
      if (error) {
        this.addResult('Authentication', 'User Session', 'error', `Auth error: ${error.message}`);
      } else if (user) {
        this.addResult('Authentication', 'User Session', 'working', 'User authenticated successfully');
        
        // Test profile access
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (profileError) {
          this.addResult('Authentication', 'User Profile', 'error', `Profile error: ${profileError.message}`);
        } else {
          this.addResult('Authentication', 'User Profile', 'working', 'Profile access working');
        }
      } else {
        this.addResult('Authentication', 'User Session', 'warning', 'No user session found');
      }
    } catch (error: any) {
      this.addResult('Authentication', 'System', 'error', `Auth system error: ${error.message}`);
    }
  }

  /**
   * Analyze database connectivity and health
   */
  private static async analyzeDatabaseConnectivity(): Promise<void> {
    try {
      // Run database health check
      const dbIssues = await DatabaseFixUtility.runHealthCheck();
      
      if (dbIssues.length === 0) {
        this.addResult('Database', 'Health Check', 'working', 'All database tables accessible');
      } else {
        const criticalIssues = dbIssues.filter(i => i.severity === 'critical');
        const warnings = dbIssues.filter(i => i.severity === 'warning');
        
        if (criticalIssues.length > 0) {
          this.addResult('Database', 'Critical Issues', 'error', 
            `${criticalIssues.length} critical database issues found`,
            'Run autoFixDatabase() to attempt fixes'
          );
        }
        
        if (warnings.length > 0) {
          this.addResult('Database', 'Warnings', 'warning', 
            `${warnings.length} database warnings found`
          );
        }
      }

      // Test basic CRUD operations
      const { data, error } = await supabase
        .from('profiles')
        .select('id')
        .limit(1);

      if (error) {
        this.addResult('Database', 'CRUD Operations', 'error', `CRUD test failed: ${error.message}`);
      } else {
        this.addResult('Database', 'CRUD Operations', 'working', 'Basic CRUD operations working');
      }
    } catch (error: any) {
      this.addResult('Database', 'Connectivity', 'error', `Database connection failed: ${error.message}`);
    }
  }

  /**
   * Analyze user interface components
   */
  private static async analyzeUserInterface(): Promise<void> {
    try {
      // Check if key UI components are loaded
      const components = [
        'EnhancedAppSidebar',
        'ModernAIInterface', 
        'ModernDashboardGrid',
        'ModernDocumentCard',
        'AIDocumentAnalyzer'
      ];

      for (const component of components) {
        // Check if component exists in the DOM or can be imported
        try {
          const element = document.querySelector(`[data-component="${component}"]`);
          if (element) {
            this.addResult('UI Components', component, 'working', 'Component rendered successfully');
          } else {
            this.addResult('UI Components', component, 'warning', 'Component not found in DOM');
          }
        } catch (error) {
          this.addResult('UI Components', component, 'error', 'Component loading error');
        }
      }

      // Check color scheme implementation
      const sidebar = document.querySelector('.sidebar-nav-link');
      if (sidebar) {
        const styles = window.getComputedStyle(sidebar);
        const borderColor = styles.borderColor;
        
        if (borderColor.includes('255, 28, 4') || borderColor.includes('#ff1c04')) {
          this.addResult('UI Components', 'Color Scheme', 'working', 'Brand colors applied correctly');
        } else {
          this.addResult('UI Components', 'Color Scheme', 'warning', 'Brand colors may not be applied');
        }
      } else {
        this.addResult('UI Components', 'Sidebar', 'warning', 'Sidebar not found in DOM');
      }
    } catch (error: any) {
      this.addResult('UI Components', 'Analysis', 'error', `UI analysis failed: ${error.message}`);
    }
  }

  /**
   * Analyze business logic functionality
   */
  private static async analyzeBusinessLogic(): Promise<void> {
    try {
      // Test task and project functionality
      const taskProjectTests = await TaskProjectFixUtility.runTests();
      
      if (taskProjectTests.passed > 0) {
        this.addResult('Business Logic', 'Task Management', 'working', 
          `${taskProjectTests.passed} task/project tests passed`
        );
      }
      
      if (taskProjectTests.failed > 0) {
        this.addResult('Business Logic', 'Task Management', 'error', 
          `${taskProjectTests.failed} task/project tests failed`,
          'Check task and project creation/update functionality'
        );
      }

      // Test invoice management
      try {
        const { data, error } = await supabase
          .from('invoices')
          .select('*')
          .limit(1);

        if (error && error.message.includes('does not exist')) {
          this.addResult('Business Logic', 'Invoice Management', 'missing', 
            'Invoices table not found',
            'Create invoices table with proper schema'
          );
        } else if (error) {
          this.addResult('Business Logic', 'Invoice Management', 'error', 
            `Invoice access error: ${error.message}`
          );
        } else {
          this.addResult('Business Logic', 'Invoice Management', 'working', 
            'Invoice management accessible'
          );
        }
      } catch (error: any) {
        this.addResult('Business Logic', 'Invoice Management', 'error', 
          `Invoice test failed: ${error.message}`
        );
      }

      // Test document management
      try {
        const { data, error } = await supabase
          .from('document_archive')
          .select('*')
          .limit(1);

        if (error && error.message.includes('does not exist')) {
          this.addResult('Business Logic', 'Document Management', 'missing', 
            'Document archive table not found',
            'Create document_archive table'
          );
        } else if (error) {
          this.addResult('Business Logic', 'Document Management', 'error', 
            `Document access error: ${error.message}`
          );
        } else {
          this.addResult('Business Logic', 'Document Management', 'working', 
            'Document management accessible'
          );
        }
      } catch (error: any) {
        this.addResult('Business Logic', 'Document Management', 'error', 
          `Document test failed: ${error.message}`
        );
      }
    } catch (error: any) {
      this.addResult('Business Logic', 'Analysis', 'error', 
        `Business logic analysis failed: ${error.message}`
      );
    }
  }

  /**
   * Analyze integrations and external services
   */
  private static async analyzeIntegrations(): Promise<void> {
    try {
      // Test AI integration
      try {
        const { data, error } = await supabase.functions.invoke('ai-assistant', {
          body: { message: 'test', context: { task: 'health_check' } }
        });

        if (error) {
          this.addResult('Integrations', 'AI Assistant', 'error', 
            `AI integration error: ${error.message}`,
            'Check OpenAI API key and edge function deployment'
          );
        } else {
          this.addResult('Integrations', 'AI Assistant', 'working', 
            'AI integration working'
          );
        }
      } catch (error: any) {
        this.addResult('Integrations', 'AI Assistant', 'error', 
          `AI test failed: ${error.message}`
        );
      }

      // Test real-time features
      try {
        const channel = supabase.channel('test-channel');
        if (channel) {
          this.addResult('Integrations', 'Real-time', 'working', 
            'Real-time channels available'
          );
        } else {
          this.addResult('Integrations', 'Real-time', 'error', 
            'Real-time channels not available'
          );
        }
      } catch (error: any) {
        this.addResult('Integrations', 'Real-time', 'error', 
          `Real-time test failed: ${error.message}`
        );
      }
    } catch (error: any) {
      this.addResult('Integrations', 'Analysis', 'error', 
        `Integration analysis failed: ${error.message}`
      );
    }
  }

  /**
   * Analyze system performance
   */
  private static async analyzePerformance(): Promise<void> {
    try {
      // Test query performance
      const startTime = performance.now();
      
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .limit(10);

      const endTime = performance.now();
      const queryTime = endTime - startTime;

      if (error) {
        this.addResult('Performance', 'Query Speed', 'error', 
          `Query failed: ${error.message}`
        );
      } else if (queryTime > 2000) {
        this.addResult('Performance', 'Query Speed', 'warning', 
          `Slow query detected: ${queryTime.toFixed(2)}ms`,
          'Consider optimizing database queries'
        );
      } else {
        this.addResult('Performance', 'Query Speed', 'working', 
          `Query performance good: ${queryTime.toFixed(2)}ms`
        );
      }

      // Check memory usage
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const usedMB = memory.usedJSHeapSize / 1024 / 1024;
        
        if (usedMB > 100) {
          this.addResult('Performance', 'Memory Usage', 'warning', 
            `High memory usage: ${usedMB.toFixed(2)}MB`,
            'Consider optimizing component rendering'
          );
        } else {
          this.addResult('Performance', 'Memory Usage', 'working', 
            `Memory usage normal: ${usedMB.toFixed(2)}MB`
          );
        }
      }
    } catch (error: any) {
      this.addResult('Performance', 'Analysis', 'error', 
        `Performance analysis failed: ${error.message}`
      );
    }
  }

  /**
   * Generate comprehensive system report
   */
  static generateSystemReport(results: SystemAnalysisResult[]): string {
    const working = results.filter(r => r.status === 'working');
    const errors = results.filter(r => r.status === 'error');
    const warnings = results.filter(r => r.status === 'warning');
    const missing = results.filter(r => r.status === 'missing');

    let report = '🔍 COMPREHENSIVE SYSTEM ANALYSIS REPORT\n';
    report += '==========================================\n\n';
    
    report += `📊 SUMMARY:\n`;
    report += `✅ Working: ${working.length}\n`;
    report += `⚠️ Warnings: ${warnings.length}\n`;
    report += `❌ Errors: ${errors.length}\n`;
    report += `❓ Missing: ${missing.length}\n\n`;

    if (errors.length > 0) {
      report += `🚨 CRITICAL ERRORS:\n`;
      errors.forEach(result => {
        report += `  - ${result.category}/${result.component}: ${result.message}\n`;
        if (result.fix) report += `    Fix: ${result.fix}\n`;
      });
      report += '\n';
    }

    if (missing.length > 0) {
      report += `❓ MISSING COMPONENTS:\n`;
      missing.forEach(result => {
        report += `  - ${result.category}/${result.component}: ${result.message}\n`;
        if (result.fix) report += `    Fix: ${result.fix}\n`;
      });
      report += '\n';
    }

    if (warnings.length > 0) {
      report += `⚠️ WARNINGS:\n`;
      warnings.forEach(result => {
        report += `  - ${result.category}/${result.component}: ${result.message}\n`;
        if (result.fix) report += `    Fix: ${result.fix}\n`;
      });
      report += '\n';
    }

    if (working.length > 0) {
      report += `✅ WORKING COMPONENTS:\n`;
      working.forEach(result => {
        report += `  - ${result.category}/${result.component}: ${result.message}\n`;
      });
    }

    return report;
  }

  private static addResult(
    category: string, 
    component: string, 
    status: 'working' | 'error' | 'missing' | 'warning', 
    message: string, 
    fix?: string
  ): void {
    this.results.push({ category, component, status, message, fix });
    
    const emoji = {
      working: '✅',
      error: '❌',
      missing: '❓',
      warning: '⚠️'
    }[status];
    
    console.log(`${emoji} ${category}/${component}: ${message}`);
  }
}

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).SystemAnalysisUtility = SystemAnalysisUtility;
  (window as any).runSystemAnalysis = () => SystemAnalysisUtility.runFullAnalysis();
  
  console.log('🔍 System Analysis Utility loaded. Available commands:');
  console.log('  - runSystemAnalysis()');
}
