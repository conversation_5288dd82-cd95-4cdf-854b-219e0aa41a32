/**
 * Fix User Presence Table
 * Utility to create and fix the user_presence table that's causing 400 errors
 */

import { supabase } from '@/integrations/supabase/client';

export class UserPresenceTableFixer {
  private static instance: UserPresenceTableFixer;
  private isFixed = false;

  static getInstance(): UserPresenceTableFixer {
    if (!UserPresenceTableFixer.instance) {
      UserPresenceTableFixer.instance = new UserPresenceTableFixer();
    }
    return UserPresenceTableFixer.instance;
  }

  /**
   * Check if user_presence table exists and is accessible
   */
  async checkTableExists(): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_presence')
        .select('id')
        .limit(1);

      if (error) {
        console.log('❌ user_presence table check failed:', error.message);
        return false;
      }

      console.log('✅ user_presence table exists and is accessible');
      return true;
    } catch (error) {
      console.log('❌ user_presence table check error:', error);
      return false;
    }
  }

  /**
   * Create user_presence table using RPC function
   */
  async createTable(): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🔧 Creating user_presence table...');

      // First check if table already exists
      const exists = await this.checkTableExists();
      if (exists) {
        console.log('✅ user_presence table already exists');
        this.isFixed = true;
        return { success: true };
      }

      // Create the table using RPC
      const { error } = await supabase.rpc('exec_sql', {
        sql: `
          -- Create user_presence table
          CREATE TABLE IF NOT EXISTS public.user_presence (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
            status TEXT DEFAULT 'offline' CHECK (status IN ('online', 'away', 'busy', 'offline')),
            last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            current_page TEXT,
            is_typing BOOLEAN DEFAULT FALSE,
            location_data JSONB,
            device_info JSONB,
            metadata JSONB DEFAULT '{}'::jsonb,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(user_id)
          );

          -- Enable RLS
          ALTER TABLE public.user_presence ENABLE ROW LEVEL SECURITY;

          -- Create policies
          DROP POLICY IF EXISTS "Users can view all presence data" ON public.user_presence;
          DROP POLICY IF EXISTS "Users can manage their own presence" ON public.user_presence;

          CREATE POLICY "Users can view all presence data" 
          ON public.user_presence 
          FOR SELECT 
          TO authenticated 
          USING (true);

          CREATE POLICY "Users can manage their own presence" 
          ON public.user_presence 
          FOR ALL 
          TO authenticated 
          USING (auth.uid() = user_id) 
          WITH CHECK (auth.uid() = user_id);

          -- Create indexes
          CREATE INDEX IF NOT EXISTS idx_user_presence_user_id ON public.user_presence(user_id);
          CREATE INDEX IF NOT EXISTS idx_user_presence_status ON public.user_presence(status);
          CREATE INDEX IF NOT EXISTS idx_user_presence_last_seen ON public.user_presence(last_seen);
        `
      });

      if (error) {
        console.error('❌ Failed to create user_presence table:', error.message);
        return { success: false, error: error.message };
      }

      // Verify table was created
      const tableExists = await this.checkTableExists();
      if (tableExists) {
        console.log('✅ user_presence table created successfully');
        this.isFixed = true;
        return { success: true };
      } else {
        return { success: false, error: 'Table creation verification failed' };
      }

    } catch (error: any) {
      console.error('❌ Error creating user_presence table:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create a test presence record for the current user
   */
  async createTestPresenceRecord(): Promise<{ success: boolean; error?: string }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return { success: false, error: 'No authenticated user' };
      }

      console.log('🧪 Creating test presence record...');

      const { error } = await supabase
        .from('user_presence')
        .upsert({
          user_id: user.id,
          status: 'online',
          last_seen: new Date().toISOString(),
          current_page: window.location.pathname,
          is_typing: false,
          metadata: {
            test_record: true,
            created_by: 'UserPresenceTableFixer',
            timestamp: new Date().toISOString()
          }
        }, {
          onConflict: 'user_id'
        });

      if (error) {
        console.error('❌ Failed to create test presence record:', error.message);
        return { success: false, error: error.message };
      }

      console.log('✅ Test presence record created successfully');
      return { success: true };

    } catch (error: any) {
      console.error('❌ Error creating test presence record:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Fix all user_presence related issues
   */
  async fixAllIssues(): Promise<{ success: boolean; results: string[] }> {
    const results: string[] = [];

    try {
      console.log('🔧 Starting user_presence table fixes...');

      // Step 1: Check if table exists
      const tableExists = await this.checkTableExists();
      if (!tableExists) {
        results.push('user_presence table does not exist');

        // Step 2: Create table
        const createResult = await this.createTable();
        if (createResult.success) {
          results.push('user_presence table created successfully');
        } else {
          results.push(`Failed to create user_presence table: ${createResult.error}`);
          return { success: false, results };
        }
      } else {
        results.push('user_presence table already exists');
      }

      // Step 3: Create test record
      const testResult = await this.createTestPresenceRecord();
      if (testResult.success) {
        results.push('Test presence record created successfully');
      } else {
        results.push(`Failed to create test presence record: ${testResult.error}`);
      }

      console.log('✅ user_presence table fixes completed');
      this.isFixed = true;
      return { success: true, results };

    } catch (error: any) {
      console.error('❌ Error during user_presence fixes:', error.message);
      results.push(`Error during fixes: ${error.message}`);
      return { success: false, results };
    }
  }

  /**
   * Get the current fix status
   */
  isTableFixed(): boolean {
    return this.isFixed;
  }

  /**
   * Reset the fix status (for testing)
   */
  resetFixStatus(): void {
    this.isFixed = false;
  }
}

// Export singleton instance
export const userPresenceTableFixer = UserPresenceTableFixer.getInstance();

// Export utility functions
export const fixUserPresenceTable = () => userPresenceTableFixer.fixAllIssues();
export const checkUserPresenceTable = () => userPresenceTableFixer.checkTableExists();
export const createTestPresenceRecord = () => userPresenceTableFixer.createTestPresenceRecord();
