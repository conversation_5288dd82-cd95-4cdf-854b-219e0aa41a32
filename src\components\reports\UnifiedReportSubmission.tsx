import { useAuth } from "@/components/auth/AuthProvider";
import { FileUploadComponent } from "@/components/files/FileUploadComponent";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { supabase } from "@/integrations/supabase/client";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { handleReportSubmission, showSuccessToast } from "@/utils/comprehensive-form-handler";
import {
  AlertCircle,
  Building,
  Calendar,
  CheckCircle,
  Clock,
  FileText,
  Paperclip,
  Send
} from "lucide-react";
import React, { useState } from 'react';

interface ReportFormData {
  title: string;
  description: string;
  report_type: string;
  priority: string;
  department_id?: string;
  project_id?: string;
  due_date?: string;
  attachments?: File[];
}

interface UploadedFile {
  id: string;
  original_filename: string;
  file_size: number;
  mime_type: string;
  file_path: string;
  upload_status: string;
}

export const UnifiedReportSubmission = () => {
  const { userProfile } = useAuth();
  const queryClient = useQueryClient();
  
  const [formData, setFormData] = useState<ReportFormData>({
    title: '',
    description: '',
    report_type: 'general',
    priority: 'medium'
  });

  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [reportId, setReportId] = useState<string | null>(null);

  // Submit report mutation
  const submitReportMutation = useMutation({
    mutationFn: async (data: ReportFormData) => {
      if (!userProfile?.id) throw new Error('User not authenticated');

      try {
        // Create the report
        const { data: reportData, error: reportError } = await supabase
          .from('reports')
          .insert([{
            title: data.title,
            description: data.description,
            report_type: data.report_type,
            priority: data.priority,
            status: 'submitted',
            submitted_by: userProfile.id,
            department_id: data.department_id || null,
            project_id: data.project_id || null,
            due_date: data.due_date || null,
            metadata: {
              submission_timestamp: new Date().toISOString(),
              user_role: userProfile.role,
              user_department: userProfile.department_id,
              attachments_count: uploadedFiles.length
            }
          }])
          .select()
          .single();

        if (reportError) {
          console.error('Report submission error:', reportError);
          throw new Error(reportError.message || 'Failed to submit report');
        }

        // Store report ID for file uploads
        setReportId(reportData.id);

        // Send notification to managers and relevant stakeholders
        await sendReportNotifications(reportData);

        return reportData;
      } catch (error: any) {
        console.error('Report submission failed:', error);
        throw error;
      }
    },
    onSuccess: (data) => {
      showSuccessToast({
        title: "Report Submitted Successfully",
        description: `Your ${data.report_type} report has been submitted and notifications sent.`,
      });

      // Reset form
      setFormData({
        title: '',
        description: '',
        report_type: 'general',
        priority: 'medium'
      });

      // Reset file uploads
      setUploadedFiles([]);
      setReportId(null);

      // Refresh reports list
      queryClient.invalidateQueries({ queryKey: ['reports'] });
      queryClient.invalidateQueries({ queryKey: ['staff-reports'] });
    },
    onError: (error: any) => {
      console.error('[UnifiedReportSubmission] Report submission error:', error, {
        user: userProfile?.id,
        formData
      });
      toast({
        title: "Submission Failed",
        description: error.message || "Failed to submit report. Please try again.",
        variant: "destructive",
      });
    }
  });

  const sendReportNotifications = async (reportData: any) => {
    try {
      // Get managers and relevant stakeholders
      const { data: managers, error: managersError } = await supabase
        .from('profiles')
        .select('id, email, full_name')
        .in('role', ['admin', 'manager'])
        .eq('status', 'active');

      if (managersError) {
        console.error('Error fetching managers:', managersError);
        return;
      }

      if (managers && managers.length > 0) {
        // Create in-app notifications
        const notifications = managers.map(manager => ({
          user_id: manager.id,
          type: 'report_submitted',
          title: 'New Report Submitted',
          message: `${userProfile?.full_name || 'A team member'} submitted a ${reportData.report_type} report: ${reportData.title}`,
          data: {
            report_id: reportData.id,
            report_type: reportData.report_type,
            submitted_by: userProfile?.full_name,
            priority: reportData.priority
          }
        }));

        const { error: notificationError } = await supabase
          .from('notifications')
          .insert(notifications);

        if (notificationError) {
          console.error('Error creating notifications:', notificationError);
        }

        // Send email notifications (optional - don't fail if this doesn't work)
        try {
          await supabase.functions.invoke('send-notification', {
            body: {
              type: 'report_submitted',
              recipients: managers.map(m => m.email).filter(Boolean),
              data: {
                reportTitle: reportData.title,
                reportType: reportData.report_type,
                submittedBy: userProfile?.full_name,
                priority: reportData.priority,
                submissionDate: new Date().toLocaleDateString()
              }
            }
          });
        } catch (emailError) {
          console.error('Error sending email notifications:', emailError);
          // Continue - email notifications are optional
        }
      }
    } catch (error) {
      console.error('[UnifiedReportSubmission] Error sending notifications:', error, {
        reportData,
        user: userProfile?.id
      });
      // Don't fail the whole operation if notifications fail
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.title?.trim()) {
      toast({
        title: "Validation Error",
        description: "Please enter a report title.",
        variant: "destructive",
      });
      return;
    }

    if (!formData.description?.trim()) {
      toast({
        title: "Validation Error",
        description: "Please enter report content.",
        variant: "destructive",
      });
      return;
    }

    if (formData.title.length < 5) {
      toast({
        title: "Validation Error",
        description: "Report title must be at least 5 characters long.",
        variant: "destructive",
      });
      return;
    }

    if (formData.description.length < 20) {
      toast({
        title: "Validation Error",
        description: "Report content must be at least 20 characters long.",
        variant: "destructive",
      });
      return;
    }

    if (!userProfile?.id) {
      toast({
        title: "Authentication Error",
        description: "You must be logged in to submit a report.",
        variant: "destructive",
      });
      return;
    }

    submitReportMutation.mutate(formData);
  };

  const handleInputChange = (field: keyof ReportFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleFileUploadComplete = (files: UploadedFile[]) => {
    setUploadedFiles(files);

    // Update the report with attachment information if report exists
    if (reportId && files.length > 0) {
      const attachmentData = files.map(file => ({
        id: file.id,
        filename: file.original_filename,
        size: file.file_size,
        type: file.mime_type,
        path: file.file_path
      }));

      // Update report metadata with attachment info
      supabase
        .from('reports')
        .update({
          attachments: attachmentData,
          updated_at: new Date().toISOString()
        })
        .eq('id', reportId)
        .then(({ error }) => {
          if (error) {
            console.error('Error updating report with attachments:', error);
          }
        });
    }

    showSuccessToast({
      title: "Files Uploaded",
      description: `${files.length} file(s) uploaded successfully`,
    });
  };

  const handleFileUploadError = (error: string) => {
    toast({
      title: "Upload Error",
      description: error,
      variant: "destructive",
    });
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Submit Report
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Report Type */}
          <div className="space-y-2">
            <Label htmlFor="report_type" className="flex items-center gap-2">
              <Building className="h-4 w-4" />
              Report Type *
            </Label>
            <Select value={formData.report_type} onValueChange={(value) => handleInputChange('report_type', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select report type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="general">General Report</SelectItem>
                <SelectItem value="weekly">Weekly Report</SelectItem>
                <SelectItem value="monthly">Monthly Report</SelectItem>
                <SelectItem value="project">Project Report</SelectItem>
                <SelectItem value="incident">Incident Report</SelectItem>
                <SelectItem value="maintenance">Maintenance Report</SelectItem>
                <SelectItem value="financial">Financial Report</SelectItem>
                <SelectItem value="safety">Safety Report</SelectItem>
                <SelectItem value="quality">Quality Report</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Report Title *
            </Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder="Enter a descriptive title for your report"
              required
            />
          </div>

          {/* Priority */}
          <div className="space-y-2">
            <Label htmlFor="priority" className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4" />
              Priority
            </Label>
            <Select value={formData.priority} onValueChange={(value) => handleInputChange('priority', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Due Date */}
          <div className="space-y-2">
            <Label htmlFor="due_date" className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Due Date (Optional)
            </Label>
            <Input
              id="due_date"
              type="date"
              value={formData.due_date || ''}
              onChange={(e) => handleInputChange('due_date', e.target.value)}
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Report Content *
            </Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Provide detailed information about your report..."
              rows={6}
              required
            />
          </div>

          {/* File Upload */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Paperclip className="h-4 w-4" />
              Attachments (Optional)
            </Label>
            <FileUploadComponent
              uploadType="report_attachment"
              entityType="report"
              entityId={reportId || undefined}
              maxFiles={5}
              maxSize={10 * 1024 * 1024} // 10MB
              onUploadComplete={handleFileUploadComplete}
              onUploadError={handleFileUploadError}
              className="mt-2"
            />

            {/* Display uploaded files */}
            {uploadedFiles.length > 0 && (
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Uploaded Files:</h4>
                <div className="space-y-2">
                  {uploadedFiles.map((file) => (
                    <div key={file.id} className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">{file.original_filename}</span>
                      <span className="text-gray-400">
                        {(file.file_size / 1024 / 1024).toFixed(2)} MB
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setFormData({
                  title: '',
                  description: '',
                  report_type: 'general',
                  priority: 'medium'
                });
                setUploadedFiles([]);
                setReportId(null);
              }}
            >
              Clear Form
            </Button>
            <Button
              type="submit"
              disabled={submitReportMutation.isPending}
              className="flex items-center gap-2"
            >
              {submitReportMutation.isPending ? (
                <>
                  <Clock className="h-4 w-4 animate-spin" />
                  Submitting...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4" />
                  Submit Report
                </>
              )}
            </Button>
          </div>
        </form>

        {/* Status Messages */}
        {submitReportMutation.isSuccess && (
          <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <div>
              <p className="text-green-800 font-medium">Report submitted successfully!</p>
              <p className="text-green-700 text-sm">Notifications have been sent to relevant managers.</p>
              {uploadedFiles.length > 0 && (
                <p className="text-green-700 text-sm">
                  {uploadedFiles.length} file(s) attached to your report.
                </p>
              )}
            </div>
          </div>
        )}

        {submitReportMutation.isError && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-red-600" />
            <div>
              <p className="text-red-800 font-medium">Failed to submit report</p>
              <p className="text-red-700 text-sm">
                {submitReportMutation.error?.message || 'Please try again or contact support if the problem persists.'}
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
