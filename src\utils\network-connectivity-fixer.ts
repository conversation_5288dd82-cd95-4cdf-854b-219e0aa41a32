import { supabase } from '@/integrations/supabase/client';

export class NetworkConnectivityFixer {
  private maxRetries = 3;
  private retryDelay = 1000; // 1 second
  private backoffMultiplier = 2;

  async testConnection(): Promise<{ success: boolean; message: string; details: string[] }> {
    const details: string[] = [];
    
    try {
      details.push('Testing Supabase connection...');
      
      // Test basic connection
      const { data, error } = await supabase
        .from('profiles')
        .select('id')
        .limit(1);
      
      if (error) {
        details.push(`Connection test failed: ${error.message}`);
        return {
          success: false,
          message: 'Supabase connection test failed',
          details
        };
      }
      
      details.push('✓ Basic connection successful');
      
      // Test auth status
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError) {
        details.push(`Auth test warning: ${authError.message}`);
      } else {
        details.push(`✓ Auth status: ${user ? 'Authenticated' : 'Not authenticated'}`);
      }
      
      return {
        success: true,
        message: 'Network connectivity test passed',
        details
      };
      
    } catch (error) {
      details.push(`Connection test error: ${error}`);
      return {
        success: false,
        message: `Network connectivity test failed: ${error}`,
        details
      };
    }
  }

  async retryWithBackoff<T>(
    operation: () => Promise<T>,
    operationName: string
  ): Promise<{ success: boolean; data?: T; error?: string; attempts: number }> {
    let lastError: any;
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const result = await operation();
        return {
          success: true,
          data: result,
          attempts: attempt
        };
      } catch (error) {
        lastError = error;
        console.warn(`${operationName} attempt ${attempt} failed:`, error);
        
        if (attempt < this.maxRetries) {
          const delay = this.retryDelay * Math.pow(this.backoffMultiplier, attempt - 1);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    return {
      success: false,
      error: `${operationName} failed after ${this.maxRetries} attempts: ${lastError}`,
      attempts: this.maxRetries
    };
  }

  async diagnoseNetworkIssues(): Promise<{ success: boolean; message: string; details: string[] }> {
    const details: string[] = [];
    
    try {
      details.push('Starting network diagnostics...');
      
      // Check if we can reach the Supabase URL
      const supabaseUrl = supabase.supabaseUrl;
      details.push(`Supabase URL: ${supabaseUrl}`);
      
      // Test basic connectivity with retry
      const connectivityTest = await this.retryWithBackoff(
        () => this.testConnection(),
        'Connectivity Test'
      );
      
      if (connectivityTest.success && connectivityTest.data?.success) {
        details.push('✓ Network connectivity is working');
        details.push(...(connectivityTest.data.details || []));
        
        return {
          success: true,
          message: 'Network diagnostics completed - connectivity is working',
          details
        };
      } else {
        details.push('✗ Network connectivity issues detected');
        details.push(connectivityTest.error || 'Unknown connectivity error');
        
        // Provide troubleshooting suggestions
        details.push('');
        details.push('Troubleshooting suggestions:');
        details.push('1. Check internet connection');
        details.push('2. Verify Supabase service status');
        details.push('3. Check firewall/proxy settings');
        details.push('4. Try refreshing the page');
        details.push('5. Check if Supabase URL is accessible');
        
        return {
          success: false,
          message: 'Network connectivity issues detected',
          details
        };
      }
      
    } catch (error) {
      details.push(`Diagnostics error: ${error}`);
      return {
        success: false,
        message: `Network diagnostics failed: ${error}`,
        details
      };
    }
  }

  async fixNetworkIssues(): Promise<{ success: boolean; message: string; details: string[] }> {
    const details: string[] = [];
    
    try {
      details.push('Attempting to fix network issues...');
      
      // First, run diagnostics
      const diagnostics = await this.diagnoseNetworkIssues();
      details.push(...diagnostics.details);
      
      if (diagnostics.success) {
        details.push('✓ No network fixes needed - connectivity is working');
        return {
          success: true,
          message: 'Network connectivity is working properly',
          details
        };
      }
      
      // Try to reinitialize the Supabase client
      details.push('Attempting to reinitialize Supabase client...');
      
      // Test with a simple query after a brief delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const retryTest = await this.retryWithBackoff(
        async () => {
          const { data, error } = await supabase
            .from('profiles')
            .select('id')
            .limit(1);
          
          if (error) throw error;
          return data;
        },
        'Retry Connection Test'
      );
      
      if (retryTest.success) {
        details.push('✓ Connection restored after retry');
        return {
          success: true,
          message: 'Network connectivity restored',
          details
        };
      } else {
        details.push('✗ Unable to restore connection automatically');
        details.push('Manual intervention may be required');
        
        return {
          success: false,
          message: 'Unable to fix network issues automatically',
          details
        };
      }
      
    } catch (error) {
      details.push(`Fix attempt error: ${error}`);
      return {
        success: false,
        message: `Network fix failed: ${error}`,
        details
      };
    }
  }

  // Enhanced query wrapper with automatic retry
  async executeWithRetry<T>(
    queryFn: () => Promise<{ data: T | null; error: any }>,
    queryName: string = 'Database Query'
  ): Promise<{ data: T | null; error: any; retried: boolean }> {
    const result = await this.retryWithBackoff(
      async () => {
        const { data, error } = await queryFn();
        if (error) throw error;
        return data;
      },
      queryName
    );
    
    if (result.success) {
      return {
        data: result.data || null,
        error: null,
        retried: result.attempts > 1
      };
    } else {
      return {
        data: null,
        error: new Error(result.error),
        retried: true
      };
    }
  }
}

export const networkConnectivityFixer = new NetworkConnectivityFixer();

// Enhanced Supabase client wrapper with automatic retry
export const enhancedSupabase = {
  ...supabase,
  
  // Override the from method to add retry logic
  from: (table: string) => {
    const originalFrom = supabase.from(table);
    
    return {
      ...originalFrom,
      
      // Override select with retry logic
      select: (columns?: string) => {
        const query = originalFrom.select(columns);
        
        // Add retry wrapper to the query execution
        const originalThen = query.then;
        query.then = function(onfulfilled, onrejected) {
          return networkConnectivityFixer.executeWithRetry(
            () => originalThen.call(this),
            `SELECT from ${table}`
          ).then(result => {
            if (result.error) {
              return onrejected ? onrejected(result.error) : Promise.reject(result.error);
            }
            return onfulfilled ? onfulfilled({ data: result.data, error: null }) : { data: result.data, error: null };
          });
        };
        
        return query;
      }
    };
  }
};