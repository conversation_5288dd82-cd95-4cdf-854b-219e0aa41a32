/**
 * LangChain Configuration Service
 * Centralized configuration management for all LangChain features
 */

import { supabase } from '@/integrations/supabase/client';

export interface LangChainConfig {
  openai: {
    apiKey: string;
    model: string;
    temperature: number;
    maxTokens: number;
  };
  embeddings: {
    model: string;
    dimensions: number;
  };
  vectorStore: {
    tableName: string;
    queryName: string;
    similarityThreshold: number;
  };
  memory: {
    maxTokens: number;
    returnMessages: number;
  };
  agent: {
    maxIterations: number;
    verbose: boolean;
  };
  rag: {
    chunkSize: number;
    chunkOverlap: number;
    maxResults: number;
  };
}

export class LangChainConfigService {
  private static config: LangChainConfig | null = null;
  private static initialized = false;

  /**
   * Initialize configuration from environment and database
   */
  static async initialize(): Promise<LangChainConfig> {
    if (this.initialized && this.config) {
      return this.config;
    }

    try {
      // Try to get API keys from database first
      let openaiKey: string | undefined;

      try {
        const { data: apiKeys } = await supabase
          .from('api_keys')
          .select('provider, api_key, is_active')
          .eq('is_active', true);

        openaiKey = apiKeys?.find(key => key.provider === 'openai')?.api_key;
      } catch (dbError) {
        console.warn('Could not fetch API keys from database:', dbError);
      }

      // Fallback to environment variables if not found in database
      if (!openaiKey) {
        openaiKey = import.meta.env.VITE_OPENAI_API_KEY || import.meta.env.OPENAI_API_KEY;
        console.log('Using OpenAI API key from environment variables');
      }

      if (!openaiKey) {
        throw new Error('OpenAI API key not found in database or environment variables');
      }

      this.config = {
        openai: {
          apiKey: openaiKey,
          model: 'gpt-4o-mini',
          temperature: 0.7,
          maxTokens: 4096,
        },
        embeddings: {
          model: 'text-embedding-3-small',
          dimensions: 1536,
        },
        vectorStore: {
          tableName: 'langchain_documents',
          queryName: 'match_documents',
          similarityThreshold: 0.7,
        },
        memory: {
          maxTokens: 2000,
          returnMessages: 10,
        },
        agent: {
          maxIterations: 10,
          verbose: false,
        },
        rag: {
          chunkSize: 1000,
          chunkOverlap: 200,
          maxResults: 5,
        },
      };

      this.initialized = true;
      return this.config;
    } catch (error) {
      console.error('Failed to initialize LangChain config:', error);
      throw error;
    }
  }

  /**
   * Get current configuration
   */
  static getConfig(): LangChainConfig {
    if (!this.config) {
      throw new Error('LangChain configuration not initialized');
    }
    return this.config;
  }

  /**
   * Update configuration
   */
  static async updateConfig(updates: Partial<LangChainConfig>): Promise<void> {
    if (!this.config) {
      await this.initialize();
    }

    this.config = { ...this.config!, ...updates };
  }

  /**
   * Validate configuration
   */
  static validateConfig(config: LangChainConfig): boolean {
    return !!(
      config.openai.apiKey &&
      config.embeddings.model &&
      config.vectorStore.tableName
    );
  }

  /**
   * Get environment-specific settings
   */
  static getEnvironmentConfig(): Partial<LangChainConfig> {
    const isDevelopment = process.env.NODE_ENV === 'development';
    
    return {
      agent: {
        maxIterations: isDevelopment ? 5 : 10,
        verbose: isDevelopment,
      },
      openai: {
        temperature: isDevelopment ? 0.3 : 0.7,
        maxTokens: isDevelopment ? 2048 : 4096,
      },
    };
  }
}

export const langChainConfig = LangChainConfigService;
