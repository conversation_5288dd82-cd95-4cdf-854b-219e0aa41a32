import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Wifi, WifiOff, RefreshCw, CheckCircle, XCircle } from 'lucide-react';
import { networkConnectivityFixer } from '@/utils/network-connectivity-fixer';

interface DiagnosticResult {
  success: boolean;
  message: string;
  details: string[];
}

export function NetworkConnectivityFix() {
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [isDiagnosing, setIsDiagnosing] = useState(false);
  const [isFixing, setIsFixing] = useState(false);
  const [connectionResult, setConnectionResult] = useState<DiagnosticResult | null>(null);
  const [diagnosticResult, setDiagnosticResult] = useState<DiagnosticResult | null>(null);
  const [fixResult, setFixResult] = useState<DiagnosticResult | null>(null);

  const handleTestConnection = async () => {
    setIsTestingConnection(true);
    setConnectionResult(null);
    
    try {
      const result = await networkConnectivityFixer.testConnection();
      setConnectionResult(result);
    } catch (error) {
      setConnectionResult({
        success: false,
        message: `Connection test failed: ${error}`,
        details: []
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleDiagnoseNetwork = async () => {
    setIsDiagnosing(true);
    setDiagnosticResult(null);
    
    try {
      const result = await networkConnectivityFixer.diagnoseNetworkIssues();
      setDiagnosticResult(result);
    } catch (error) {
      setDiagnosticResult({
        success: false,
        message: `Diagnostics failed: ${error}`,
        details: []
      });
    } finally {
      setIsDiagnosing(false);
    }
  };

  const handleFixNetwork = async () => {
    setIsFixing(true);
    setFixResult(null);
    
    try {
      const result = await networkConnectivityFixer.fixNetworkIssues();
      setFixResult(result);
    } catch (error) {
      setFixResult({
        success: false,
        message: `Fix attempt failed: ${error}`,
        details: []
      });
    } finally {
      setIsFixing(false);
    }
  };

  const renderResult = (result: DiagnosticResult | null, title: string) => {
    if (!result) return null;

    return (
      <Alert className={result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
        <div className="flex items-center gap-2">
          {result.success ? (
            <CheckCircle className="h-4 w-4 text-green-600" />
          ) : (
            <XCircle className="h-4 w-4 text-red-600" />
          )}
          <div className="flex-1">
            <div className="font-medium">{title}</div>
            <AlertDescription className="mt-1">
              {result.message}
            </AlertDescription>
            {result.details.length > 0 && (
              <div className="mt-2 space-y-1">
                {result.details.map((detail, index) => (
                  <div key={index} className="text-sm text-gray-600 font-mono">
                    {detail}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </Alert>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Wifi className="h-5 w-5" />
          Network Connectivity Diagnostics
        </CardTitle>
        <CardDescription>
          Test and fix Supabase network connectivity issues. Use this when experiencing ERR_QUIC_PROTOCOL_ERROR, 
          ERR_NETWORK_CHANGED, ERR_ADDRESS_UNREACHABLE, or ERR_NAME_NOT_RESOLVED errors.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button
            onClick={handleTestConnection}
            disabled={isTestingConnection}
            variant="outline"
            className="w-full"
          >
            {isTestingConnection ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Wifi className="h-4 w-4 mr-2" />
            )}
            Test Connection
          </Button>
          
          <Button
            onClick={handleDiagnoseNetwork}
            disabled={isDiagnosing}
            variant="outline"
            className="w-full"
          >
            {isDiagnosing ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Diagnose Issues
          </Button>
          
          <Button
            onClick={handleFixNetwork}
            disabled={isFixing}
            variant="default"
            className="w-full"
          >
            {isFixing ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <WifiOff className="h-4 w-4 mr-2" />
            )}
            Fix Network Issues
          </Button>
        </div>

        <div className="space-y-4">
          {renderResult(connectionResult, 'Connection Test Result')}
          {renderResult(diagnosticResult, 'Network Diagnostics Result')}
          {renderResult(fixResult, 'Network Fix Result')}
        </div>

        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">What this component does:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• <strong>Test Connection:</strong> Verifies basic Supabase connectivity</li>
            <li>• <strong>Diagnose Issues:</strong> Identifies network problems and provides troubleshooting steps</li>
            <li>• <strong>Fix Network Issues:</strong> Attempts automatic recovery with retry logic</li>
          </ul>
          
          <h4 className="font-medium text-blue-900 mt-4 mb-2">Common network errors addressed:</h4>
          <div className="flex flex-wrap gap-2">
            <Badge variant="secondary">ERR_QUIC_PROTOCOL_ERROR</Badge>
            <Badge variant="secondary">ERR_NETWORK_CHANGED</Badge>
            <Badge variant="secondary">ERR_ADDRESS_UNREACHABLE</Badge>
            <Badge variant="secondary">ERR_NAME_NOT_RESOLVED</Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}