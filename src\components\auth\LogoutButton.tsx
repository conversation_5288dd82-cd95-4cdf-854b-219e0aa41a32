import { useAuth } from "@/components/auth/AuthProvider";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { LogOut } from "lucide-react";
import { useNavigate } from "react-router-dom";

interface LogoutButtonProps {
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  className?: string;
  showText?: boolean;
  style?: React.CSSProperties;
}

export const LogoutButton = ({ variant = "destructive", className, showText = true, style }: LogoutButtonProps) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { signOut } = useAuth();

  const handleLogout = async () => {
    try {
      await signOut();
      navigate("/auth");
      toast({
        title: "Logged Out",
        description: "You have been successfully logged out.",
      });
    } catch (error) {
      console.error("Logout error:", error);
      toast({
        title: "Logout Failed",
        description: "Failed to log out. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <Button
      variant={variant}
      onClick={handleLogout}
      className={`w-full justify-start gap-2 ${className || ''}`}
      style={style}
    >
      <LogOut className={`w-4 h-4 ${showText ? 'mr-2' : ''}`} />
      {showText && 'Logout'}
    </Button>
  );
};
