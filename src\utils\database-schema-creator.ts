/**
 * Database Schema Creator
 * Creates missing database tables and fixes schema issues
 */

import { supabase } from '@/integrations/supabase/client';

export class DatabaseSchemaCreator {
  /**
   * Create all missing database tables
   */
  static async createMissingTables(): Promise<{ success: boolean; errors: string[] }> {
    const errors: string[] = [];
    console.log('🗄️ Creating missing database tables...');

    try {
      // Create departments table
      await this.createDepartmentsTable();
      console.log('✅ Departments table created');
    } catch (error: any) {
      errors.push(`Departments table: ${error.message}`);
    }

    try {
      // Create document_archive table
      await this.createDocumentArchiveTable();
      console.log('✅ Document archive table created');
    } catch (error: any) {
      errors.push(`Document archive table: ${error.message}`);
    }

    try {
      // Create task_assignments table
      await this.createTaskAssignmentsTable();
      console.log('✅ Task assignments table created');
    } catch (error: any) {
      errors.push(`Task assignments table: ${error.message}`);
    }

    try {
      // Create project_assignments table
      await this.createProjectAssignmentsTable();
      console.log('✅ Project assignments table created');
    } catch (error: any) {
      errors.push(`Project assignments table: ${error.message}`);
    }

    try {
      // Create notifications table
      await this.createNotificationsTable();
      console.log('✅ Notifications table created');
    } catch (error: any) {
      errors.push(`Notifications table: ${error.message}`);
    }

    try {
      // Create time_logs table
      await this.createTimeLogsTable();
      console.log('✅ Time logs table created');
    } catch (error: any) {
      errors.push(`Time logs table: ${error.message}`);
    }

    return {
      success: errors.length === 0,
      errors
    };
  }

  /**
   * Create departments table
   */
  private static async createDepartmentsTable(): Promise<void> {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS public.departments (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          name TEXT NOT NULL UNIQUE,
          description TEXT,
          manager_id UUID REFERENCES auth.users(id),
          budget DECIMAL(15,2),
          location TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Enable RLS
        ALTER TABLE public.departments ENABLE ROW LEVEL SECURITY;

        -- Create policies
        CREATE POLICY "Enable read access for all users" ON public.departments
          FOR SELECT USING (true);

        CREATE POLICY "Enable insert for authenticated users" ON public.departments
          FOR INSERT WITH CHECK (auth.role() = 'authenticated');

        CREATE POLICY "Enable update for authenticated users" ON public.departments
          FOR UPDATE USING (auth.role() = 'authenticated');
      `
    });

    if (error) throw error;
  }

  /**
   * Create document_archive table
   */
  private static async createDocumentArchiveTable(): Promise<void> {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS public.document_archive (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          title TEXT NOT NULL,
          file_name TEXT NOT NULL,
          file_path TEXT,
          file_size INTEGER,
          file_type TEXT,
          file_url TEXT,
          description TEXT,
          tags TEXT[],
          category TEXT,
          uploaded_by UUID REFERENCES auth.users(id),
          project_id UUID REFERENCES public.projects(id),
          department_id UUID REFERENCES public.departments(id),
          is_public BOOLEAN DEFAULT false,
          download_count INTEGER DEFAULT 0,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Enable RLS
        ALTER TABLE public.document_archive ENABLE ROW LEVEL SECURITY;

        -- Create policies
        CREATE POLICY "Enable read access for all users" ON public.document_archive
          FOR SELECT USING (true);

        CREATE POLICY "Enable insert for authenticated users" ON public.document_archive
          FOR INSERT WITH CHECK (auth.role() = 'authenticated');

        CREATE POLICY "Enable update for document owners" ON public.document_archive
          FOR UPDATE USING (auth.uid() = uploaded_by);
      `
    });

    if (error) throw error;
  }

  /**
   * Create task_assignments table
   */
  private static async createTaskAssignmentsTable(): Promise<void> {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS public.task_assignments (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          task_id UUID REFERENCES public.tasks(id) ON DELETE CASCADE,
          assigned_to UUID REFERENCES auth.users(id),
          assigned_by UUID REFERENCES auth.users(id),
          role TEXT DEFAULT 'assignee' CHECK (role IN ('assignee', 'reviewer', 'collaborator', 'observer')),
          status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
          assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          completed_at TIMESTAMP WITH TIME ZONE,
          notes TEXT,
          metadata JSONB DEFAULT '{}',
          UNIQUE(task_id, assigned_to, role)
        );

        -- Enable RLS
        ALTER TABLE public.task_assignments ENABLE ROW LEVEL SECURITY;

        -- Create policies
        CREATE POLICY "Enable read access for all users" ON public.task_assignments
          FOR SELECT USING (true);

        CREATE POLICY "Enable insert for authenticated users" ON public.task_assignments
          FOR INSERT WITH CHECK (auth.role() = 'authenticated');

        CREATE POLICY "Enable update for assigned users" ON public.task_assignments
          FOR UPDATE USING (auth.uid() = assigned_to OR auth.uid() = assigned_by);
      `
    });

    if (error) throw error;
  }

  /**
   * Create project_assignments table
   */
  private static async createProjectAssignmentsTable(): Promise<void> {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS public.project_assignments (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
          assigned_to UUID REFERENCES auth.users(id),
          assigned_by UUID REFERENCES auth.users(id),
          role TEXT DEFAULT 'member' CHECK (role IN ('manager', 'lead', 'member', 'consultant', 'observer')),
          status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
          start_date DATE,
          end_date DATE,
          hourly_rate DECIMAL(10,2),
          assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          notes TEXT,
          metadata JSONB DEFAULT '{}',
          UNIQUE(project_id, assigned_to)
        );

        -- Enable RLS
        ALTER TABLE public.project_assignments ENABLE ROW LEVEL SECURITY;

        -- Create policies
        CREATE POLICY "Enable read access for all users" ON public.project_assignments
          FOR SELECT USING (true);

        CREATE POLICY "Enable insert for authenticated users" ON public.project_assignments
          FOR INSERT WITH CHECK (auth.role() = 'authenticated');

        CREATE POLICY "Enable update for assigned users" ON public.project_assignments
          FOR UPDATE USING (auth.uid() = assigned_to OR auth.uid() = assigned_by);
      `
    });

    if (error) throw error;
  }

  /**
   * Create notifications table
   */
  private static async createNotificationsTable(): Promise<void> {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS public.notifications (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
          title TEXT NOT NULL,
          message TEXT,
          type TEXT DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
          category TEXT DEFAULT 'general' CHECK (category IN ('general', 'task', 'project', 'system', 'reminder')),
          data JSONB DEFAULT '{}',
          read BOOLEAN DEFAULT FALSE,
          read_at TIMESTAMP WITH TIME ZONE,
          action_url TEXT,
          expires_at TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Enable RLS
        ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

        -- Create policies
        CREATE POLICY "Users can view their own notifications" ON public.notifications
          FOR SELECT USING (auth.uid() = user_id);

        CREATE POLICY "Enable insert for authenticated users" ON public.notifications
          FOR INSERT WITH CHECK (auth.role() = 'authenticated');

        CREATE POLICY "Users can update their own notifications" ON public.notifications
          FOR UPDATE USING (auth.uid() = user_id);
      `
    });

    if (error) throw error;
  }

  /**
   * Create time_logs table
   */
  private static async createTimeLogsTable(): Promise<void> {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS public.time_logs (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES auth.users(id),
          project_id UUID REFERENCES public.projects(id),
          task_id UUID REFERENCES public.tasks(id),
          description TEXT,
          start_time TIMESTAMP WITH TIME ZONE NOT NULL,
          end_time TIMESTAMP WITH TIME ZONE,
          duration_minutes INTEGER,
          is_billable BOOLEAN DEFAULT true,
          hourly_rate DECIMAL(10,2),
          status TEXT DEFAULT 'active' CHECK (status IN ('active', 'paused', 'completed')),
          tags TEXT[],
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Enable RLS
        ALTER TABLE public.time_logs ENABLE ROW LEVEL SECURITY;

        -- Create policies
        CREATE POLICY "Users can view their own time logs" ON public.time_logs
          FOR SELECT USING (auth.uid() = user_id);

        CREATE POLICY "Users can insert their own time logs" ON public.time_logs
          FOR INSERT WITH CHECK (auth.uid() = user_id);

        CREATE POLICY "Users can update their own time logs" ON public.time_logs
          FOR UPDATE USING (auth.uid() = user_id);
      `
    });

    if (error) throw error;
  }

  /**
   * Create indexes for better performance
   */
  static async createIndexes(): Promise<{ success: boolean; errors: string[] }> {
    const errors: string[] = [];
    console.log('📊 Creating database indexes...');

    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_document_archive_uploaded_by ON public.document_archive(uploaded_by);',
      'CREATE INDEX IF NOT EXISTS idx_document_archive_project_id ON public.document_archive(project_id);',
      'CREATE INDEX IF NOT EXISTS idx_task_assignments_task_id ON public.task_assignments(task_id);',
      'CREATE INDEX IF NOT EXISTS idx_task_assignments_assigned_to ON public.task_assignments(assigned_to);',
      'CREATE INDEX IF NOT EXISTS idx_project_assignments_project_id ON public.project_assignments(project_id);',
      'CREATE INDEX IF NOT EXISTS idx_project_assignments_assigned_to ON public.project_assignments(assigned_to);',
      'CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);',
      'CREATE INDEX IF NOT EXISTS idx_notifications_read ON public.notifications(read);',
      'CREATE INDEX IF NOT EXISTS idx_time_logs_user_id ON public.time_logs(user_id);',
      'CREATE INDEX IF NOT EXISTS idx_time_logs_project_id ON public.time_logs(project_id);',
      'CREATE INDEX IF NOT EXISTS idx_time_logs_task_id ON public.time_logs(task_id);'
    ];

    for (const indexSql of indexes) {
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: indexSql });
        if (error) throw error;
      } catch (error: any) {
        errors.push(`Index creation failed: ${error.message}`);
      }
    }

    console.log(`✅ Created ${indexes.length - errors.length} indexes`);
    return {
      success: errors.length === 0,
      errors
    };
  }

  /**
   * Verify all tables exist
   */
  static async verifyTables(): Promise<{ success: boolean; missingTables: string[] }> {
    const requiredTables = [
      'profiles',
      'departments',
      'projects',
      'tasks',
      'invoices',
      'memos',
      'time_logs',
      'project_assignments',
      'task_assignments',
      'document_archive',
      'notifications'
    ];

    const missingTables: string[] = [];

    for (const table of requiredTables) {
      try {
        const { error } = await supabase
          .from(table)
          .select('*')
          .limit(1);

        if (error && error.message.includes('does not exist')) {
          missingTables.push(table);
        }
      } catch (err) {
        missingTables.push(table);
      }
    }

    return {
      success: missingTables.length === 0,
      missingTables
    };
  }
}

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).DatabaseSchemaCreator = DatabaseSchemaCreator;
  (window as any).createMissingTables = () => DatabaseSchemaCreator.createMissingTables();
  (window as any).verifyTables = () => DatabaseSchemaCreator.verifyTables();
  
  console.log('🗄️ Database Schema Creator loaded. Available commands:');
  console.log('  - createMissingTables()');
  console.log('  - verifyTables()');
}
