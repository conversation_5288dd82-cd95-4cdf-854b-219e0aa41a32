/**
 * System Analysis Report Generator
 * Generates comprehensive reports for all system issues
 */

import { ComprehensiveCodeAnalyzer } from './comprehensive-code-analyzer';

export class SystemAnalysisReport {
  /**
   * Generate complete system analysis report
   */
  static async generateCompleteReport(): Promise<string> {
    console.log('📊 Generating complete system analysis report...');
    
    const analysis = await ComprehensiveCodeAnalyzer.runFullAnalysis();
    
    let report = '';
    
    // Header
    report += '# 🔍 COMPLETE SYSTEM ANALYSIS REPORT\n';
    report += `Generated: ${new Date().toISOString()}\n\n`;
    
    // Executive Summary
    report += this.generateExecutiveSummary(analysis);
    
    // Critical Issues
    report += this.generateCriticalIssues(analysis);
    
    // Database Analysis
    report += this.generateDatabaseAnalysis(analysis);
    
    // Foreign Key Issues
    report += this.generateForeignKeyAnalysis(analysis);
    
    // Routing and Pages Analysis
    report += this.generateRoutingAnalysis(analysis);
    
    // Missing Components
    report += this.generateMissingComponentsAnalysis(analysis);
    
    // Recommendations
    report += this.generateRecommendations(analysis);
    
    // Implementation Plan
    report += this.generateImplementationPlan(analysis);
    
    console.log('✅ Complete system analysis report generated');
    return report;
  }

  /**
   * Generate executive summary
   */
  private static generateExecutiveSummary(analysis: any): string {
    let summary = '## 📋 EXECUTIVE SUMMARY\n\n';
    
    summary += `### System Health Overview\n`;
    summary += `- **Total Issues Found**: ${analysis.summary.totalIssues}\n`;
    summary += `- **Critical Issues**: ${analysis.summary.criticalIssues} 🔴\n`;
    summary += `- **High Priority Issues**: ${analysis.summary.highPriorityIssues} 🟡\n`;
    summary += `- **Database Health**: ${this.getHealthEmoji(analysis.summary.databaseHealth)} ${analysis.summary.databaseHealth.toUpperCase()}\n`;
    summary += `- **Routing Health**: ${this.getHealthEmoji(analysis.summary.routingHealth)} ${analysis.summary.routingHealth.toUpperCase()}\n\n`;
    
    summary += `### Key Findings\n`;
    summary += `- **Database Issues**: ${analysis.databaseIssues.length} issues found\n`;
    summary += `- **Missing Tables**: ${analysis.databaseIssues.filter((i: any) => i.type === 'missing_table').length}\n`;
    summary += `- **Missing Columns**: ${analysis.databaseIssues.filter((i: any) => i.type === 'missing_column').length}\n`;
    summary += `- **Foreign Key Issues**: ${analysis.brokenForeignKeys.length}\n`;
    summary += `- **Missing Pages**: ${analysis.missingPages.length}\n`;
    summary += `- **Missing Components**: ${analysis.missingComponents.length}\n\n`;
    
    return summary;
  }

  /**
   * Generate critical issues section
   */
  private static generateCriticalIssues(analysis: any): string {
    let critical = '## 🔴 CRITICAL ISSUES (IMMEDIATE ACTION REQUIRED)\n\n';
    
    const criticalIssues = [...analysis.databaseIssues, ...analysis.routeIssues]
      .filter((issue: any) => issue.severity === 'critical');
    
    if (criticalIssues.length === 0) {
      critical += '✅ **No critical issues found!**\n\n';
      return critical;
    }
    
    criticalIssues.forEach((issue: any, index: number) => {
      critical += `### ${index + 1}. ${issue.type.replace('_', ' ').toUpperCase()}\n`;
      critical += `- **Location**: ${issue.table || issue.route}\n`;
      critical += `- **Error**: ${issue.error}\n`;
      critical += `- **Impact**: System functionality severely affected\n`;
      critical += `- **Fix**: ${issue.fix}\n\n`;
    });
    
    return critical;
  }

  /**
   * Generate database analysis section
   */
  private static generateDatabaseAnalysis(analysis: any): string {
    let db = '## 🗄️ DATABASE ANALYSIS\n\n';
    
    // Missing Tables
    const missingTables = analysis.databaseIssues.filter((i: any) => i.type === 'missing_table');
    if (missingTables.length > 0) {
      db += `### Missing Tables (${missingTables.length})\n`;
      missingTables.forEach((issue: any) => {
        db += `- **${issue.table}**: ${issue.error}\n`;
        db += `  - Fix: ${issue.fix}\n`;
      });
      db += '\n';
    }
    
    // Missing Columns
    const missingColumns = analysis.databaseIssues.filter((i: any) => i.type === 'missing_column');
    if (missingColumns.length > 0) {
      db += `### Missing Columns (${missingColumns.length})\n`;
      const columnsByTable = missingColumns.reduce((acc: any, issue: any) => {
        if (!acc[issue.table]) acc[issue.table] = [];
        acc[issue.table].push(issue.column);
        return acc;
      }, {});
      
      Object.entries(columnsByTable).forEach(([table, columns]: [string, any]) => {
        db += `- **${table}**: Missing columns: ${columns.join(', ')}\n`;
      });
      db += '\n';
    }
    
    // Schema Mismatches
    const schemaMismatches = analysis.databaseIssues.filter((i: any) => i.type === 'schema_mismatch');
    if (schemaMismatches.length > 0) {
      db += `### Schema Mismatches (${schemaMismatches.length})\n`;
      schemaMismatches.forEach((issue: any) => {
        db += `- **${issue.table}**: ${issue.error}\n`;
      });
      db += '\n';
    }
    
    return db;
  }

  /**
   * Generate foreign key analysis section
   */
  private static generateForeignKeyAnalysis(analysis: any): string {
    let fk = '## 🔗 FOREIGN KEY ANALYSIS\n\n';
    
    if (analysis.brokenForeignKeys.length === 0) {
      fk += '✅ **All foreign key relationships are working correctly!**\n\n';
      return fk;
    }
    
    fk += `### Broken Foreign Key Relationships (${analysis.brokenForeignKeys.length})\n`;
    
    const fkIssues = analysis.databaseIssues.filter((i: any) => 
      i.type === 'missing_fkey' || i.type === 'broken_fkey'
    );
    
    fkIssues.forEach((issue: any) => {
      fk += `- **${issue.table}.${issue.column}** → **${issue.referencedTable}.${issue.referencedColumn}**\n`;
      fk += `  - Error: ${issue.error}\n`;
      fk += `  - Fix: ${issue.fix}\n`;
    });
    
    fk += '\n### Impact on Queries\n';
    fk += 'These foreign key issues affect:\n';
    fk += '- Join operations in API queries\n';
    fk += '- Data integrity constraints\n';
    fk += '- Automatic relationship resolution\n';
    fk += '- Performance of complex queries\n\n';
    
    return fk;
  }

  /**
   * Generate routing analysis section
   */
  private static generateRoutingAnalysis(analysis: any): string {
    let routing = '## 🛣️ ROUTING & PAGES ANALYSIS\n\n';
    
    if (analysis.missingPages.length === 0) {
      routing += '✅ **All routes have corresponding page components!**\n\n';
      return routing;
    }
    
    routing += `### Missing Pages (${analysis.missingPages.length})\n`;
    
    // Group by role/section
    const pagesBySection = analysis.routeIssues
      .filter((i: any) => i.type === 'missing_page')
      .reduce((acc: any, issue: any) => {
        const section = issue.route.split('/')[2] || 'general';
        if (!acc[section]) acc[section] = [];
        acc[section].push(issue);
        return acc;
      }, {});
    
    Object.entries(pagesBySection).forEach(([section, issues]: [string, any]) => {
      routing += `#### ${section.toUpperCase()} Section\n`;
      issues.forEach((issue: any) => {
        routing += `- **${issue.route}**: Missing ${issue.component}\n`;
        routing += `  - Severity: ${issue.severity}\n`;
        routing += `  - Fix: ${issue.fix}\n`;
      });
      routing += '\n';
    });
    
    return routing;
  }

  /**
   * Generate missing components analysis
   */
  private static generateMissingComponentsAnalysis(analysis: any): string {
    let components = '## 🧩 MISSING COMPONENTS ANALYSIS\n\n';
    
    const missingComponents = [...new Set(analysis.missingComponents)];
    
    if (missingComponents.length === 0) {
      components += '✅ **All required components are available!**\n\n';
      return components;
    }
    
    components += `### Components to Create (${missingComponents.length})\n`;
    
    // Categorize components
    const componentCategories = {
      'Admin': missingComponents.filter(c => c.includes('Admin') || c.includes('User') || c.includes('Department')),
      'Manager': missingComponents.filter(c => c.includes('Manager') || c.includes('Team') || c.includes('Project')),
      'Staff': missingComponents.filter(c => c.includes('Staff') || c.includes('Task') || c.includes('Time')),
      'Accountant': missingComponents.filter(c => c.includes('Accountant') || c.includes('Invoice') || c.includes('Financial')),
      'General': missingComponents.filter(c => 
        !c.includes('Admin') && !c.includes('Manager') && !c.includes('Staff') && 
        !c.includes('Accountant') && !c.includes('User') && !c.includes('Department') &&
        !c.includes('Team') && !c.includes('Project') && !c.includes('Task') &&
        !c.includes('Invoice') && !c.includes('Financial')
      )
    };
    
    Object.entries(componentCategories).forEach(([category, comps]: [string, any]) => {
      if (comps.length > 0) {
        components += `#### ${category} Components\n`;
        comps.forEach((comp: string) => {
          components += `- ${comp}\n`;
        });
        components += '\n';
      }
    });
    
    return components;
  }

  /**
   * Generate recommendations section
   */
  private static generateRecommendations(analysis: any): string {
    let recommendations = '## 💡 RECOMMENDATIONS\n\n';
    
    recommendations += '### Immediate Actions (Next 1-2 Days)\n';
    recommendations += '1. **Fix Critical Database Issues**: Address missing tables and critical schema mismatches\n';
    recommendations += '2. **Create Core Missing Pages**: Focus on admin, manager, and staff dashboard pages\n';
    recommendations += '3. **Implement Safe Query Patterns**: Use manual joins instead of foreign key relationships\n';
    recommendations += '4. **Add Error Boundaries**: Ensure missing components don\'t crash the application\n\n';
    
    recommendations += '### Short-term Actions (Next 1-2 Weeks)\n';
    recommendations += '1. **Database Schema Normalization**: Create all missing tables and columns\n';
    recommendations += '2. **Foreign Key Implementation**: Add proper foreign key constraints\n';
    recommendations += '3. **Complete Page Components**: Implement all missing page components\n';
    recommendations += '4. **Navigation Updates**: Ensure all sidebar links work correctly\n\n';
    
    recommendations += '### Long-term Actions (Next 1-2 Months)\n';
    recommendations += '1. **Database Optimization**: Add indexes and optimize query performance\n';
    recommendations += '2. **Component Library**: Create reusable components for consistency\n';
    recommendations += '3. **Testing Suite**: Add comprehensive tests for all components and APIs\n';
    recommendations += '4. **Documentation**: Document all APIs, components, and database schema\n\n';
    
    return recommendations;
  }

  /**
   * Generate implementation plan
   */
  private static generateImplementationPlan(analysis: any): string {
    let plan = '## 📅 IMPLEMENTATION PLAN\n\n';
    
    plan += '### Phase 1: Critical Fixes (Priority 1)\n';
    plan += '**Timeline**: 1-2 days\n';
    plan += '**Effort**: 8-16 hours\n\n';
    
    const criticalIssues = [...analysis.databaseIssues, ...analysis.routeIssues]
      .filter((issue: any) => issue.severity === 'critical');
    
    if (criticalIssues.length > 0) {
      plan += 'Critical issues to fix:\n';
      criticalIssues.forEach((issue: any, index: number) => {
        plan += `${index + 1}. ${issue.error}\n`;
      });
    } else {
      plan += '✅ No critical issues found!\n';
    }
    
    plan += '\n### Phase 2: High Priority Fixes (Priority 2)\n';
    plan += '**Timeline**: 3-7 days\n';
    plan += '**Effort**: 16-40 hours\n\n';
    
    const highPriorityIssues = [...analysis.databaseIssues, ...analysis.routeIssues]
      .filter((issue: any) => issue.severity === 'high');
    
    plan += `High priority issues to fix (${highPriorityIssues.length}):\n`;
    highPriorityIssues.slice(0, 10).forEach((issue: any, index: number) => {
      plan += `${index + 1}. ${issue.error}\n`;
    });
    
    if (highPriorityIssues.length > 10) {
      plan += `... and ${highPriorityIssues.length - 10} more\n`;
    }
    
    plan += '\n### Phase 3: Medium Priority Fixes (Priority 3)\n';
    plan += '**Timeline**: 1-2 weeks\n';
    plan += '**Effort**: 40-80 hours\n\n';
    
    const mediumPriorityIssues = [...analysis.databaseIssues, ...analysis.routeIssues]
      .filter((issue: any) => issue.severity === 'medium');
    
    plan += `Medium priority issues (${mediumPriorityIssues.length}):\n`;
    plan += '- Complete missing page components\n';
    plan += '- Fix remaining foreign key relationships\n';
    plan += '- Add missing database columns\n';
    plan += '- Implement error handling for missing components\n\n';
    
    plan += '### Success Metrics\n';
    plan += '- ✅ Zero critical issues\n';
    plan += '- ✅ All core pages accessible\n';
    plan += '- ✅ Database queries working without errors\n';
    plan += '- ✅ All navigation links functional\n';
    plan += '- ✅ No console errors on page load\n\n';
    
    return plan;
  }

  /**
   * Get health emoji
   */
  private static getHealthEmoji(health: string): string {
    switch (health) {
      case 'healthy': return '✅';
      case 'warning': return '⚠️';
      case 'critical': return '🔴';
      default: return '❓';
    }
  }
}

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).systemAnalysisReport = SystemAnalysisReport;
  (window as any).generateSystemReport = () => SystemAnalysisReport.generateCompleteReport();
  
  console.log('📊 System Analysis Report loaded. Available commands:');
  console.log('  - generateSystemReport()');
}
