
import React from "react";
import { useState } from "react";
import { ThemeSwitcher } from "@/components/ThemeSwitcher";
import { useAuth } from "@/components/auth/AuthProvider";
import { useToast } from "@/hooks/use-toast";
import { LoginForm } from "@/components/login/LoginForm";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { LoginHeader } from "@/components/login/LoginHeader";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Clock } from "lucide-react";

const Login = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { signIn } = useAuth();
  const [error, setError] = useState<string | null>(null);

  const handleLogin = async (email: string, password: string) => {
    try {
      console.log('🔐 Login page: Starting login process');
      const result = await signIn(email, password);

      if (result?.error) {
        throw result.error;
      }

      console.log('🔐 Login page: Login successful, navigating to /');
      // Navigate to main page, let the auth provider handle role-based routing
      navigate('/');

      toast({
        title: "Welcome back!",
        description: "You have successfully logged in.",
      });
    } catch (error) {
      console.error('🔐 Login page error:', error);
      setError(error instanceof Error ? error.message : 'Failed to login');
      toast({
        title: "Login Failed",
        description: error instanceof Error ? error.message : 'Failed to login',
        variant: "destructive",
      });
    }
  };

  return (
    <ErrorBoundary>
      <div className="min-h-screen flex flex-col items-center justify-center bg-background px-4 py-8 relative overflow-hidden">
        {/* Navbar with logo on the left */}
        <nav className="w-full flex items-center justify-between px-6 py-4 fixed top-0 left-0 z-50 bg-background/80 backdrop-blur-md border-b border-white/10">
          <div className="flex items-center">
            <img 
              src="/lovable-uploads/3e0c6645-67a5-4ece-879d-eb26fa386647.png" 
              alt="CTNL Logo" 
              className="h-12 w-auto"
            />
          </div>
          <div className="flex items-center gap-4">
            <ThemeSwitcher />
          </div>
        </nav>
        {/* Spacer for navbar */}
        <div className="h-20" />
        {/* Clock In Button above login card */}
        <div className="mb-6 w-full flex justify-center">
          <Button
            onClick={() => window.location.href = '/clock-in'}
            variant="outline"
            className="glassmorphism border-[#ff1c04]/30 hover:bg-[#ff1c04]/10 hover:border-[#ff1c04]/50 transition-all duration-300"
          >
            <Clock className="h-4 w-4 mr-2 text-[#ff1c04]" />
            Clock In
          </Button>
        </div>
        <div className="w-full max-w-[400px] glass-card relative z-10 
          border border-white/10 dark:border-white/5 
          shadow-2xl hover:shadow-primary/5 transition-all duration-300
          bg-white/10 dark:bg-black/20 backdrop-blur-xl rounded-xl p-6">
          {/* Logo removed from here */}
          {/* Platform write-ups removed */}
          <LoginForm onLogin={handleLogin} />
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default Login;
