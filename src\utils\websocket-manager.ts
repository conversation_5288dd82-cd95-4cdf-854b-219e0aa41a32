/**
 * WebSocket Connection Manager
 * Handles real-time connections with proper error handling and reconnection
 */

import { supabase } from '@/integrations/supabase/client';

export class WebSocketManager {
  private static instance: WebSocketManager;
  private channels: Map<string, any> = new Map();
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second

  static getInstance(): WebSocketManager {
    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager();
    }
    return WebSocketManager.instance;
  }

  /**
   * Initialize WebSocket connections with better error handling
   */
  async initialize(): Promise<void> {
    try {
      console.log('🔌 Initializing WebSocket connections...');

      // Check if user is authenticated before connecting
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        console.log('ℹ️ No active session, skipping WebSocket initialization');
        return;
      }

      // Disconnect any existing channels first
      await this.disconnect();

      // Wait a bit before reconnecting
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Initialize real-time subscriptions with timeout
      const initPromise = Promise.all([
        this.setupNotificationChannel(),
        this.setupActivityChannel()
      ]);

      // Set a timeout for initialization
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('WebSocket initialization timeout')), 10000);
      });

      await Promise.race([initPromise, timeoutPromise]);

      this.isConnected = true;
      this.reconnectAttempts = 0;
      console.log('✅ WebSocket connections initialized');
    } catch (error) {
      console.warn('⚠️ WebSocket initialization failed, continuing without real-time features:', error);
      // Don't treat this as a critical error
      this.isConnected = false;
    }
  }

  /**
   * Setup notification channel
   */
  private async setupNotificationChannel(): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        console.log('ℹ️ No authenticated user, skipping notification channel');
        return;
      }

      const channel = supabase
        .channel('notifications')
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'notifications',
            filter: `user_id=eq.${user.id}`
          },
          (payload) => {
            console.log('🔔 New notification:', payload);
            this.handleNotification(payload.new);
          }
        )
        .on('presence', { event: 'sync' }, () => {
          console.log('👥 Presence sync');
        })
        .subscribe((status) => {
          console.log('📡 Notification channel status:', status);
          if (status === 'SUBSCRIBED') {
            console.log('✅ Notification channel connected');
          } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT' || status === 'CLOSED') {
            console.warn('⚠️ Notification channel issue:', status);
            // Don't immediately retry on errors, let it fail gracefully
            if (status === 'CHANNEL_ERROR') {
              this.handleChannelError('notifications');
            }
          }
        });

      this.channels.set('notifications', channel);
    } catch (error) {
      console.error('❌ Notification channel setup failed:', error);
    }
  }

  /**
   * Setup activity channel
   */
  private async setupActivityChannel(): Promise<void> {
    try {
      const channel = supabase
        .channel('activities')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'activity_logs'
          },
          (payload) => {
            console.log('📊 Activity update:', payload);
            this.handleActivityUpdate(payload);
          }
        )
        .subscribe((status) => {
          console.log('📡 Activity channel status:', status);
          if (status === 'SUBSCRIBED') {
            console.log('✅ Activity channel connected');
          } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT' || status === 'CLOSED') {
            console.warn('⚠️ Activity channel issue:', status);
            // Don't immediately retry on errors, let it fail gracefully
            if (status === 'CHANNEL_ERROR') {
              this.handleChannelError('activities');
            }
          }
        });

      this.channels.set('activities', channel);
    } catch (error) {
      console.error('❌ Activity channel setup failed:', error);
    }
  }

  /**
   * Handle new notifications
   */
  private handleNotification(notification: any): void {
    try {
      // Dispatch custom event for notification
      const event = new CustomEvent('newNotification', {
        detail: notification
      });
      window.dispatchEvent(event);

      // Show browser notification if permission granted
      if (Notification.permission === 'granted') {
        new Notification(notification.title, {
          body: notification.message,
          icon: '/icon.png'
        });
      }
    } catch (error) {
      console.error('❌ Notification handling failed:', error);
    }
  }

  /**
   * Handle activity updates
   */
  private handleActivityUpdate(payload: any): void {
    try {
      // Dispatch custom event for activity update
      const event = new CustomEvent('activityUpdate', {
        detail: payload
      });
      window.dispatchEvent(event);
    } catch (error) {
      console.error('❌ Activity update handling failed:', error);
    }
  }

  /**
   * Handle channel errors
   */
  private handleChannelError(channelName: string): void {
    console.error(`❌ Channel error: ${channelName}`);
    
    // Remove failed channel
    const channel = this.channels.get(channelName);
    if (channel) {
      try {
        supabase.removeChannel(channel);
        this.channels.delete(channelName);
      } catch (error) {
        console.error(`❌ Failed to remove channel ${channelName}:`, error);
      }
    }

    // Attempt reconnection
    this.attemptReconnection(channelName);
  }

  /**
   * Handle connection errors
   */
  private handleConnectionError(): void {
    this.isConnected = false;
    
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.attemptReconnection();
    } else {
      console.error('❌ Max reconnection attempts reached, giving up');
    }
  }

  /**
   * Attempt to reconnect
   */
  private attemptReconnection(channelName?: string): void {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff
    
    console.log(`🔄 Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);
    
    setTimeout(async () => {
      try {
        if (channelName) {
          // Reconnect specific channel
          if (channelName === 'notifications') {
            await this.setupNotificationChannel();
          } else if (channelName === 'activities') {
            await this.setupActivityChannel();
          }
        } else {
          // Full reconnection
          await this.initialize();
        }
      } catch (error) {
        console.error('❌ Reconnection failed:', error);
        this.handleConnectionError();
      }
    }, delay);
  }

  /**
   * Disconnect all channels
   */
  async disconnect(): Promise<void> {
    try {
      console.log('🔌 Disconnecting WebSocket channels...');
      
      for (const [name, channel] of this.channels) {
        try {
          await supabase.removeChannel(channel);
          console.log(`✅ Disconnected channel: ${name}`);
        } catch (error) {
          console.error(`❌ Failed to disconnect channel ${name}:`, error);
        }
      }
      
      this.channels.clear();
      this.isConnected = false;
      this.reconnectAttempts = 0;
      
      console.log('✅ All WebSocket channels disconnected');
    } catch (error) {
      console.error('❌ WebSocket disconnection failed:', error);
    }
  }

  /**
   * Get connection status
   */
  getStatus(): {
    isConnected: boolean;
    channelCount: number;
    reconnectAttempts: number;
    channels: string[];
  } {
    return {
      isConnected: this.isConnected,
      channelCount: this.channels.size,
      reconnectAttempts: this.reconnectAttempts,
      channels: Array.from(this.channels.keys())
    };
  }

  /**
   * Send presence update
   */
  async updatePresence(status: 'online' | 'away' | 'offline'): Promise<void> {
    try {
      const channel = this.channels.get('notifications');
      if (channel) {
        await channel.track({
          status,
          timestamp: new Date().toISOString()
        });
        console.log(`✅ Presence updated: ${status}`);
      }
    } catch (error) {
      console.error('❌ Presence update failed:', error);
    }
  }

  /**
   * Request notification permission
   */
  async requestNotificationPermission(): Promise<boolean> {
    try {
      if ('Notification' in window) {
        const permission = await Notification.requestPermission();
        console.log(`🔔 Notification permission: ${permission}`);
        return permission === 'granted';
      }
      return false;
    } catch (error) {
      console.error('❌ Notification permission request failed:', error);
      return false;
    }
  }
}

// Global instance
export const webSocketManager = WebSocketManager.getInstance();

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).webSocketManager = webSocketManager;
  (window as any).getWebSocketStatus = () => webSocketManager.getStatus();
  (window as any).reconnectWebSocket = () => webSocketManager.initialize();
  
  console.log('🔌 WebSocket Manager loaded. Available commands:');
  console.log('  - getWebSocketStatus()');
  console.log('  - reconnectWebSocket()');
}
