import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/auth/AuthProvider';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Clock, CheckCircle, AlertCircle, Play, Pause, FileText, Calendar, User } from 'lucide-react';
import { format } from 'date-fns';

interface Task {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assigned_to_id: string;
  created_by: string;
  project_id?: string;
  due_date?: string;
  estimated_hours?: number;
  actual_hours?: number;
  created_at: string;
  updated_at: string;
  assigned_to_profile?: {
    full_name: string;
    email: string;
  };
  created_by_profile?: {
    full_name: string;
    email: string;
  };
  project?: {
    name: string;
  };
}

interface TaskUpdate {
  id: string;
  task_id: string;
  updated_by: string;
  previous_status: string;
  new_status: string;
  previous_progress?: number;
  new_progress?: number;
  hours_worked?: number;
  update_notes: string;
  created_at: string;
  updated_by_profile?: {
    full_name: string;
    email: string;
  };
  task?: {
    title: string;
    project?: {
      name: string;
    };
  };
}

export const TaskUpdates = () => {
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false);
  const [updateForm, setUpdateForm] = useState({
    status: '',
    progress: 0,
    hours_worked: 0,
    notes: ''
  });

  // Fetch user's tasks
  const { data: tasks, isLoading: tasksLoading } = useQuery({
    queryKey: ['user-tasks', userProfile?.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tasks')
        .select(`
          *,
          assigned_to_profile:profiles!assigned_to_id(full_name, email),
          created_by_profile:profiles!created_by(full_name, email),
          project:projects(name)
        `)
        .eq('assigned_to_id', userProfile?.id)
        .order('updated_at', { ascending: false });

      if (error) throw error;
      return data as Task[];
    },
    enabled: !!userProfile?.id
  });

  // Fetch task updates
  const { data: taskUpdates, isLoading: updatesLoading } = useQuery({
    queryKey: ['task-updates', userProfile?.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('task_updates')
        .select(`
          *,
          updated_by_profile:profiles!updated_by(full_name, email),
          task:tasks(
            title,
            project:projects(name)
          )
        `)
        .eq('updated_by', userProfile?.id)
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) throw error;
      return data as TaskUpdate[];
    },
    enabled: !!userProfile?.id
  });

  // Create task update mutation
  const createUpdateMutation = useMutation({
    mutationFn: async (updateData: {
      task_id: string;
      new_status: string;
      previous_status: string;
      progress?: number;
      hours_worked?: number;
      notes: string;
    }) => {
      // Update the task
      const { error: taskError } = await supabase
        .from('tasks')
        .update({
          status: updateData.new_status,
          actual_hours: updateData.hours_worked,
          updated_at: new Date().toISOString()
        })
        .eq('id', updateData.task_id);

      if (taskError) throw taskError;

      // Create task update record
      const { data, error } = await supabase
        .from('task_updates')
        .insert({
          task_id: updateData.task_id,
          updated_by: userProfile?.id,
          previous_status: updateData.previous_status,
          new_status: updateData.new_status,
          new_progress: updateData.progress,
          hours_worked: updateData.hours_worked,
          update_notes: updateData.notes
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      toast({
        title: "Task Updated",
        description: "Task has been successfully updated",
      });
      queryClient.invalidateQueries({ queryKey: ['user-tasks'] });
      queryClient.invalidateQueries({ queryKey: ['task-updates'] });
      setIsUpdateDialogOpen(false);
      setSelectedTask(null);
      setUpdateForm({ status: '', progress: 0, hours_worked: 0, notes: '' });
    },
    onError: (error: any) => {
      toast({
        title: "Update Failed",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const handleTaskUpdate = (task: Task) => {
    setSelectedTask(task);
    setUpdateForm({
      status: task.status,
      progress: 0,
      hours_worked: 0,
      notes: ''
    });
    setIsUpdateDialogOpen(true);
  };

  const handleSubmitUpdate = () => {
    if (!selectedTask) return;

    createUpdateMutation.mutate({
      task_id: selectedTask.id,
      new_status: updateForm.status,
      previous_status: selectedTask.status,
      progress: updateForm.progress,
      hours_worked: updateForm.hours_worked,
      notes: updateForm.notes
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="h-4 w-4" />;
      case 'in_progress': return <Play className="h-4 w-4" />;
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'cancelled': return <Pause className="h-4 w-4" />;
      default: return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'urgent': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (tasksLoading || updatesLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Task Updates</h1>
          <p className="text-muted-foreground">
            Update your task progress and track your work
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Current Tasks */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              My Active Tasks
            </CardTitle>
            <CardDescription>
              Tasks assigned to you that need updates
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {tasks?.filter(task => task.status !== 'completed' && task.status !== 'cancelled').map((task) => (
              <div key={task.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <h3 className="font-medium">{task.title}</h3>
                    <p className="text-sm text-muted-foreground">{task.description}</p>
                    {task.project && (
                      <p className="text-xs text-muted-foreground">
                        Project: {task.project.name}
                      </p>
                    )}
                  </div>
                  <div className="flex flex-col gap-2">
                    <Badge className={getStatusColor(task.status)}>
                      {getStatusIcon(task.status)}
                      <span className="ml-1 capitalize">{task.status.replace('_', ' ')}</span>
                    </Badge>
                    <Badge variant="outline" className={getPriorityColor(task.priority)}>
                      {task.priority}
                    </Badge>
                  </div>
                </div>
                
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <div className="flex items-center gap-4">
                    {task.due_date && (
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        Due: {format(new Date(task.due_date), 'MMM dd, yyyy')}
                      </div>
                    )}
                    {task.estimated_hours && (
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        Est: {task.estimated_hours}h
                      </div>
                    )}
                  </div>
                  <Button
                    size="sm"
                    onClick={() => handleTaskUpdate(task)}
                  >
                    Update Task
                  </Button>
                </div>
              </div>
            ))}
            
            {tasks?.filter(task => task.status !== 'completed' && task.status !== 'cancelled').length === 0 && (
              <p className="text-center text-muted-foreground py-8">
                No active tasks to update
              </p>
            )}
          </CardContent>
        </Card>

        {/* Recent Updates */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Recent Updates
            </CardTitle>
            <CardDescription>
              Your recent task updates and progress
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {taskUpdates?.map((update) => (
              <div key={update.id} className="border rounded-lg p-4 space-y-2">
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <h4 className="font-medium">{update.task?.title}</h4>
                    {update.task?.project && (
                      <p className="text-xs text-muted-foreground">
                        Project: {update.task.project.name}
                      </p>
                    )}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {format(new Date(update.created_at), 'MMM dd, HH:mm')}
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Badge className={getStatusColor(update.previous_status)}>
                    {update.previous_status.replace('_', ' ')}
                  </Badge>
                  <span>→</span>
                  <Badge className={getStatusColor(update.new_status)}>
                    {update.new_status.replace('_', ' ')}
                  </Badge>
                </div>
                
                {update.hours_worked && (
                  <p className="text-sm text-muted-foreground">
                    Hours worked: {update.hours_worked}
                  </p>
                )}
                
                {update.update_notes && (
                  <p className="text-sm">{update.update_notes}</p>
                )}
              </div>
            ))}
            
            {taskUpdates?.length === 0 && (
              <p className="text-center text-muted-foreground py-8">
                No recent updates
              </p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Update Dialog */}
      <Dialog open={isUpdateDialogOpen} onOpenChange={setIsUpdateDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Update Task</DialogTitle>
            <DialogDescription>
              Update the status and progress of: {selectedTask?.title}
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="status" className="text-right">
                Status
              </Label>
              <Select
                value={updateForm.status}
                onValueChange={(value) => setUpdateForm(prev => ({ ...prev, status: value }))}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="hours" className="text-right">
                Hours Worked
              </Label>
              <Input
                id="hours"
                type="number"
                step="0.5"
                min="0"
                value={updateForm.hours_worked}
                onChange={(e) => setUpdateForm(prev => ({ ...prev, hours_worked: parseFloat(e.target.value) || 0 }))}
                className="col-span-3"
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="notes" className="text-right">
                Notes
              </Label>
              <Textarea
                id="notes"
                placeholder="Add update notes..."
                value={updateForm.notes}
                onChange={(e) => setUpdateForm(prev => ({ ...prev, notes: e.target.value }))}
                className="col-span-3"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button
              type="submit"
              onClick={handleSubmitUpdate}
              disabled={createUpdateMutation.isPending || !updateForm.status}
            >
              {createUpdateMutation.isPending && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              Update Task
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
