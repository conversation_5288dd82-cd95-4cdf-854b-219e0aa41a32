import { ErrorBoundary } from '@/components/ErrorBoundary'
import { AIRealtimeUpdates } from '@/components/ai/AIRealtimeUpdates'
import { AISystemController } from '@/components/ai/AISystemController'
import { AITestComponent } from '@/components/ai/AITestComponent'
import { EnhancedRAGSystem } from '@/components/ai/EnhancedRAGSystem'
import { ProjectManagerAssistant } from '@/components/project/ProjectManagerAssistant'
import { ModernAIInterface } from '@/components/ai/ModernAIInterface'
import { AIDocumentAnalyzer } from '@/components/ai/AIDocumentAnalyzer'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Activity, Database, Settings, Users, Zap, Brain, FileText } from 'lucide-react'

const AIPage = () => {
  return (
    <ErrorBoundary>
      <div className='min-h-screen bg-background p-4 md:p-6'>
        {/* Enhanced Header with System Theme */}
        <div className='text-center mb-8'>
          <div className='relative inline-block'>
            <h1 className='text-6xl font-bold bg-gradient-to-r from-primary via-secondary to-primary bg-clip-text text-transparent mb-4'>
              AI COMMAND CENTER
            </h1>
            <div className='absolute -inset-4 border border-primary/30 rounded-full animate-pulse' />
          </div>
          <p className='text-xl text-muted-foreground mb-2'>
            Advanced Intelligence • RAG Enhanced • Multi-LLM Powered
          </p>
          <div className='flex justify-center gap-4 text-sm text-muted-foreground'>
            <span>🧠 Neural Networks Active</span>
            <span>🚀 Real-time Processing</span>
            <span>🔒 Role-Based Security</span>
            <span>⚡ Continuous Learning</span>
          </div>
        </div>

        <Tabs defaultValue='modern' className='space-y-6'>
          <TabsList className='grid w-full grid-cols-6 bg-card border border-border backdrop-blur-xl rounded-2xl p-2'>

            <TabsTrigger
              value='modern'
              className='flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-xl transition-all duration-300'
            >
              <Brain className='h-4 w-4' />
              MODERN AI
            </TabsTrigger>

            <TabsTrigger
              value='rag'
              className='flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-xl transition-all duration-300'
            >
              <Database className='h-4 w-4' />
              RAG SYSTEM
            </TabsTrigger>
            <TabsTrigger
              value='realtime'
              className='flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-xl transition-all duration-300'
            >
              <Activity className='h-4 w-4' />
              LIVE NEURAL FEED
            </TabsTrigger>
            <TabsTrigger
              value='controller'
              className='flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-xl transition-all duration-300'
            >
              <Settings className='h-4 w-4' />
              SYSTEM CONTROLLER
            </TabsTrigger>
            <TabsTrigger
              value='tests'
              className='flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-xl transition-all duration-300'
            >
              <Zap className='h-4 w-4' />
              DATABASE TESTS
            </TabsTrigger>
            <TabsTrigger
              value='project-manager'
              className='flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-xl transition-all duration-300'
            >
              <Users className='h-4 w-4' />
              PROJECT MANAGER AI
            </TabsTrigger>
            <TabsTrigger
              value='document-analyzer'
              className='flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-xl transition-all duration-300'
            >
              <FileText className='h-4 w-4' />
              DOCUMENT ANALYZER
            </TabsTrigger>
          </TabsList>

          <TabsContent value='modern'>
            <ErrorBoundary>
              <ModernAIInterface />
            </ErrorBoundary>
          </TabsContent>

          <TabsContent value='rag'>
            <ErrorBoundary>
              <EnhancedRAGSystem />
            </ErrorBoundary>
          </TabsContent>

          <TabsContent value='realtime'>
            <ErrorBoundary>
              <div className='neumorphism-card p-6'>
                <AIRealtimeUpdates />
              </div>
            </ErrorBoundary>
          </TabsContent>

          <TabsContent value='controller'>
            <ErrorBoundary>
              <div className='neumorphism-card p-6'>
                <AISystemController />
              </div>
            </ErrorBoundary>
          </TabsContent>

          <TabsContent value='tests'>
            <ErrorBoundary>
              <div className='neumorphism-card p-6'>
                <AITestComponent />
              </div>
            </ErrorBoundary>
          </TabsContent>

          <TabsContent value='project-manager'>
            <ErrorBoundary>
              <div className='neumorphism-card p-6'>
                <ProjectManagerAssistant />
              </div>
            </ErrorBoundary>
          </TabsContent>

          <TabsContent value='document-analyzer'>
            <ErrorBoundary>
              <div className='neumorphism-card p-6'>
                <AIDocumentAnalyzer />
              </div>
            </ErrorBoundary>
          </TabsContent>
        </Tabs>
      </div>
    </ErrorBoundary>
  )
}

export default AIPage
