/**
 * Fix Tasks Foreign Key Constraints
 * Updates foreign key constraints to reference profiles table instead of auth.users
 */

import { supabase } from '@/integrations/supabase/client';

export class TasksForeignKeyFixer {
  private static results: string[] = [];

  /**
   * Fix all tasks foreign key constraints
   */
  static async fixTasksForeignKeys(): Promise<{ success: boolean; results: string[] }> {
    console.log('🔧 Starting tasks foreign key fixes...');
    this.results = [];

    try {
      // Step 1: Drop existing incorrect foreign key constraints
      await this.dropIncorrectConstraints();
      
      // Step 2: Add correct foreign key constraints
      await this.addCorrectConstraints();
      
      // Step 3: Test the fixed relationships
      await this.testFixedRelationships();

      console.log('✅ Tasks foreign key fixes completed successfully');
      return { success: true, results: this.results };
    } catch (error: any) {
      console.error('❌ Tasks foreign key fixes failed:', error);
      this.results.push(`Error: ${error.message}`);
      return { success: false, results: this.results };
    }
  }

  /**
   * Drop incorrect foreign key constraints that reference auth.users
   */
  private static async dropIncorrectConstraints(): Promise<void> {
    console.log('🗑️ Dropping incorrect foreign key constraints...');

    const constraints = [
      'tasks_assigned_to_id_fkey',
      'tasks_created_by_fkey'
    ];

    for (const constraint of constraints) {
      try {
        const { error } = await supabase.rpc('exec_sql', {
          sql: `
            DO $$
            BEGIN
              IF EXISTS (
                SELECT 1 FROM information_schema.table_constraints 
                WHERE constraint_name = '${constraint}' 
                AND table_name = 'tasks'
                AND table_schema = 'public'
              ) THEN
                ALTER TABLE public.tasks DROP CONSTRAINT ${constraint};
                RAISE NOTICE 'Dropped constraint: ${constraint}';
              ELSE
                RAISE NOTICE 'Constraint ${constraint} does not exist';
              END IF;
            END
            $$;
          `
        });

        if (error) {
          console.warn(`⚠️ Warning dropping constraint ${constraint}:`, error.message);
          this.results.push(`Warning dropping ${constraint}: ${error.message}`);
        } else {
          console.log(`✅ Dropped constraint: ${constraint}`);
          this.results.push(`Dropped constraint: ${constraint}`);
        }
      } catch (error: any) {
        console.warn(`⚠️ Warning dropping constraint ${constraint}:`, error.message);
        this.results.push(`Warning dropping ${constraint}: ${error.message}`);
      }
    }
  }

  /**
   * Add correct foreign key constraints that reference profiles table
   */
  private static async addCorrectConstraints(): Promise<void> {
    console.log('🔗 Adding correct foreign key constraints...');

    const constraints = [
      {
        name: 'tasks_assigned_to_id_fkey',
        sql: `
          ALTER TABLE public.tasks 
          ADD CONSTRAINT tasks_assigned_to_id_fkey 
          FOREIGN KEY (assigned_to_id) 
          REFERENCES public.profiles(id) 
          ON DELETE SET NULL;
        `
      },
      {
        name: 'tasks_created_by_fkey',
        sql: `
          ALTER TABLE public.tasks 
          ADD CONSTRAINT tasks_created_by_fkey 
          FOREIGN KEY (created_by) 
          REFERENCES public.profiles(id) 
          ON DELETE SET NULL;
        `
      }
    ];

    for (const constraint of constraints) {
      try {
        const { error } = await supabase.rpc('exec_sql', {
          sql: `
            DO $$
            BEGIN
              IF NOT EXISTS (
                SELECT 1 FROM information_schema.table_constraints 
                WHERE constraint_name = '${constraint.name}' 
                AND table_name = 'tasks'
                AND table_schema = 'public'
              ) THEN
                ${constraint.sql}
                RAISE NOTICE 'Added constraint: ${constraint.name}';
              ELSE
                RAISE NOTICE 'Constraint ${constraint.name} already exists';
              END IF;
            END
            $$;
          `
        });

        if (error) {
          console.error(`❌ Error adding constraint ${constraint.name}:`, error.message);
          this.results.push(`Error adding ${constraint.name}: ${error.message}`);
        } else {
          console.log(`✅ Added constraint: ${constraint.name}`);
          this.results.push(`Added constraint: ${constraint.name}`);
        }
      } catch (error: any) {
        console.error(`❌ Error adding constraint ${constraint.name}:`, error.message);
        this.results.push(`Error adding ${constraint.name}: ${error.message}`);
      }
    }
  }

  /**
   * Test the fixed foreign key relationships
   */
  private static async testFixedRelationships(): Promise<void> {
    console.log('🧪 Testing fixed foreign key relationships...');

    try {
      // Test query with the fixed foreign keys
      const { data, error } = await supabase
        .from('tasks')
        .select(`
          id,
          title,
          status,
          assignee:profiles!tasks_assigned_to_id_fkey(id, full_name, email),
          creator:profiles!tasks_created_by_fkey(id, full_name, email)
        `)
        .limit(5);

      if (error) {
        console.error('❌ Test query failed:', error.message);
        this.results.push(`Test query failed: ${error.message}`);
      } else {
        console.log(`✅ Test query successful, returned ${data?.length || 0} tasks`);
        this.results.push(`Test query successful, returned ${data?.length || 0} tasks`);
      }
    } catch (error: any) {
      console.error('❌ Test query error:', error.message);
      this.results.push(`Test query error: ${error.message}`);
    }
  }

  /**
   * Check current foreign key constraints
   */
  static async checkCurrentConstraints(): Promise<{ success: boolean; constraints: any[] }> {
    try {
      const { data, error } = await supabase.rpc('exec_sql', {
        sql: `
          SELECT 
            tc.constraint_name,
            tc.table_name,
            kcu.column_name,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name
          FROM information_schema.table_constraints AS tc 
          JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
          JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
            AND ccu.table_schema = tc.table_schema
          WHERE tc.constraint_type = 'FOREIGN KEY' 
            AND tc.table_name = 'tasks'
            AND tc.table_schema = 'public'
            AND (tc.constraint_name LIKE '%assigned_to%' OR tc.constraint_name LIKE '%created_by%');
        `
      });

      if (error) {
        console.error('❌ Error checking constraints:', error.message);
        return { success: false, constraints: [] };
      }

      return { success: true, constraints: data || [] };
    } catch (error: any) {
      console.error('❌ Error checking constraints:', error.message);
      return { success: false, constraints: [] };
    }
  }
}