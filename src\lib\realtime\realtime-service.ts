/**
 * Real-time Collaboration Service
 * Handles live updates, presence, and collaborative features
 */

import { supabase } from '@/integrations/supabase/client';
import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';

export interface UserPresence {
  userId: string;
  userName: string;
  avatar?: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  lastSeen: Date;
  currentPage?: string;
  isTyping?: boolean;
  cursor?: { x: number; y: number };
}

export interface CollaborativeSession {
  id: string;
  type: 'document' | 'project' | 'task' | 'meeting';
  resourceId: string;
  participants: UserPresence[];
  createdAt: Date;
  lastActivity: Date;
}

export interface RealtimeEvent {
  type: string;
  payload: any;
  userId: string;
  timestamp: Date;
  sessionId?: string;
}

export type RealtimeEventHandler = (event: RealtimeEvent) => void;

export class RealtimeService {
  private static instance: RealtimeService;
  private channels = new Map<string, RealtimeChannel>();
  private eventHandlers = new Map<string, RealtimeEventHandler[]>();
  private userPresence: UserPresence | null = null;
  private activeSessions = new Map<string, CollaborativeSession>();
  private heartbeatInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.initializeHeartbeat();
  }

  static getInstance(): RealtimeService {
    if (!RealtimeService.instance) {
      RealtimeService.instance = new RealtimeService();
    }
    return RealtimeService.instance;
  }

  /**
   * Initialize user presence
   */
  async initializePresence(userId: string, userName: string, avatar?: string): Promise<void> {
    try {
      // Get user profile if not provided
      if (!userName) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('full_name, avatar_url')
          .eq('id', userId)
          .single();
        
        userName = profile?.full_name || 'Unknown User';
        avatar = profile?.avatar_url || undefined;
      }

      this.userPresence = {
        userId,
        userName,
        avatar,
        status: 'online',
        lastSeen: new Date(),
        currentPage: window.location.pathname,
      };

      // Subscribe to presence channel
      await this.subscribeToPresence();
      
      // Update presence in database
      await this.updatePresenceInDB();
      
      console.log('Presence initialized for user:', userName);
    } catch (error) {
      console.error('Failed to initialize presence:', error);
    }
  }

  /**
   * Subscribe to presence channel
   */
  private async subscribeToPresence(): Promise<void> {
    if (!this.userPresence) return;

    const channel = supabase.channel('user-presence', {
      config: {
        presence: {
          key: this.userPresence.userId,
        },
      },
    });

    // Track presence
    channel
      .on('presence', { event: 'sync' }, () => {
        const state = channel.presenceState();
        this.handlePresenceSync(state);
      })
      .on('presence', { event: 'join' }, ({ key, newPresences }) => {
        this.handlePresenceJoin(key, newPresences);
      })
      .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
        this.handlePresenceLeave(key, leftPresences);
      });

    // Subscribe and track presence
    await channel.subscribe(async (status) => {
      if (status === 'SUBSCRIBED') {
        await channel.track(this.userPresence!);
      }
    });

    this.channels.set('presence', channel);
  }

  /**
   * Handle presence sync
   */
  private handlePresenceSync(state: Record<string, any>): void {
    const users: UserPresence[] = [];
    
    Object.entries(state).forEach(([userId, presences]) => {
      if (Array.isArray(presences) && presences.length > 0) {
        const presence = presences[0] as UserPresence;
        users.push(presence);
      }
    });

    this.emitEvent({
      type: 'presence_sync',
      payload: { users },
      userId: this.userPresence?.userId || '',
      timestamp: new Date(),
    });
  }

  /**
   * Handle user joining
   */
  private handlePresenceJoin(key: string, newPresences: any[]): void {
    if (newPresences.length > 0) {
      const presence = newPresences[0] as UserPresence;
      this.emitEvent({
        type: 'user_joined',
        payload: { user: presence },
        userId: key,
        timestamp: new Date(),
      });
    }
  }

  /**
   * Handle user leaving
   */
  private handlePresenceLeave(key: string, leftPresences: any[]): void {
    if (leftPresences.length > 0) {
      const presence = leftPresences[0] as UserPresence;
      this.emitEvent({
        type: 'user_left',
        payload: { user: presence },
        userId: key,
        timestamp: new Date(),
      });
    }
  }

  /**
   * Update user status
   */
  async updateStatus(status: UserPresence['status']): Promise<void> {
    if (!this.userPresence) return;

    this.userPresence.status = status;
    this.userPresence.lastSeen = new Date();

    const channel = this.channels.get('presence');
    if (channel) {
      await channel.track(this.userPresence);
    }

    await this.updatePresenceInDB();
  }

  /**
   * Update current page
   */
  async updateCurrentPage(page: string): Promise<void> {
    if (!this.userPresence) return;

    this.userPresence.currentPage = page;
    this.userPresence.lastSeen = new Date();

    const channel = this.channels.get('presence');
    if (channel) {
      await channel.track(this.userPresence);
    }
  }

  /**
   * Set typing indicator
   */
  async setTyping(isTyping: boolean, sessionId?: string): Promise<void> {
    if (!this.userPresence) return;

    this.userPresence.isTyping = isTyping;
    this.userPresence.lastSeen = new Date();

    const channel = this.channels.get('presence');
    if (channel) {
      await channel.track(this.userPresence);
    }

    // Emit typing event
    this.emitEvent({
      type: 'typing_indicator',
      payload: { isTyping, sessionId },
      userId: this.userPresence.userId,
      timestamp: new Date(),
      sessionId,
    });
  }

  /**
   * Join collaborative session
   */
  async joinSession(
    sessionId: string,
    type: CollaborativeSession['type'],
    resourceId: string
  ): Promise<void> {
    if (!this.userPresence) return;

    try {
      // Create or get session
      let session = this.activeSessions.get(sessionId);
      if (!session) {
        session = {
          id: sessionId,
          type,
          resourceId,
          participants: [],
          createdAt: new Date(),
          lastActivity: new Date(),
        };
        this.activeSessions.set(sessionId, session);
      }

      // Add user to session
      const existingIndex = session.participants.findIndex(p => p.userId === this.userPresence!.userId);
      if (existingIndex >= 0) {
        session.participants[existingIndex] = this.userPresence;
      } else {
        session.participants.push(this.userPresence);
      }

      // Subscribe to session channel
      await this.subscribeToSession(sessionId);

      // Save session to database
      await this.saveSessionToDB(session);

      this.emitEvent({
        type: 'session_joined',
        payload: { session },
        userId: this.userPresence.userId,
        timestamp: new Date(),
        sessionId,
      });
    } catch (error) {
      console.error('Failed to join session:', error);
    }
  }

  /**
   * Leave collaborative session
   */
  async leaveSession(sessionId: string): Promise<void> {
    if (!this.userPresence) return;

    try {
      const session = this.activeSessions.get(sessionId);
      if (session) {
        // Remove user from session
        session.participants = session.participants.filter(p => p.userId !== this.userPresence!.userId);
        
        if (session.participants.length === 0) {
          this.activeSessions.delete(sessionId);
          await this.deleteSessionFromDB(sessionId);
        } else {
          await this.saveSessionToDB(session);
        }
      }

      // Unsubscribe from session channel
      const channel = this.channels.get(`session-${sessionId}`);
      if (channel) {
        await supabase.removeChannel(channel);
        this.channels.delete(`session-${sessionId}`);
      }

      this.emitEvent({
        type: 'session_left',
        payload: { sessionId },
        userId: this.userPresence.userId,
        timestamp: new Date(),
        sessionId,
      });
    } catch (error) {
      console.error('Failed to leave session:', error);
    }
  }

  /**
   * Subscribe to session-specific events
   */
  private async subscribeToSession(sessionId: string): Promise<void> {
    const channelName = `session-${sessionId}`;
    
    if (this.channels.has(channelName)) {
      return; // Already subscribed
    }

    const channel = supabase.channel(channelName);

    // Listen for broadcast events
    channel.on('broadcast', { event: '*' }, (payload) => {
      this.handleSessionEvent(sessionId, payload);
    });

    await channel.subscribe();
    this.channels.set(channelName, channel);
  }

  /**
   * Handle session-specific events
   */
  private handleSessionEvent(sessionId: string, payload: any): void {
    this.emitEvent({
      type: payload.type || 'session_event',
      payload: payload.data || payload,
      userId: payload.userId || '',
      timestamp: new Date(),
      sessionId,
    });
  }

  /**
   * Broadcast event to session
   */
  async broadcastToSession(sessionId: string, eventType: string, data: any): Promise<void> {
    const channel = this.channels.get(`session-${sessionId}`);
    if (channel && this.userPresence) {
      await channel.send({
        type: 'broadcast',
        event: eventType,
        payload: {
          type: eventType,
          data,
          userId: this.userPresence.userId,
          timestamp: new Date().toISOString(),
        },
      });
    }
  }

  /**
   * Subscribe to database changes
   */
  async subscribeToTableChanges(
    table: string,
    filter?: string,
    handler?: (payload: RealtimePostgresChangesPayload<any>) => void
  ): Promise<void> {
    const channelName = `table-${table}`;
    
    if (this.channels.has(channelName)) {
      return; // Already subscribed
    }

    const channel = supabase.channel(channelName);

    let subscription = channel.on('postgres_changes', {
      event: '*',
      schema: 'public',
      table,
    }, (payload) => {
      if (handler) {
        handler(payload);
      } else {
        this.emitEvent({
          type: `table_${payload.eventType}`,
          payload: {
            table,
            record: payload.new || payload.old,
            eventType: payload.eventType,
          },
          userId: this.userPresence?.userId || '',
          timestamp: new Date(),
        });
      }
    });

    if (filter) {
      subscription = subscription.filter(filter);
    }

    await channel.subscribe();
    this.channels.set(channelName, channel);
  }

  /**
   * Add event handler
   */
  addEventListener(eventType: string, handler: RealtimeEventHandler): void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    this.eventHandlers.get(eventType)!.push(handler);
  }

  /**
   * Remove event handler
   */
  removeEventListener(eventType: string, handler: RealtimeEventHandler): void {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index >= 0) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * Emit event to handlers
   */
  private emitEvent(event: RealtimeEvent): void {
    const handlers = this.eventHandlers.get(event.type);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(event);
        } catch (error) {
          console.error('Event handler error:', error);
        }
      });
    }
  }

  /**
   * Initialize heartbeat to keep presence alive
   */
  private initializeHeartbeat(): void {
    this.heartbeatInterval = setInterval(async () => {
      if (this.userPresence) {
        this.userPresence.lastSeen = new Date();
        await this.updatePresenceInDB();
      }
    }, 30000); // Update every 30 seconds
  }

  /**
   * Update presence in database
   */
  private async updatePresenceInDB(): Promise<void> {
    if (!this.userPresence) return;

    try {
      await supabase
        .from('user_presence')
        .upsert({
          user_id: this.userPresence.userId,
          status: this.userPresence.status,
          last_seen: this.userPresence.lastSeen.toISOString(),
          current_page: this.userPresence.currentPage,
          is_typing: this.userPresence.isTyping || false,
        });
    } catch (error) {
      console.error('Failed to update presence in DB:', error);
    }
  }

  /**
   * Save session to database
   */
  private async saveSessionToDB(session: CollaborativeSession): Promise<void> {
    try {
      await supabase
        .from('collaborative_sessions')
        .upsert({
          id: session.id,
          type: session.type,
          resource_id: session.resourceId,
          participants: session.participants,
          last_activity: session.lastActivity.toISOString(),
        });
    } catch (error) {
      console.error('Failed to save session to DB:', error);
    }
  }

  /**
   * Delete session from database
   */
  private async deleteSessionFromDB(sessionId: string): Promise<void> {
    try {
      await supabase
        .from('collaborative_sessions')
        .delete()
        .eq('id', sessionId);
    } catch (error) {
      console.error('Failed to delete session from DB:', error);
    }
  }

  /**
   * Cleanup on disconnect
   */
  async cleanup(): Promise<void> {
    // Clear heartbeat
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    // Leave all sessions
    for (const sessionId of this.activeSessions.keys()) {
      await this.leaveSession(sessionId);
    }

    // Unsubscribe from all channels
    for (const channel of this.channels.values()) {
      await supabase.removeChannel(channel);
    }
    this.channels.clear();

    // Update status to offline
    if (this.userPresence) {
      await this.updateStatus('offline');
    }

    // Clear handlers
    this.eventHandlers.clear();
  }

  /**
   * Get current user presence
   */
  getCurrentPresence(): UserPresence | null {
    return this.userPresence;
  }

  /**
   * Get active sessions
   */
  getActiveSessions(): CollaborativeSession[] {
    return Array.from(this.activeSessions.values());
  }
}

export const realtimeService = RealtimeService.getInstance();
