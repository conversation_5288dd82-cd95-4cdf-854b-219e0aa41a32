/**
 * Database Relationship Fixer
 * Fixes foreign key relationship issues in Supabase queries
 */

import { supabase } from '@/integrations/supabase/client';

export class DatabaseRelationshipFixer {
  /**
   * Fix projects query by removing problematic foreign key references
   */
  static async getProjectsWithoutForeignKeys() {
    try {
      console.log('📋 Fetching projects without foreign key relationships...');
      
      // Get projects without the problematic foreign key reference
      const { data: projects, error: projectsError } = await supabase
        .from('projects')
        .select('*')
        .order('created_at', { ascending: false });

      if (projectsError) {
        console.warn('⚠️ Projects query failed:', projectsError.message);
        return { data: [], error: projectsError };
      }

      // If we have projects, try to get manager info separately
      if (projects && projects.length > 0) {
        const managerIds = projects
          .map(p => p.manager_id)
          .filter(id => id !== null && id !== undefined);

        if (managerIds.length > 0) {
          const { data: managers, error: managersError } = await supabase
            .from('profiles')
            .select('id, full_name, email')
            .in('id', managerIds);

          if (!managersError && managers) {
            // Manually join the data
            const projectsWithManagers = projects.map(project => ({
              ...project,
              manager: managers.find(m => m.id === project.manager_id) || null
            }));

            console.log('✅ Projects fetched with manual manager join');
            return { data: projectsWithManagers, error: null };
          }
        }
      }

      console.log('✅ Projects fetched without manager info');
      return { data: projects || [], error: null };
    } catch (error: any) {
      console.error('❌ Projects fetch failed:', error);
      return { data: [], error: error };
    }
  }

  /**
   * Fix tasks query by removing problematic foreign key references
   */
  static async getTasksWithoutForeignKeys(userId?: string) {
    try {
      console.log('📋 Fetching tasks without foreign key relationships...');
      
      let query = supabase
        .from('tasks')
        .select('*')
        .order('created_at', { ascending: false });

      // Add user filter if provided
      if (userId) {
        query = query.eq('assigned_to_id', userId);
      }

      const { data: tasks, error: tasksError } = await query;

      if (tasksError) {
        console.warn('⚠️ Tasks query failed:', tasksError.message);
        return { data: [], error: tasksError };
      }

      // If we have tasks, try to get creator info separately
      if (tasks && tasks.length > 0) {
        const creatorIds = tasks
          .map(t => t.created_by)
          .filter(id => id !== null && id !== undefined);

        if (creatorIds.length > 0) {
          const { data: creators, error: creatorsError } = await supabase
            .from('profiles')
            .select('id, full_name, email')
            .in('id', creatorIds);

          if (!creatorsError && creators) {
            // Manually join the data
            const tasksWithCreators = tasks.map(task => ({
              ...task,
              creator: creators.find(c => c.id === task.created_by) || null
            }));

            console.log('✅ Tasks fetched with manual creator join');
            return { data: tasksWithCreators, error: null };
          }
        }
      }

      console.log('✅ Tasks fetched without creator info');
      return { data: tasks || [], error: null };
    } catch (error: any) {
      console.error('❌ Tasks fetch failed:', error);
      return { data: [], error: error };
    }
  }

  /**
   * Fix memos query by removing problematic foreign key references
   */
  static async getMemosWithoutForeignKeys() {
    try {
      console.log('📋 Fetching memos without foreign key relationships...');
      
      const { data: memos, error: memosError } = await supabase
        .from('memos')
        .select('*')
        .order('created_at', { ascending: false });

      if (memosError) {
        console.warn('⚠️ Memos query failed:', memosError.message);
        return { data: [], error: memosError };
      }

      // If we have memos, try to get author info separately
      if (memos && memos.length > 0) {
        const authorIds = memos
          .map(m => m.created_by)
          .filter(id => id !== null && id !== undefined);

        if (authorIds.length > 0) {
          const { data: authors, error: authorsError } = await supabase
            .from('profiles')
            .select('id, full_name, role')
            .in('id', authorIds);

          if (!authorsError && authors) {
            // Manually join the data
            const memosWithAuthors = memos.map(memo => ({
              ...memo,
              author: authors.find(a => a.id === memo.created_by) || null
            }));

            console.log('✅ Memos fetched with manual author join');
            return { data: memosWithAuthors, error: null };
          }
        }
      }

      console.log('✅ Memos fetched without author info');
      return { data: memos || [], error: null };
    } catch (error: any) {
      console.error('❌ Memos fetch failed:', error);
      return { data: [], error: error };
    }
  }

  /**
   * Fix reports query by removing problematic foreign key references
   */
  static async getReportsWithoutForeignKeys(userId?: string) {
    try {
      console.log('📋 Fetching reports without foreign key relationships...');
      
      let query = supabase
        .from('reports')
        .select('*')
        .order('created_at', { ascending: false });

      // Add user filter if provided
      if (userId) {
        query = query.eq('submitted_by', userId);
      }

      const { data: reports, error: reportsError } = await query;

      if (reportsError) {
        console.warn('⚠️ Reports query failed:', reportsError.message);
        return { data: [], error: reportsError };
      }

      // If we have reports, try to get submitter and reviewer info separately
      if (reports && reports.length > 0) {
        const userIds = [
          ...reports.map(r => r.submitted_by),
          ...reports.map(r => r.reviewed_by)
        ].filter(id => id !== null && id !== undefined);

        if (userIds.length > 0) {
          const { data: users, error: usersError } = await supabase
            .from('profiles')
            .select('id, full_name, email')
            .in('id', userIds);

          if (!usersError && users) {
            // Manually join the data
            const reportsWithUsers = reports.map(report => ({
              ...report,
              profiles: users.find(u => u.id === report.submitted_by) || null,
              reviewer: users.find(u => u.id === report.reviewed_by) || null
            }));

            console.log('✅ Reports fetched with manual user join');
            return { data: reportsWithUsers, error: null };
          }
        }
      }

      console.log('✅ Reports fetched without user info');
      return { data: reports || [], error: null };
    } catch (error: any) {
      console.error('❌ Reports fetch failed:', error);
      return { data: [], error: error };
    }
  }

  /**
   * Test all database relationships
   */
  static async testAllRelationships() {
    console.log('🔍 Testing all database relationships...');
    
    const results = {
      projects: await this.getProjectsWithoutForeignKeys(),
      tasks: await this.getTasksWithoutForeignKeys(),
      memos: await this.getMemosWithoutForeignKeys(),
      reports: await this.getReportsWithoutForeignKeys()
    };

    const summary = {
      projects: results.projects.data.length,
      tasks: results.tasks.data.length,
      memos: results.memos.data.length,
      reports: results.reports.data.length,
      errors: [
        results.projects.error,
        results.tasks.error,
        results.memos.error,
        results.reports.error
      ].filter(Boolean)
    };

    console.log('📊 Database relationship test results:', summary);
    return summary;
  }

  /**
   * Create missing foreign key constraints (if possible)
   */
  static async createMissingConstraints() {
    console.log('🔧 Attempting to create missing foreign key constraints...');
    
    try {
      // Note: This would typically require database admin access
      // For now, we'll just log what constraints should be created
      
      const constraintsNeeded = [
        'ALTER TABLE projects ADD CONSTRAINT projects_manager_id_fkey FOREIGN KEY (manager_id) REFERENCES auth.users(id);',
        'ALTER TABLE tasks ADD CONSTRAINT tasks_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id);',
        'ALTER TABLE tasks ADD CONSTRAINT tasks_assigned_to_id_fkey FOREIGN KEY (assigned_to_id) REFERENCES auth.users(id);',
        'ALTER TABLE memos ADD CONSTRAINT memos_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id);',
        'ALTER TABLE reports ADD CONSTRAINT reports_submitted_by_fkey FOREIGN KEY (submitted_by) REFERENCES auth.users(id);',
        'ALTER TABLE reports ADD CONSTRAINT reports_reviewed_by_fkey FOREIGN KEY (reviewed_by) REFERENCES auth.users(id);'
      ];

      console.log('📝 Foreign key constraints needed:');
      constraintsNeeded.forEach((constraint, index) => {
        console.log(`${index + 1}. ${constraint}`);
      });

      console.log('ℹ️ These constraints need to be created via Supabase dashboard SQL editor');
      
      return {
        success: true,
        constraints: constraintsNeeded,
        message: 'Constraints identified - manual creation required'
      };
    } catch (error: any) {
      console.error('❌ Constraint creation failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).DatabaseRelationshipFixer = DatabaseRelationshipFixer;
  (window as any).testDatabaseRelationships = () => DatabaseRelationshipFixer.testAllRelationships();
  (window as any).getProjectsFixed = () => DatabaseRelationshipFixer.getProjectsWithoutForeignKeys();
  (window as any).getTasksFixed = () => DatabaseRelationshipFixer.getTasksWithoutForeignKeys();
  
  console.log('🔧 Database Relationship Fixer loaded. Available commands:');
  console.log('  - testDatabaseRelationships()');
  console.log('  - getProjectsFixed()');
  console.log('  - getTasksFixed()');
}
