/**
 * Enhanced Task Creation Component
 * Comprehensive task creation with AI assistance and advanced features
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { 
  Plus, 
  User, 
  Calendar, 
  Clock, 
  Flag, 
  Tag, 
  Brain,
  Save,
  X,
  Users,
  Project,
  AlertCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface TaskFormData {
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  assigned_to_id: string;
  project_id: string;
  due_date: string;
  estimated_hours: number;
  tags: string[];
  dependencies: string[];
}

interface User {
  id: string;
  full_name: string;
  email: string;
  role: string;
  department_id?: string;
}

interface Project {
  id: string;
  name: string;
  status: string;
  manager_id: string;
}

export const EnhancedTaskCreation: React.FC<{
  onTaskCreated?: (task: any) => void;
  onCancel?: () => void;
  initialData?: Partial<TaskFormData>;
}> = ({ onTaskCreated, onCancel, initialData }) => {
  const [formData, setFormData] = useState<TaskFormData>({
    title: '',
    description: '',
    priority: 'medium',
    status: 'pending',
    assigned_to_id: '',
    project_id: '',
    due_date: '',
    estimated_hours: 0,
    tags: [],
    dependencies: [],
    ...initialData
  });

  const [users, setUsers] = useState<User[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isAIAssisting, setIsAIAssisting] = useState(false);
  const [newTag, setNewTag] = useState('');
  const { toast } = useToast();

  useEffect(() => {
    loadUsers();
    loadProjects();
  }, []);

  const loadUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, email, role, department_id')
        .order('full_name');

      if (error) throw error;
      setUsers(data || []);
    } catch (error: any) {
      console.error('Error loading users:', error);
      toast({
        title: "Error",
        description: "Failed to load users",
        variant: "destructive",
      });
    }
  };

  const loadProjects = async () => {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('id, name, status, manager_id')
        .in('status', ['planning', 'active'])
        .order('name');

      if (error) throw error;
      setProjects(data || []);
    } catch (error: any) {
      console.error('Error loading projects:', error);
      toast({
        title: "Error",
        description: "Failed to load projects",
        variant: "destructive",
      });
    }
  };

  const handleInputChange = (field: keyof TaskFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const getAIAssistance = async () => {
    if (!formData.title.trim()) {
      toast({
        title: "Missing Information",
        description: "Please provide a task title first",
        variant: "destructive",
      });
      return;
    }

    setIsAIAssisting(true);
    try {
      const { data, error } = await supabase.functions.invoke('ai-assistant', {
        body: {
          message: `Help me create a detailed task for: "${formData.title}". ${formData.description ? `Additional context: ${formData.description}` : ''}`,
          context: {
            task: 'task_creation_assistance',
            userRole: 'task_creator'
          }
        }
      });

      if (error) throw error;

      // Parse AI response for suggestions
      const aiResponse = data.response;
      
      // Simple AI enhancement - in a real implementation, this would be more sophisticated
      if (!formData.description) {
        setFormData(prev => ({
          ...prev,
          description: `AI-suggested description: ${aiResponse.substring(0, 200)}...`
        }));
      }

      // Suggest tags based on title
      const suggestedTags = formData.title.toLowerCase().split(' ')
        .filter(word => word.length > 3)
        .slice(0, 3);
      
      setFormData(prev => ({
        ...prev,
        tags: [...new Set([...prev.tags, ...suggestedTags])]
      }));

      toast({
        title: "AI Assistance",
        description: "Task details enhanced with AI suggestions",
      });
    } catch (error: any) {
      console.error('AI assistance error:', error);
      toast({
        title: "AI Assistance Unavailable",
        description: "AI features are currently unavailable",
        variant: "destructive",
      });
    } finally {
      setIsAIAssisting(false);
    }
  };

  const createTask = async () => {
    if (!formData.title.trim()) {
      toast({
        title: "Missing Information",
        description: "Task title is required",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        throw new Error('User not authenticated');
      }

      const taskData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        priority: formData.priority,
        status: formData.status,
        assigned_to_id: formData.assigned_to_id || null,
        project_id: formData.project_id || null,
        due_date: formData.due_date || null,
        estimated_hours: formData.estimated_hours || null,
        created_by: user.id,
        tags: formData.tags,
        metadata: {
          dependencies: formData.dependencies,
          created_via: 'enhanced_task_creation'
        }
      };

      const { data, error } = await supabase
        .from('tasks')
        .insert(taskData)
        .select()
        .single();

      if (error) throw error;

      // Create task assignment if assigned to someone
      if (formData.assigned_to_id) {
        await supabase
          .from('task_assignments')
          .insert({
            task_id: data.id,
            assigned_to: formData.assigned_to_id,
            assigned_by: user.id,
            role: 'assignee',
            status: 'active'
          });
      }

      toast({
        title: "Success",
        description: "Task created successfully",
      });

      if (onTaskCreated) {
        onTaskCreated(data);
      }

      // Reset form
      setFormData({
        title: '',
        description: '',
        priority: 'medium',
        status: 'pending',
        assigned_to_id: '',
        project_id: '',
        due_date: '',
        estimated_hours: 0,
        tags: [],
        dependencies: []
      });

    } catch (error: any) {
      console.error('Error creating task:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create task",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-700 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-700 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-700 border-green-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  return (
    <Card className={cn(
      "border-[0.8px] border-[#ff1c04]/20",
      "bg-gradient-to-br from-[#1a1a1a] via-[#0f0f0f] to-[#1a1a1a]",
      "shadow-[8px_8px_16px_rgba(0,0,0,0.4),-8px_-8px_16px_rgba(255,28,4,0.1)]"
    )} data-component="EnhancedTaskCreation">
      <CardHeader>
        <CardTitle className="flex items-center gap-3 text-[#e5e5e5]">
          <div className="p-2 rounded-xl bg-gradient-to-br from-[#ff1c04]/10 to-[#ff1c04]/20 border border-[#ff1c04]/20">
            <Plus className="h-6 w-6 text-[#ff1c04]" />
          </div>
          <div>
            <h2 className="text-2xl font-bold">CREATE NEW TASK</h2>
            <p className="text-sm text-[#a5a5a5] font-normal">Enhanced task creation with AI assistance</p>
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <h3 className="text-lg font-semibold text-[#e5e5e5]">BASIC INFORMATION</h3>
            <Button
              onClick={getAIAssistance}
              disabled={isAIAssisting || !formData.title.trim()}
              variant="outline"
              size="sm"
              className="border-[#ff1c04]/30 text-[#ff1c04] hover:bg-[#ff1c04]/10"
            >
              <Brain className="h-4 w-4 mr-2" />
              {isAIAssisting ? 'AI Assisting...' : 'AI Assist'}
            </Button>
          </div>

          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-[#e5e5e5]">Task Title *</label>
              <Input
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="Enter task title..."
                className="bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-[#e5e5e5]">Priority</label>
              <Select value={formData.priority} onValueChange={(value) => handleInputChange('priority', value)}>
                <SelectTrigger className="bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="urgent">Urgent</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-[#e5e5e5]">Description</label>
            <Textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Describe the task in detail..."
              className="min-h-[100px] bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]"
            />
          </div>
        </div>

        {/* Assignment & Project */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-[#e5e5e5]">ASSIGNMENT & PROJECT</h3>
          
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-[#e5e5e5]">Assign To</label>
              <Select value={formData.assigned_to_id} onValueChange={(value) => handleInputChange('assigned_to_id', value)}>
                <SelectTrigger className="bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]">
                  <SelectValue placeholder="Select assignee..." />
                </SelectTrigger>
                <SelectContent>
                  {users.map(user => (
                    <SelectItem key={user.id} value={user.id}>
                      {user.full_name} ({user.role})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-[#e5e5e5]">Project</label>
              <Select value={formData.project_id} onValueChange={(value) => handleInputChange('project_id', value)}>
                <SelectTrigger className="bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]">
                  <SelectValue placeholder="Select project..." />
                </SelectTrigger>
                <SelectContent>
                  {projects.map(project => (
                    <SelectItem key={project.id} value={project.id}>
                      {project.name} ({project.status})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Timeline & Effort */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-[#e5e5e5]">TIMELINE & EFFORT</h3>
          
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-[#e5e5e5]">Due Date</label>
              <Input
                type="date"
                value={formData.due_date}
                onChange={(e) => handleInputChange('due_date', e.target.value)}
                className="bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-[#e5e5e5]">Estimated Hours</label>
              <Input
                type="number"
                value={formData.estimated_hours}
                onChange={(e) => handleInputChange('estimated_hours', parseFloat(e.target.value) || 0)}
                placeholder="0"
                min="0"
                step="0.5"
                className="bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]"
              />
            </div>
          </div>
        </div>

        {/* Tags */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-[#e5e5e5]">TAGS</h3>
          
          <div className="flex gap-2">
            <Input
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              placeholder="Add tag..."
              className="bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]"
              onKeyPress={(e) => e.key === 'Enter' && addTag()}
            />
            <Button onClick={addTag} variant="outline" className="border-[#ff1c04]/30 text-[#ff1c04]">
              <Tag className="h-4 w-4" />
            </Button>
          </div>

          {formData.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {formData.tags.map(tag => (
                <Badge key={tag} variant="outline" className="border-[#ff1c04]/30 text-[#ff1c04]">
                  {tag}
                  <X 
                    className="h-3 w-3 ml-1 cursor-pointer" 
                    onClick={() => removeTag(tag)}
                  />
                </Badge>
              ))}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex gap-4 pt-4">
          <Button
            onClick={createTask}
            disabled={isLoading || !formData.title.trim()}
            className="bg-[#ff1c04] hover:bg-[#ff1c04]/90 text-white"
          >
            <Save className="h-4 w-4 mr-2" />
            {isLoading ? 'Creating...' : 'Create Task'}
          </Button>

          {onCancel && (
            <Button
              onClick={onCancel}
              variant="outline"
              className="border-[#ff1c04]/30 text-[#ff1c04] hover:bg-[#ff1c04]/10"
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
