/**
 * Comprehensive Form Handler
 * Ensures all forms provide proper feedback and error handling
 */

import { showActionToast, showErrorToast, showSuccessToast, showLoadingToast } from './comprehensive-toast-system';

export interface FormSubmissionOptions {
  action: string;
  successMessage?: string;
  errorMessage?: string;
  loadingMessage?: string;
  showRetry?: boolean;
  onRetry?: () => void;
}

export class ComprehensiveFormHandler {
  private static instance: ComprehensiveFormHandler;
  private isInitialized = false;

  static getInstance(): ComprehensiveFormHandler {
    if (!ComprehensiveFormHandler.instance) {
      ComprehensiveFormHandler.instance = new ComprehensiveFormHandler();
    }
    return ComprehensiveFormHandler.instance;
  }

  /**
   * Initialize the comprehensive form handler
   */
  initialize(): void {
    if (this.isInitialized) return;

    console.log('📝 Initializing comprehensive form handler...');

    // Setup global form monitoring
    this.setupFormMonitoring();
    
    // Setup input validation monitoring
    this.setupInputValidationMonitoring();
    
    // Setup submission monitoring
    this.setupSubmissionMonitoring();

    this.isInitialized = true;
    console.log('✅ Comprehensive form handler initialized');
  }

  /**
   * Handle form submission with comprehensive feedback
   */
  async handleFormSubmission<T>(
    submitFunction: () => Promise<T>,
    options: FormSubmissionOptions
  ): Promise<T | null> {
    const { action, successMessage, errorMessage, loadingMessage, showRetry, onRetry } = options;

    console.log(`📝 Handling form submission for action: ${action}`);

    // Show loading toast
    const loadingToastId = showLoadingToast(loadingMessage || `Submitting ${action}...`);

    try {
      const result = await submitFunction();
      
      // Show success toast
      showActionToast(action, true, successMessage);
      
      console.log(`✅ Form submission successful for action: ${action}`);
      return result;
    } catch (error: any) {
      console.error(`❌ Form submission failed for action: ${action}`, error);
      
      // Show error toast with retry option
      showActionToast(action, false, errorMessage, error);
      
      return null;
    }
  }

  /**
   * Validate form before submission
   */
  validateForm(form: HTMLFormElement): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Check required fields
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach((field) => {
      const input = field as HTMLInputElement;
      if (!input.value.trim()) {
        errors.push(`${input.name || input.id || 'Field'} is required`);
        input.classList.add('border-red-500');
      } else {
        input.classList.remove('border-red-500');
      }
    });

    // Check email fields
    const emailFields = form.querySelectorAll('input[type="email"]');
    emailFields.forEach((field) => {
      const input = field as HTMLInputElement;
      if (input.value && !this.isValidEmail(input.value)) {
        errors.push('Please enter a valid email address');
        input.classList.add('border-red-500');
      }
    });

    // Check password fields
    const passwordFields = form.querySelectorAll('input[type="password"]');
    passwordFields.forEach((field) => {
      const input = field as HTMLInputElement;
      if (input.value && input.value.length < 6) {
        errors.push('Password must be at least 6 characters long');
        input.classList.add('border-red-500');
      }
    });

    // Check phone fields
    const phoneFields = form.querySelectorAll('input[type="tel"], input[name*="phone"]');
    phoneFields.forEach((field) => {
      const input = field as HTMLInputElement;
      if (input.value && !this.isValidPhone(input.value)) {
        errors.push('Please enter a valid phone number');
        input.classList.add('border-red-500');
      }
    });

    const isValid = errors.length === 0;
    
    if (!isValid) {
      console.log('📝 Form validation failed:', errors);
      showErrorToast({
        title: 'Form Validation Failed',
        description: errors.join(', '),
        duration: 5000
      });
    }

    return { isValid, errors };
  }

  /**
   * Setup global form monitoring
   */
  private setupFormMonitoring(): void {
    document.addEventListener('submit', (event) => {
      const form = event.target as HTMLFormElement;
      const formName = form.getAttribute('name') || form.id || 'form';
      
      console.log('📝 Form submission detected:', formName);

      // Validate form before submission
      const validation = this.validateForm(form);
      
      if (!validation.isValid) {
        event.preventDefault();
        console.log('📝 Form submission prevented due to validation errors');
        return false;
      }

      // Add visual feedback
      this.addSubmissionFeedback(form);
    });
  }

  /**
   * Setup input validation monitoring
   */
  private setupInputValidationMonitoring(): void {
    // Real-time validation on input change
    document.addEventListener('input', (event) => {
      const input = event.target as HTMLInputElement;
      
      if (input.hasAttribute('required')) {
        if (input.value.trim()) {
          input.classList.remove('border-red-500');
          input.classList.add('border-green-500');
        } else {
          input.classList.remove('border-green-500');
        }
      }

      // Email validation
      if (input.type === 'email' && input.value) {
        if (this.isValidEmail(input.value)) {
          input.classList.remove('border-red-500');
          input.classList.add('border-green-500');
        } else {
          input.classList.remove('border-green-500');
          input.classList.add('border-red-500');
        }
      }

      // Password validation
      if (input.type === 'password' && input.value) {
        if (input.value.length >= 6) {
          input.classList.remove('border-red-500');
          input.classList.add('border-green-500');
        } else {
          input.classList.remove('border-green-500');
          input.classList.add('border-red-500');
        }
      }
    });

    // Validation on blur
    document.addEventListener('blur', (event) => {
      const input = event.target as HTMLInputElement;
      
      if (input.hasAttribute('required') && !input.value.trim()) {
        input.classList.add('border-red-500');
      }
    }, true);
  }

  /**
   * Setup submission monitoring
   */
  private setupSubmissionMonitoring(): void {
    // Monitor for failed submissions
    document.addEventListener('error', (event) => {
      const target = event.target as HTMLElement;
      
      if (target.tagName === 'FORM' || target.closest('form')) {
        console.log('📝 Form submission error detected');
        showErrorToast({
          title: 'Submission Error',
          description: 'There was an error submitting the form. Please try again.',
          showRetry: true,
          onRetry: () => {
            const form = target.tagName === 'FORM' ? target as HTMLFormElement : target.closest('form');
            if (form) {
              form.requestSubmit();
            }
          }
        });
      }
    }, true);
  }

  /**
   * Add visual feedback during submission
   */
  private addSubmissionFeedback(form: HTMLFormElement): void {
    const submitButton = form.querySelector('button[type="submit"]') as HTMLButtonElement;
    
    if (submitButton) {
      const originalText = submitButton.textContent;
      const originalDisabled = submitButton.disabled;
      
      // Add loading state
      submitButton.disabled = true;
      submitButton.textContent = 'Submitting...';
      submitButton.classList.add('opacity-75');
      
      // Reset after a delay (in case the form doesn't handle it)
      setTimeout(() => {
        submitButton.disabled = originalDisabled;
        submitButton.textContent = originalText;
        submitButton.classList.remove('opacity-75');
      }, 5000);
    }
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate phone format
   */
  private isValidPhone(phone: string): boolean {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  }

  /**
   * Handle specific form types
   */
  handleLoginForm(email: string, password: string, submitFunction: () => Promise<any>): Promise<any> {
    return this.handleFormSubmission(submitFunction, {
      action: 'login',
      successMessage: 'Welcome back! Redirecting to your dashboard...',
      errorMessage: 'Invalid credentials. Please check your email and password.',
      loadingMessage: 'Signing you in...',
      showRetry: true
    });
  }

  handleSignupForm(submitFunction: () => Promise<any>): Promise<any> {
    return this.handleFormSubmission(submitFunction, {
      action: 'signup',
      successMessage: 'Account created successfully! Please check your email to verify.',
      errorMessage: 'Failed to create account. Please try again.',
      loadingMessage: 'Creating your account...',
      showRetry: true
    });
  }

  handleReportSubmission(submitFunction: () => Promise<any>): Promise<any> {
    return this.handleFormSubmission(submitFunction, {
      action: 'report-submit',
      successMessage: 'Report submitted successfully!',
      errorMessage: 'Failed to submit report. Please try again.',
      loadingMessage: 'Submitting your report...',
      showRetry: true
    });
  }

  handleClockInForm(submitFunction: () => Promise<any>): Promise<any> {
    return this.handleFormSubmission(submitFunction, {
      action: 'clock-in',
      successMessage: 'Successfully clocked in!',
      errorMessage: 'Failed to clock in. Please try again.',
      loadingMessage: 'Clocking you in...',
      showRetry: true
    });
  }

  handleAIQueryForm(submitFunction: () => Promise<any>): Promise<any> {
    return this.handleFormSubmission(submitFunction, {
      action: 'ai-query',
      successMessage: 'AI query processed successfully!',
      errorMessage: 'Failed to process AI query. Please try again.',
      loadingMessage: 'Processing your query...',
      showRetry: true
    });
  }
}

// Export singleton instance
export const comprehensiveFormHandler = ComprehensiveFormHandler.getInstance();

// Export utility functions
export const handleFormSubmission = (submitFunction: () => Promise<any>, options: FormSubmissionOptions) =>
  comprehensiveFormHandler.handleFormSubmission(submitFunction, options);

export const validateForm = (form: HTMLFormElement) => comprehensiveFormHandler.validateForm(form);

export const handleLoginForm = (email: string, password: string, submitFunction: () => Promise<any>) =>
  comprehensiveFormHandler.handleLoginForm(email, password, submitFunction);

export const handleSignupForm = (submitFunction: () => Promise<any>) =>
  comprehensiveFormHandler.handleSignupForm(submitFunction);

export const handleReportSubmission = (submitFunction: () => Promise<any>) =>
  comprehensiveFormHandler.handleReportSubmission(submitFunction);

export const handleClockInForm = (submitFunction: () => Promise<any>) =>
  comprehensiveFormHandler.handleClockInForm(submitFunction);

export const handleAIQueryForm = (submitFunction: () => Promise<any>) =>
  comprehensiveFormHandler.handleAIQueryForm(submitFunction);

// Auto-initialize when module loads
if (typeof window !== 'undefined') {
  setTimeout(() => {
    comprehensiveFormHandler.initialize();
  }, 1500);
}
