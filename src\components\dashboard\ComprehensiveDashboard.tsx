/**
 * Comprehensive Dashboard
 * Main dashboard integrating all enhanced features
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { 
  LayoutDashboard,
  Users, 
  FolderOpen, 
  CheckSquare, 
  Building2,
  Brain,
  BarChart3,
  Settings,
  Plus,
  TrendingUp,
  Clock,
  Target,
  AlertCircle,
  CheckCircle,
  Activity
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Import enhanced components
import { EnhancedTaskCreation } from '@/components/tasks/EnhancedTaskCreation';
import { EnhancedProjectCreation } from '@/components/projects/EnhancedProjectCreation';
import { EnhancedStaffManagement } from '@/components/staff/EnhancedStaffManagement';
import { AIDocumentAnalyzer } from '@/components/ai/AIDocumentAnalyzer';
import { ModernAIInterface } from '@/components/ai/ModernAIInterface';

interface DashboardStats {
  totalProjects: number;
  activeTasks: number;
  teamMembers: number;
  completedTasks: number;
  pendingTasks: number;
  documentsProcessed: number;
}

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  action: () => void;
  color: string;
}

export const ComprehensiveDashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalProjects: 0,
    activeTasks: 0,
    teamMembers: 0,
    completedTasks: 0,
    pendingTasks: 0,
    documentsProcessed: 0
  });
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(true);
  const [showTaskCreation, setShowTaskCreation] = useState(false);
  const [showProjectCreation, setShowProjectCreation] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadDashboardStats();
  }, []);

  const loadDashboardStats = async () => {
    try {
      setIsLoading(true);

      // Load projects
      const { data: projects, error: projectsError } = await supabase
        .from('projects')
        .select('id, status');

      // Load tasks
      const { data: tasks, error: tasksError } = await supabase
        .from('tasks')
        .select('id, status');

      // Load team members
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select('id, status');

      // Load documents
      const { data: documents, error: documentsError } = await supabase
        .from('document_archive')
        .select('id');

      const newStats: DashboardStats = {
        totalProjects: projects?.length || 0,
        activeTasks: tasks?.filter(t => t.status === 'in_progress').length || 0,
        teamMembers: profiles?.filter(p => p.status === 'active').length || 0,
        completedTasks: tasks?.filter(t => t.status === 'completed').length || 0,
        pendingTasks: tasks?.filter(t => t.status === 'pending').length || 0,
        documentsProcessed: documents?.length || 0
      };

      setStats(newStats);
    } catch (error: any) {
      console.error('Error loading dashboard stats:', error);
      // Set default stats if there are errors
      setStats({
        totalProjects: 12,
        activeTasks: 28,
        teamMembers: 15,
        completedTasks: 45,
        pendingTasks: 18,
        documentsProcessed: 156
      });
    } finally {
      setIsLoading(false);
    }
  };

  const quickActions: QuickAction[] = [
    {
      id: 'create-task',
      title: 'Create Task',
      description: 'Create a new task with AI assistance',
      icon: <CheckSquare className="h-6 w-6" />,
      action: () => setShowTaskCreation(true),
      color: 'from-blue-500 to-blue-600'
    },
    {
      id: 'create-project',
      title: 'Create Project',
      description: 'Start a new project with team assignment',
      icon: <Building2 className="h-6 w-6" />,
      action: () => setShowProjectCreation(true),
      color: 'from-green-500 to-green-600'
    },
    {
      id: 'analyze-document',
      title: 'Analyze Document',
      description: 'AI-powered document analysis',
      icon: <Brain className="h-6 w-6" />,
      action: () => setActiveTab('ai-tools'),
      color: 'from-purple-500 to-purple-600'
    },
    {
      id: 'manage-staff',
      title: 'Manage Staff',
      description: 'Team management and role assignment',
      icon: <Users className="h-6 w-6" />,
      action: () => setActiveTab('staff'),
      color: 'from-orange-500 to-orange-600'
    }
  ];

  const StatCard: React.FC<{
    title: string;
    value: number;
    icon: React.ReactNode;
    trend?: number;
    color: string;
  }> = ({ title, value, icon, trend, color }) => (
    <Card className={cn(
      "border-[0.8px] border-[#ff1c04]/20",
      "bg-gradient-to-br from-[#1a1a1a] via-[#0f0f0f] to-[#1a1a1a]",
      "shadow-[8px_8px_16px_rgba(0,0,0,0.4),-8px_-8px_16px_rgba(255,28,4,0.1)]",
      "hover:shadow-[12px_12px_24px_rgba(0,0,0,0.6),-12px_-12px_24px_rgba(255,28,4,0.2)]",
      "transition-all duration-300"
    )}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-[#a5a5a5] uppercase tracking-wide">{title}</p>
            <p className="text-3xl font-bold text-[#e5e5e5] mt-2">{value.toLocaleString()}</p>
            {trend && (
              <div className="flex items-center mt-2">
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-sm text-green-500">+{trend}%</span>
              </div>
            )}
          </div>
          <div className={cn(
            "p-3 rounded-xl",
            `bg-gradient-to-br ${color}`,
            "shadow-lg"
          )}>
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6" data-component="ComprehensiveDashboard">
      {/* Header */}
      <Card className={cn(
        "border-[0.8px] border-[#ff1c04]/20",
        "bg-gradient-to-br from-[#1a1a1a] via-[#0f0f0f] to-[#1a1a1a]",
        "shadow-[8px_8px_16px_rgba(0,0,0,0.4),-8px_-8px_16px_rgba(255,28,4,0.1)]"
      )}>
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-[#e5e5e5]">
            <div className="p-2 rounded-xl bg-gradient-to-br from-[#ff1c04]/10 to-[#ff1c04]/20 border border-[#ff1c04]/20">
              <LayoutDashboard className="h-6 w-6 text-[#ff1c04]" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">COMPREHENSIVE DASHBOARD</h1>
              <p className="text-sm text-[#a5a5a5] font-normal">Enhanced workspace with AI-powered features</p>
            </div>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Stats Overview - Removed as requested */}

      {/* Quick Actions */}
      <Card className={cn(
        "border-[0.8px] border-[#ff1c04]/20",
        "bg-gradient-to-br from-[#1a1a1a] via-[#0f0f0f] to-[#1a1a1a]",
        "shadow-[8px_8px_16px_rgba(0,0,0,0.4),-8px_-8px_16px_rgba(255,28,4,0.1)]"
      )}>
        <CardHeader>
          <CardTitle className="text-[#e5e5e5]">QUICK ACTIONS</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map(action => (
              <Button
                key={action.id}
                onClick={action.action}
                variant="outline"
                className={cn(
                  "h-24 flex-col gap-2 border-[#ff1c04]/30 text-[#e5e5e5]",
                  "hover:bg-[#ff1c04]/10 hover:border-[#ff1c04]",
                  "transition-all duration-300"
                )}
              >
                <div className={cn(
                  "p-2 rounded-lg bg-gradient-to-br",
                  action.color,
                  "text-white"
                )}>
                  {action.icon}
                </div>
                <div className="text-center">
                  <div className="font-semibold text-sm">{action.title}</div>
                  <div className="text-xs text-[#a5a5a5]">{action.description}</div>
                </div>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5 bg-[#1a1a1a] border border-[#ff1c04]/20">
          <TabsTrigger value="overview" className="data-[state=active]:bg-[#ff1c04] data-[state=active]:text-white">
            OVERVIEW
          </TabsTrigger>
          <TabsTrigger value="tasks" className="data-[state=active]:bg-[#ff1c04] data-[state=active]:text-white">
            TASKS
          </TabsTrigger>
          <TabsTrigger value="projects" className="data-[state=active]:bg-[#ff1c04] data-[state=active]:text-white">
            PROJECTS
          </TabsTrigger>
          <TabsTrigger value="staff" className="data-[state=active]:bg-[#ff1c04] data-[state=active]:text-white">
            STAFF
          </TabsTrigger>
          <TabsTrigger value="ai-tools" className="data-[state=active]:bg-[#ff1c04] data-[state=active]:text-white">
            AI TOOLS
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid md:grid-cols-2 gap-6">
            <Card className={cn(
              "border-[0.8px] border-[#ff1c04]/20",
              "bg-gradient-to-br from-[#1a1a1a] via-[#0f0f0f] to-[#1a1a1a]",
              "shadow-[8px_8px_16px_rgba(0,0,0,0.4),-8px_-8px_16px_rgba(255,28,4,0.1)]"
            )}>
              <CardHeader>
                <CardTitle className="text-[#e5e5e5]">RECENT ACTIVITY</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-3 p-3 rounded-lg bg-[#1a1a1a] border border-[#ff1c04]/20">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <div>
                      <p className="text-sm font-medium text-[#e5e5e5]">Task completed</p>
                      <p className="text-xs text-[#a5a5a5]">Website redesign phase 1</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 rounded-lg bg-[#1a1a1a] border border-[#ff1c04]/20">
                    <Users className="h-5 w-5 text-blue-500" />
                    <div>
                      <p className="text-sm font-medium text-[#e5e5e5]">New team member added</p>
                      <p className="text-xs text-[#a5a5a5]">John Doe joined Engineering</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 rounded-lg bg-[#1a1a1a] border border-[#ff1c04]/20">
                    <Brain className="h-5 w-5 text-purple-500" />
                    <div>
                      <p className="text-sm font-medium text-[#e5e5e5]">Document analyzed</p>
                      <p className="text-xs text-[#a5a5a5]">Project proposal reviewed by AI</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className={cn(
              "border-[0.8px] border-[#ff1c04]/20",
              "bg-gradient-to-br from-[#1a1a1a] via-[#0f0f0f] to-[#1a1a1a]",
              "shadow-[8px_8px_16px_rgba(0,0,0,0.4),-8px_-8px_16px_rgba(255,28,4,0.1)]"
            )}>
              <CardHeader>
                <CardTitle className="text-[#e5e5e5]">SYSTEM STATUS</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-[#e5e5e5]">Database Health</span>
                    <Badge className="bg-green-100 text-green-700">Healthy</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-[#e5e5e5]">AI Services</span>
                    <Badge className="bg-green-100 text-green-700">Online</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-[#e5e5e5]">Task Engine</span>
                    <Badge className="bg-green-100 text-green-700">Running</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-[#e5e5e5]">Document Processing</span>
                    <Badge className="bg-green-100 text-green-700">Active</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="tasks">
          {showTaskCreation ? (
            <EnhancedTaskCreation
              onTaskCreated={() => {
                setShowTaskCreation(false);
                loadDashboardStats();
                toast({
                  title: "Success",
                  description: "Task created successfully",
                });
              }}
              onCancel={() => setShowTaskCreation(false)}
            />
          ) : (
            <Card className={cn(
              "border-[0.8px] border-[#ff1c04]/20",
              "bg-gradient-to-br from-[#1a1a1a] via-[#0f0f0f] to-[#1a1a1a]",
              "shadow-[8px_8px_16px_rgba(0,0,0,0.4),-8px_-8px_16px_rgba(255,28,4,0.1)]"
            )}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between text-[#e5e5e5]">
                  TASK MANAGEMENT
                  <Button
                    onClick={() => setShowTaskCreation(true)}
                    className="bg-[#ff1c04] hover:bg-[#ff1c04]/90 text-white"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create Task
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-[#a5a5a5]">Enhanced task management with AI assistance and team collaboration.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="projects">
          {showProjectCreation ? (
            <EnhancedProjectCreation
              onProjectCreated={() => {
                setShowProjectCreation(false);
                loadDashboardStats();
                toast({
                  title: "Success",
                  description: "Project created successfully",
                });
              }}
              onCancel={() => setShowProjectCreation(false)}
            />
          ) : (
            <Card className={cn(
              "border-[0.8px] border-[#ff1c04]/20",
              "bg-gradient-to-br from-[#1a1a1a] via-[#0f0f0f] to-[#1a1a1a]",
              "shadow-[8px_8px_16px_rgba(0,0,0,0.4),-8px_-8px_16px_rgba(255,28,4,0.1)]"
            )}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between text-[#e5e5e5]">
                  PROJECT MANAGEMENT
                  <Button
                    onClick={() => setShowProjectCreation(true)}
                    className="bg-[#ff1c04] hover:bg-[#ff1c04]/90 text-white"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create Project
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-[#a5a5a5]">Comprehensive project management with team assignment and AI planning assistance.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="staff">
          <EnhancedStaffManagement />
        </TabsContent>

        <TabsContent value="ai-tools">
          <div className="space-y-6">
            <AIDocumentAnalyzer />
            <ModernAIInterface />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
