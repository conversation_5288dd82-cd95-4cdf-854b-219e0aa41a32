/**
 * System Fixes Test Utility
 * Tests all the fixes applied to resolve import errors and database issues
 */

import { supabase } from '@/integrations/supabase/client';

export interface TestResult {
  test: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: any;
}

export class SystemFixesTest {
  private results: TestResult[] = [];

  async runAllTests(): Promise<TestResult[]> {
    this.results = [];
    
    console.log('🧪 Starting system fixes tests...');
    
    await this.testDatabaseSchema();
    await this.testTimeLogsTable();
    await this.testMemoNotifications();
    await this.testRPCFunctions();
    await this.testForeignKeyConstraints();
    
    console.log('✅ System fixes tests completed');
    return this.results;
  }

  private async testDatabaseSchema(): Promise<void> {
    try {
      // Test if clock_in_method column exists
      const { data, error } = await supabase
        .from('time_logs')
        .select('clock_in_method')
        .limit(1);

      if (error && error.message.includes('clock_in_method')) {
        this.addResult('Database Schema', 'fail', 'clock_in_method column missing', error);
      } else {
        this.addResult('Database Schema', 'pass', 'clock_in_method column exists');
      }
    } catch (error) {
      this.addResult('Database Schema', 'fail', 'Schema test failed', error);
    }
  }

  private async testTimeLogsTable(): Promise<void> {
    try {
      // Test basic time_logs functionality
      const { data, error } = await supabase
        .from('time_logs')
        .select('id, user_id, clock_in, clock_in_method, activity_type, status')
        .limit(1);

      if (error) {
        this.addResult('Time Logs Table', 'fail', 'Time logs query failed', error);
      } else {
        this.addResult('Time Logs Table', 'pass', 'Time logs table accessible');
      }
    } catch (error) {
      this.addResult('Time Logs Table', 'fail', 'Time logs test failed', error);
    }
  }

  private async testMemoNotifications(): Promise<void> {
    try {
      // Test notifications table with memo-related notifications
      const { data, error } = await supabase
        .from('notifications')
        .select(`
          id,
          user_id,
          title,
          message,
          type,
          metadata,
          user:user_id(id, email)
        `)
        .eq('type', 'memo_published')
        .limit(1);

      if (error && error.message.includes('relationship')) {
        this.addResult('Memo Notifications', 'fail', 'Foreign key relationship error', error);
      } else {
        this.addResult('Memo Notifications', 'pass', 'Memo notifications working with notifications table');
      }
    } catch (error) {
      this.addResult('Memo Notifications', 'fail', 'Memo notifications test failed', error);
    }
  }

  private async testRPCFunctions(): Promise<void> {
    try {
      // Test get_team_time_logs function
      const { data: timeLogsData, error: timeLogsError } = await supabase
        .rpc('get_team_time_logs', {
          user_id_param: null,
          start_date_param: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          end_date_param: new Date().toISOString().split('T')[0],
          limit_param: 10
        });

      if (timeLogsError) {
        this.addResult('RPC Functions - Time Logs', 'fail', 'get_team_time_logs failed', timeLogsError);
      } else {
        this.addResult('RPC Functions - Time Logs', 'pass', 'get_team_time_logs working');
      }

      // Test get_attendance_stats function
      const { data: statsData, error: statsError } = await supabase
        .rpc('get_attendance_stats', {
          user_id_param: null,
          start_date_param: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          end_date_param: new Date().toISOString().split('T')[0]
        });

      if (statsError) {
        this.addResult('RPC Functions - Attendance', 'fail', 'get_attendance_stats failed', statsError);
      } else {
        this.addResult('RPC Functions - Attendance', 'pass', 'get_attendance_stats working');
      }
    } catch (error) {
      this.addResult('RPC Functions', 'fail', 'RPC functions test failed', error);
    }
  }

  private async testForeignKeyConstraints(): Promise<void> {
    try {
      // Test projects with foreign key joins
      const { data: projectsData, error: projectsError } = await supabase
        .from('projects')
        .select(`
          id,
          name,
          created_by,
          manager_id,
          creator:created_by(id, full_name),
          manager:manager_id(id, full_name)
        `)
        .limit(1);

      if (projectsError && projectsError.message.includes('relationship')) {
        this.addResult('Foreign Keys - Projects', 'warning', 'Projects foreign key issues', projectsError);
      } else {
        this.addResult('Foreign Keys - Projects', 'pass', 'Projects foreign keys working');
      }

      // Test invoices with foreign key joins
      const { data: invoicesData, error: invoicesError } = await supabase
        .from('invoices')
        .select(`
          id,
          invoice_number,
          created_by,
          creator:created_by(id, full_name)
        `)
        .limit(1);

      if (invoicesError && invoicesError.message.includes('relationship')) {
        this.addResult('Foreign Keys - Invoices', 'warning', 'Invoices foreign key issues', invoicesError);
      } else {
        this.addResult('Foreign Keys - Invoices', 'pass', 'Invoices foreign keys working');
      }
    } catch (error) {
      this.addResult('Foreign Key Constraints', 'fail', 'Foreign key test failed', error);
    }
  }

  private addResult(test: string, status: 'pass' | 'fail' | 'warning', message: string, details?: any): void {
    this.results.push({ test, status, message, details });
    
    const emoji = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⚠️';
    console.log(`${emoji} ${test}: ${message}`);
    
    if (details && status !== 'pass') {
      console.log('   Details:', details);
    }
  }

  getResultsSummary(): { total: number; passed: number; failed: number; warnings: number } {
    const total = this.results.length;
    const passed = this.results.filter(r => r.status === 'pass').length;
    const failed = this.results.filter(r => r.status === 'fail').length;
    const warnings = this.results.filter(r => r.status === 'warning').length;
    
    return { total, passed, failed, warnings };
  }
}

// Export a singleton instance for easy use
export const systemFixesTest = new SystemFixesTest();
