-- Create Missing RPC Functions Migration
-- This migration creates the exec_sql function and other missing RPC functions

-- ========================================
-- STEP 1: CREATE EXEC_SQL FUNCTION
-- ========================================

-- Create the exec_sql function that many components depend on
CREATE OR REPLACE FUNCTION public.exec_sql(sql text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    EXECUTE sql;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error executing SQL: %', SQLERRM;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.exec_sql(text) TO authenticated;
GRANT EXECUTE ON FUNCTION public.exec_sql(text) TO service_role;

-- ========================================
-- STEP 2: CREATE ATTENDANCE STATS FUNCTION
-- ========================================

CREATE OR REPLACE FUNCTION public.get_attendance_stats()
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_hours', COALESCE(SUM(total_hours), 0),
        'total_entries', COUNT(*),
        'avg_hours_per_day', COALESCE(AVG(total_hours), 0),
        'last_updated', NOW()
    ) INTO result
    FROM (
        SELECT 
            DATE(clock_in) as work_date,
            SUM(
                CASE 
                    WHEN clock_out IS NOT NULL THEN 
                        EXTRACT(EPOCH FROM (clock_out - clock_in)) / 3600.0
                    ELSE 0
                END
            ) as total_hours
        FROM time_logs 
        WHERE clock_in >= CURRENT_DATE - INTERVAL '30 days'
        GROUP BY DATE(clock_in)
    ) daily_stats;
    
    RETURN COALESCE(result, '{"total_hours": 0, "total_entries": 0, "avg_hours_per_day": 0}'::json);
EXCEPTION
    WHEN OTHERS THEN
        RETURN '{"error": "Failed to get attendance stats", "total_hours": 0, "total_entries": 0, "avg_hours_per_day": 0}'::json;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.get_attendance_stats() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_attendance_stats() TO service_role;

-- ========================================
-- STEP 3: CREATE TEAM TIME LOGS FUNCTION
-- ========================================

CREATE OR REPLACE FUNCTION public.get_team_time_logs()
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_agg(
        json_build_object(
            'id', tl.id,
            'user_id', tl.user_id,
            'user_name', COALESCE(p.full_name, 'Unknown User'),
            'clock_in', tl.clock_in,
            'clock_out', tl.clock_out,
            'total_hours', 
                CASE 
                    WHEN tl.clock_out IS NOT NULL THEN 
                        EXTRACT(EPOCH FROM (tl.clock_out - tl.clock_in)) / 3600.0
                    ELSE NULL
                END,
            'status', 
                CASE 
                    WHEN tl.clock_out IS NULL THEN 'active'
                    ELSE 'completed'
                END,
            'date', DATE(tl.clock_in)
        )
    ) INTO result
    FROM time_logs tl
    LEFT JOIN profiles p ON tl.user_id = p.id
    WHERE tl.clock_in >= CURRENT_DATE - INTERVAL '7 days'
    ORDER BY tl.clock_in DESC;
    
    RETURN COALESCE(result, '[]'::json);
EXCEPTION
    WHEN OTHERS THEN
        RETURN '[]'::json;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.get_team_time_logs() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_team_time_logs() TO service_role;

-- ========================================
-- STEP 4: CREATE CLOCK IN/OUT FUNCTIONS
-- ========================================

CREATE OR REPLACE FUNCTION public.clock_in(p_user_id UUID DEFAULT NULL)
RETURNS JSON AS $$
DECLARE
    user_id_to_use UUID;
    existing_entry RECORD;
    new_entry_id UUID;
BEGIN
    -- Use provided user_id or get from auth context
    user_id_to_use := COALESCE(p_user_id, auth.uid());
    
    IF user_id_to_use IS NULL THEN
        RETURN '{"success": false, "error": "No user ID provided"}'::json;
    END IF;
    
    -- Check if user is already clocked in
    SELECT * INTO existing_entry
    FROM time_logs 
    WHERE user_id = user_id_to_use 
    AND clock_out IS NULL 
    AND DATE(clock_in) = CURRENT_DATE;
    
    IF existing_entry.id IS NOT NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Already clocked in',
            'existing_entry', existing_entry.id
        );
    END IF;
    
    -- Create new clock in entry
    INSERT INTO time_logs (user_id, clock_in, created_at)
    VALUES (user_id_to_use, NOW(), NOW())
    RETURNING id INTO new_entry_id;
    
    RETURN json_build_object(
        'success', true,
        'entry_id', new_entry_id,
        'clock_in_time', NOW()
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.clock_out(p_user_id UUID DEFAULT NULL)
RETURNS JSON AS $$
DECLARE
    user_id_to_use UUID;
    existing_entry RECORD;
    total_hours NUMERIC;
BEGIN
    -- Use provided user_id or get from auth context
    user_id_to_use := COALESCE(p_user_id, auth.uid());
    
    IF user_id_to_use IS NULL THEN
        RETURN '{"success": false, "error": "No user ID provided"}'::json;
    END IF;
    
    -- Find active clock in entry
    SELECT * INTO existing_entry
    FROM time_logs 
    WHERE user_id = user_id_to_use 
    AND clock_out IS NULL 
    AND DATE(clock_in) = CURRENT_DATE;
    
    IF existing_entry.id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'No active clock in entry found'
        );
    END IF;
    
    -- Calculate total hours
    total_hours := EXTRACT(EPOCH FROM (NOW() - existing_entry.clock_in)) / 3600.0;
    
    -- Update with clock out time
    UPDATE time_logs 
    SET 
        clock_out = NOW(),
        total_hours = total_hours,
        updated_at = NOW()
    WHERE id = existing_entry.id;
    
    RETURN json_build_object(
        'success', true,
        'entry_id', existing_entry.id,
        'clock_out_time', NOW(),
        'total_hours', total_hours
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.clock_in(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.clock_out(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.clock_in(UUID) TO service_role;
GRANT EXECUTE ON FUNCTION public.clock_out(UUID) TO service_role;

-- ========================================
-- STEP 5: VERIFICATION
-- ========================================

DO $$
BEGIN
    RAISE NOTICE '✅ Missing RPC functions created successfully';
    RAISE NOTICE '🔧 exec_sql function: Available';
    RAISE NOTICE '📊 get_attendance_stats function: Available';
    RAISE NOTICE '👥 get_team_time_logs function: Available';
    RAISE NOTICE '⏰ clock_in/clock_out functions: Available';
    RAISE NOTICE '🚀 All RPC functions are ready for use';
END $$;