
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import type {
    EnhancedProfile,
    UserRole
} from "@/types/auth";
import {
    DEFAULT_ROLE,
    DEFAULT_STATUS,
    isValidRole,
    isValidStatus
} from "@/types/auth";
import { Session, User } from "@supabase/supabase-js";
import { useCallback, useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";

export const useSupabaseAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [userProfile, setUserProfile] = useState<EnhancedProfile | null>(null);
  const { toast } = useToast();
  const navigate = useNavigate();
  const isMountedRef = useRef(true);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Helper function to validate and convert profile data
  const validateProfile = useCallback((profileData: any): EnhancedProfile | null => {
    if (!profileData) return null;

    // Validate role
    const role = profileData.role && isValidRole(profileData.role)
      ? profileData.role as UserRole
      : DEFAULT_ROLE;

    // Validate status
    const status = profileData.status && isValidStatus(profileData.status)
      ? profileData.status
      : DEFAULT_STATUS;

    // Return validated profile
    return {
      ...profileData,
      role,
      status,
      account_type: profileData.account_type || role
    } as EnhancedProfile;
  }, []);

  useEffect(() => {
    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔐 Auth state changed:', event, session?.user?.email);
        console.log('🔐 Session data:', session);
        console.log('🔐 User metadata:', session?.user?.user_metadata);
        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          // Fetch user profile with role information
          console.log('🔎 Entered profile fetch/insert logic for user:', session.user.id);
          (async () => {
            try {
              const { data: profile, error } = await supabase
                .from('profiles')
                .select('*')
                .eq('id', session.user.id)
                .single();

              if (error) {
                console.error('Error fetching profile:', error);
              } else {
                console.log('Profile fetch response:', profile);
              }

              if (!profile) {
                console.warn('⚠️ No profile found, creating default profile for user:', session.user.id);

                // Try to create profile with better error handling
                try {
                  const profileData = {
                    id: session.user.id,
                    full_name: session.user.user_metadata?.full_name || '',
                    email: session.user.email,
                    role: 'staff' as const,
                    status: 'active' as const,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                  };

                  console.log('Attempting to insert profile:', profileData);

                  const { error: insertError, data: insertData } = await supabase
                    .from('profiles')
                    .insert(profileData);

                  console.log('Insert response:', { insertError, insertData });

                  if (insertError) {
                    console.error('Error creating profile:', insertError);

                    // Check if it's an RLS error
                    if (insertError.message?.includes('RLS') ||
                        insertError.message?.includes('policy') ||
                        insertError.code === '42501' ||
                        insertError.code === 'PGRST301') {
                      console.error('🚨 RLS Policy Error detected. Profile creation blocked by Row Level Security.');
                      console.error('Please run the RLS fix script or check /simple-rls-fix.html');

                      // Set a temporary profile to prevent app crashes
                      const tempProfile: EnhancedProfile = {
                        id: session.user.id,
                        full_name: session.user.user_metadata?.full_name || 'User',
                        email: session.user.email || '',
                        role: 'staff',
                        status: 'active',
                        account_type: 'staff',
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                      };

                      setUserProfile(tempProfile);
                      setIsLoading(false);
                      return;
                    }

                    return;
                  }
                } catch (profileError) {
                  console.error('Profile creation failed:', profileError);
                  return;
                }
                // Fetch again
                console.log('Fetching profile after insert...');
                const { data: newProfile, error: fetchError } = await supabase
                  .from('profiles')
                  .select('*')
                  .eq('id', session.user.id)
                  .single();
                console.log('Fetch after insert response:', { fetchError, newProfile });
                if (fetchError || !newProfile) {
                  console.error('Error fetching new profile:', fetchError);
                  return;
                }
                const validatedProfile = validateProfile(newProfile);
                setUserProfile(validatedProfile);
                console.log('🔐 Profile created and fetched:', validatedProfile);
                if (!validatedProfile?.role) {
                  console.warn('⚠️ No role found in profile, cannot redirect.');
                } else {
                  console.log('🔐 User role:', validatedProfile.role);
                  let redirectPath = '/dashboard';
                  switch (validatedProfile.role) {
                    case 'admin':
                      redirectPath = '/dashboard/admin';
                      break;
                    case 'manager':
                      redirectPath = '/dashboard/manager';
                      break;
                    case 'accountant':
                      redirectPath = '/dashboard/accountant';
                      break;
                    case 'hr':
                      redirectPath = '/dashboard/hr';
                      break;
                    case 'staff-admin':
                      redirectPath = '/dashboard/staff-admin';
                      break;
                    case 'staff':
                    default:
                      redirectPath = '/dashboard/staff';
                  }
                  console.log('➡️ Redirecting to:', redirectPath);
                  navigate(redirectPath);
                }
                if (validatedProfile?.role) {
                  localStorage.setItem('userRole', validatedProfile.role);
                  localStorage.setItem('accountType', validatedProfile.account_type || validatedProfile.role);
                }
              } else {
                const validatedProfile = validateProfile(profile);
                setUserProfile(validatedProfile);
                console.log('🔐 Profile fetched:', validatedProfile);
                if (!validatedProfile?.role) {
                  console.warn('⚠️ No role found in profile, cannot redirect.');
                } else {
                  console.log('🔐 User role:', validatedProfile.role);
                  switch (validatedProfile.role) {
                    case 'admin':
                      console.log('➡️ Redirect: admin dashboard');
                      break;
                    case 'manager':
                      console.log('➡️ Redirect: manager dashboard');
                      break;
                    case 'staff':
                      console.log('➡️ Redirect: staff dashboard');
                      break;
                    case 'accountant':
                      console.log('➡️ Redirect: accountant dashboard');
                      break;
                    case 'hr':
                      console.log('➡️ Redirect: HR dashboard');
                      break;
                    case 'staff-admin':
                      console.log('➡️ Redirect: staff-admin dashboard');
                      break;
                    default:
                      console.log('➡️ Redirect: default dashboard');
                  }
                }
                // Store role information for easy access
                if (validatedProfile?.role) {
                  localStorage.setItem('userRole', validatedProfile.role);
                  localStorage.setItem('accountType', validatedProfile.account_type || validatedProfile.role);
                }
              }
            } catch (err) {
              console.error('Profile fetch error:', err);
            }
          })();
        } else {
          setUserProfile(null);
          localStorage.removeItem('userRole');
          localStorage.removeItem('accountType');
        }
        setLoading(false);
      }
    );

    // Check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      toast({
        title: "Success",
        description: "Successfully signed in!",
      });

      return { data, error: null };
    } catch (error: any) {
      toast({
        title: "Authentication Error",
        description: error.message,
        variant: "destructive",
      });
      return { data: null, error };
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string, metadata?: any) => {
    try {
      setLoading(true);
      const redirectUrl = `${window.location.origin}/`;
      
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: redirectUrl,
          data: metadata
        }
      });

      if (error) throw error;

      toast({
        title: "Success",
        description: "Please check your email to verify your account!",
      });

      return { data, error: null };
    } catch (error: any) {
      toast({
        title: "Sign Up Error",
        description: error.message,
        variant: "destructive",
      });
      return { data: null, error };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      localStorage.removeItem('userRole');
      localStorage.removeItem('accountType');
      
      navigate('/auth');
      toast({
        title: "Signed Out",
        description: "You have been successfully signed out.",
      });
    } catch (error: any) {
      toast({
        title: "Sign Out Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const updateProfile = async (updates: any) => {
    try {
      if (!user) throw new Error('No user logged in');
      
      const { error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.id);
      
      if (error) throw error;
      
      // Refresh profile data
      const { data: profile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      const validatedProfile = validateProfile(profile);
      setUserProfile(validatedProfile);
      
      toast({
        title: "Success",
        description: "Profile updated successfully!",
      });
      
      return { error: null };
    } catch (error: any) {
      toast({
        title: "Update Error",
        description: error.message,
        variant: "destructive",
      });
      return { error };
    }
  };

  return {
    user,
    session,
    userProfile,
    loading,
    signIn,
    signUp,
    signOut,
    updateProfile,
    isAuthenticated: !!user,
    isAdmin: userProfile?.role === 'admin',
    isManager: userProfile?.role === 'manager',
    isStaff: userProfile?.role === 'staff',
    isAccountant: userProfile?.role === 'accountant',
    isHR: userProfile?.role === 'hr',
    isStaffAdmin: userProfile?.role === 'staff-admin',
    // Helper functions for role checking
    hasRole: (role: string) => userProfile?.role === role,
    hasAnyRole: (roles: string[]) => roles.includes(userProfile?.role),
    canManage: userProfile?.role === 'admin' || userProfile?.role === 'manager',
    // Enhanced properties
    initialized: !loading,
  };
};
