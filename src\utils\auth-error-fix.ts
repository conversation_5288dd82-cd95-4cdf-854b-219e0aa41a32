/**
 * Comprehensive Auth Error Fix
 * Handles all authentication-related errors and prevents error boundary crashes
 */

import { supabase } from '@/integrations/supabase/client';
import { showErrorToast, showWarningToast } from './comprehensive-toast-system';

export class AuthErrorFix {
  private static instance: AuthErrorFix;
  private isInitialized = false;
  private errorCount = 0;
  private maxErrors = 5;

  static getInstance(): AuthErrorFix {
    if (!AuthErrorFix.instance) {
      AuthErrorFix.instance = new AuthErrorFix();
    }
    return AuthErrorFix.instance;
  }

  /**
   * Initialize comprehensive auth error handling
   */
  initialize(): void {
    if (this.isInitialized) return;

    console.log('🔧 Initializing comprehensive auth error fix...');

    // Setup auth error monitoring
    this.setupAuthErrorMonitoring();
    
    // Setup session error handling
    this.setupSessionErrorHandling();
    
    // Setup token refresh error handling
    this.setupTokenRefreshErrorHandling();
    
    // Setup error boundary protection
    this.setupErrorBoundaryProtection();
    
    // Setup graceful degradation
    this.setupGracefulDegradation();

    this.isInitialized = true;
    console.log('✅ Comprehensive auth error fix initialized');
  }

  /**
   * Setup auth error monitoring
   */
  private setupAuthErrorMonitoring(): void {
    // Monitor auth state changes for errors
    supabase.auth.onAuthStateChange((event, session) => {
      console.log('🔄 Auth state change:', event);

      try {
        switch (event) {
          case 'SIGNED_OUT':
            this.handleSignOut();
            break;
          case 'TOKEN_REFRESHED':
            this.handleTokenRefresh(session);
            break;
          case 'SIGNED_IN':
            this.handleSignIn(session);
            break;
          default:
            console.log('🔄 Auth event:', event);
        }
      } catch (error: any) {
        console.error('❌ Error in auth state change handler:', error);
        this.handleAuthError(error, 'auth-state-change');
      }
    });
  }

  /**
   * Setup session error handling
   */
  private setupSessionErrorHandling(): void {
    // Override session methods to handle errors gracefully
    const originalGetSession = supabase.auth.getSession.bind(supabase.auth);
    
    supabase.auth.getSession = async () => {
      try {
        const result = await originalGetSession();
        
        if (result.error) {
          console.warn('⚠️ Session error:', result.error.message);
          this.handleSessionError(result.error);
        }
        
        return result;
      } catch (error: any) {
        console.error('❌ Session retrieval failed:', error);
        this.handleAuthError(error, 'get-session');
        
        // Return empty session to prevent crashes
        return { data: { session: null }, error: null };
      }
    };
  }

  /**
   * Setup token refresh error handling
   */
  private setupTokenRefreshErrorHandling(): void {
    // Monitor for token refresh errors
    window.addEventListener('unhandledrejection', (event) => {
      const error = event.reason;
      
      if (this.isTokenRefreshError(error)) {
        console.warn('🔧 Handling token refresh error:', error.message);
        event.preventDefault();
        
        this.handleTokenRefreshError(error);
        return false;
      }
    });
  }

  /**
   * Setup error boundary protection
   */
  private setupErrorBoundaryProtection(): void {
    // Add global error handler to prevent auth errors from crashing the app
    window.addEventListener('error', (event) => {
      const error = event.error;
      
      if (this.isAuthRelatedError(error)) {
        console.warn('🔧 Preventing auth error from crashing app:', error.message);
        event.preventDefault();
        
        this.handleAuthError(error, 'global-error');
        return false;
      }
    });

    // Don't override console.error as it causes cascading issues
    // Just handle auth errors through event listeners
  }

  /**
   * Setup graceful degradation
   */
  private setupGracefulDegradation(): void {
    // If too many auth errors occur, switch to fallback mode
    setInterval(() => {
      if (this.errorCount > this.maxErrors) {
        console.warn('⚠️ Too many auth errors, enabling fallback mode');
        this.enableFallbackMode();
        this.errorCount = 0; // Reset counter
      }
    }, 60000); // Check every minute
  }

  /**
   * Handle sign out
   */
  private handleSignOut(): void {
    try {
      // Clear any cached auth data
      localStorage.removeItem('supabase.auth.token');
      sessionStorage.removeItem('supabase.auth.token');
      
      console.log('✅ Sign out handled successfully');
    } catch (error: any) {
      console.error('❌ Error handling sign out:', error);
    }
  }

  /**
   * Handle token refresh
   */
  private handleTokenRefresh(session: any): void {
    try {
      if (session) {
        console.log('✅ Token refreshed successfully');
      } else {
        console.warn('⚠️ Token refresh returned null session');
      }
    } catch (error: any) {
      console.error('❌ Error handling token refresh:', error);
    }
  }

  /**
   * Handle sign in
   */
  private handleSignIn(session: any): void {
    try {
      if (session?.user) {
        console.log('✅ Sign in handled successfully');
        this.errorCount = 0; // Reset error count on successful sign in
      }
    } catch (error: any) {
      console.error('❌ Error handling sign in:', error);
    }
  }

  /**
   * Handle session errors
   */
  private handleSessionError(error: any): void {
    this.errorCount++;
    
    if (error.message?.includes('Invalid Refresh Token') || 
        error.message?.includes('Refresh Token Not Found')) {
      console.warn('🔧 Handling invalid refresh token');
      
      // Clear session and redirect to login
      supabase.auth.signOut();
      
      showWarningToast({
        title: 'Session Expired',
        description: 'Please sign in again to continue.',
        duration: 5000
      });
    }
  }

  /**
   * Handle token refresh errors
   */
  private handleTokenRefreshError(error: any): void {
    this.errorCount++;
    
    console.warn('🔧 Token refresh failed, signing out user');
    
    // Sign out user and clear session
    supabase.auth.signOut();
    
    showWarningToast({
      title: 'Session Expired',
      description: 'Your session has expired. Please sign in again.',
      duration: 5000
    });
  }

  /**
   * Handle general auth errors
   */
  private handleAuthError(error: any, context: string): void {
    this.errorCount++;
    
    console.warn(`🔧 Handling auth error in ${context}:`, error.message);
    
    // Don't show toast for every auth error to avoid spam
    if (this.errorCount <= 2) {
      showErrorToast({
        title: 'Authentication Issue',
        description: 'There was an authentication issue. Please try refreshing the page.',
        duration: 5000
      });
    }
  }

  /**
   * Check if error is token refresh related
   */
  private isTokenRefreshError(error: any): boolean {
    if (!error || !error.message) return false;
    
    const message = error.message.toLowerCase();
    return message.includes('invalid refresh token') ||
           message.includes('refresh token not found') ||
           message.includes('token refresh') ||
           (message.includes('auth') && message.includes('400'));
  }

  /**
   * Check if error is auth related
   */
  private isAuthRelatedError(error: any): boolean {
    if (!error || !error.message) return false;
    
    const message = error.message.toLowerCase();
    return message.includes('auth') ||
           message.includes('session') ||
           message.includes('token') ||
           message.includes('unauthorized') ||
           message.includes('forbidden') ||
           message.includes('supabase');
  }

  /**
   * Enable fallback mode
   */
  private enableFallbackMode(): void {
    console.warn('🔧 Enabling auth fallback mode');
    
    // Clear all auth data
    localStorage.clear();
    sessionStorage.clear();
    
    // Sign out user
    supabase.auth.signOut();
    
    showWarningToast({
      title: 'Authentication Reset',
      description: 'Authentication has been reset due to persistent issues. Please sign in again.',
      duration: 8000
    });
    
    // Redirect to auth page after a delay
    setTimeout(() => {
      window.location.href = '/auth';
    }, 3000);
  }

  /**
   * Get error count
   */
  getErrorCount(): number {
    return this.errorCount;
  }

  /**
   * Reset error count
   */
  resetErrorCount(): void {
    this.errorCount = 0;
  }

  /**
   * Check if initialized
   */
  isAuthErrorFixInitialized(): boolean {
    return this.isInitialized;
  }
}

// Export singleton instance
export const authErrorFix = AuthErrorFix.getInstance();

// Export utility functions
export const initializeAuthErrorFix = () => authErrorFix.initialize();
export const getAuthErrorCount = () => authErrorFix.getErrorCount();
export const resetAuthErrorCount = () => authErrorFix.resetErrorCount();

// Auto-initialize when module loads
if (typeof window !== 'undefined') {
  setTimeout(() => {
    authErrorFix.initialize();
  }, 500);
}
