/* Dashboard and Sidebar Specific Fixes for CTNL AI Work-Board */

/* ===== DASHBOARD LAYOUT FIXES ===== */

/* Fix for dashboard container */
.dashboard-layout {
  display: flex;
  min-height: 100vh;
  background: hsl(var(--background));
}

/* Fix for main dashboard content */
.dashboard-main {
  flex: 1;
  overflow-x: hidden;
  padding: 1rem;
  background: hsl(var(--background));
}

@media (min-width: 768px) {
  .dashboard-main {
    padding: 2rem;
    margin-left: 280px; /* Sidebar width */
  }
}

/* Fix for dashboard grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 640px) {
  .dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .dashboard-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Fix for dashboard cards */
.dashboard-card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
}

.dashboard-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* Fix for dashboard stats */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: hsl(var(--primary));
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.875rem;
  color: hsl(var(--muted-foreground));
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* ===== SIDEBAR FIXES ===== */

/* Fix for sidebar container */
.sidebar-container {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 280px;
  background: hsl(var(--background));
  border-right: 1px solid hsl(var(--border));
  z-index: 40;
  transform: translateX(-100%);
  transition: transform 0.3s ease-in-out;
  overflow-y: auto;
}

.sidebar-container.open {
  transform: translateX(0);
}

@media (min-width: 768px) {
  .sidebar-container {
    position: fixed;
    transform: translateX(0);
  }
}

/* Fix for sidebar header */
.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid hsl(var(--border));
  background: hsl(var(--background));
  position: sticky;
  top: 0;
  z-index: 10;
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 600;
  color: hsl(var(--foreground));
}

.sidebar-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border: none;
  background: transparent;
  color: hsl(var(--muted-foreground));
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease-in-out;
}

.sidebar-toggle:hover {
  background: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

@media (min-width: 768px) {
  .sidebar-toggle {
    display: none;
  }
}

/* Fix for sidebar navigation */
.sidebar-nav {
  padding: 1rem 0;
}

.sidebar-nav-group {
  margin-bottom: 1.5rem;
}

.sidebar-nav-label {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: hsl(var(--muted-foreground));
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.sidebar-nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-nav-item {
  margin: 0;
}

.sidebar-nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: #e5e5e5;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease-in-out;
  border-radius: 5px;
  margin: 0 0.5rem;
  border: 0.8px solid #ff1c04;
  background: linear-gradient(145deg, #1a1a1a, #0f0f0f);
  box-shadow:
    6px 6px 12px rgba(0, 0, 0, 0.4),
    -6px -6px 12px rgba(255, 28, 4, 0.1),
    inset 0 1px 0 rgba(255, 28, 4, 0.2);
  position: relative;
  overflow: hidden;
}

.sidebar-nav-link:hover {
  background: linear-gradient(145deg, #2a0a0a, #1a0505);
  color: #ff1c04;
  border-color: #ff1c04;
  box-shadow:
    3px 3px 6px rgba(0, 0, 0, 0.6),
    -3px -3px 6px rgba(255, 28, 4, 0.2),
    inset 1px 1px 2px rgba(255, 28, 4, 0.3);
  transform: translateY(-1px);
}

.sidebar-nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.sidebar-nav-link:hover::before {
  transform: translateX(100%);
}

.sidebar-nav-link.active {
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.sidebar-nav-icon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

.sidebar-nav-text {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sidebar-nav-badge {
  background: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
  font-size: 0.75rem;
  padding: 0.125rem 0.375rem;
  border-radius: 9999px;
  font-weight: 500;
}

/* Fix for sidebar user profile */
.sidebar-user {
  padding: 1rem;
  border-top: 1px solid hsl(var(--border));
  background: hsl(var(--background));
  position: sticky;
  bottom: 0;
}

.sidebar-user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.sidebar-user-avatar {
  width: 2.25rem;
  height: 2.25rem;
  border-radius: 50%;
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

.sidebar-user-details {
  flex: 1;
  min-width: 0;
}

.sidebar-user-name {
  font-weight: 500;
  color: hsl(var(--foreground));
  font-size: 0.875rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sidebar-user-role {
  font-size: 0.75rem;
  color: hsl(var(--muted-foreground));
  background: hsl(var(--secondary));
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  display: inline-block;
  margin-top: 0.25rem;
}

/* Fix for sidebar actions */
.sidebar-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.sidebar-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 0.8px solid #ff1c04;
  background: linear-gradient(145deg, #1a1a1a, #0f0f0f);
  color: #e5e5e5;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-radius: 5px;
  transition: all 0.3s ease-in-out;
  cursor: pointer;
  box-shadow:
    4px 4px 8px rgba(0, 0, 0, 0.4),
    -4px -4px 8px rgba(255, 28, 4, 0.1),
    inset 0 1px 0 rgba(255, 28, 4, 0.2);
  position: relative;
  overflow: hidden;
}

.sidebar-action-btn:hover {
  background: linear-gradient(145deg, #2a0a0a, #1a0505);
  color: #ff1c04;
  border-color: #ff1c04;
  box-shadow:
    2px 2px 4px rgba(0, 0, 0, 0.6),
    -2px -2px 4px rgba(255, 28, 4, 0.2),
    inset 1px 1px 2px rgba(255, 28, 4, 0.3);
  transform: translateY(-1px);
}

.sidebar-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.sidebar-action-btn:hover::before {
  transform: translateX(100%);
}

/* Fix for mobile sidebar overlay */
.sidebar-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 30;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease-in-out;
}

.sidebar-overlay.show {
  opacity: 1;
  visibility: visible;
}

@media (min-width: 768px) {
  .sidebar-overlay {
    display: none;
  }
}

/* Fix for responsive adjustments */
@media (max-width: 767px) {
  .dashboard-main {
    margin-left: 0;
    padding: 1rem;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .dashboard-stats {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .stat-card {
    padding: 1rem;
  }
  
  .stat-value {
    font-size: 1.5rem;
  }
}

/* Fix for dark mode */
.dark .sidebar-container {
  background: hsl(var(--background));
  border-right-color: hsl(var(--border));
}

.dark .sidebar-header {
  background: hsl(var(--background));
  border-bottom-color: hsl(var(--border));
}

.dark .sidebar-user {
  background: hsl(var(--background));
  border-top-color: hsl(var(--border));
}

/* Fix for scrollbar styling */
.sidebar-container::-webkit-scrollbar {
  width: 6px;
}

.sidebar-container::-webkit-scrollbar-track {
  background: hsl(var(--background));
}

.sidebar-container::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 3px;
}

.sidebar-container::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground));
}
