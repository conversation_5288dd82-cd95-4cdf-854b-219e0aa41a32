import { supabase } from '@/integrations/supabase/client';

export class ComprehensiveForeignKeyFixer {
  private async dropConstraintIfExists(tableName: string, constraintName: string): Promise<void> {
    try {
      const { error } = await supabase.rpc('execute_sql', {
        sql: `
          DO $$ 
          BEGIN
            IF EXISTS (SELECT 1 FROM information_schema.table_constraints 
                      WHERE constraint_name = '${constraintName}' 
                      AND table_name = '${tableName}') THEN
              EXECUTE 'ALTER TABLE public.${tableName} DROP CONSTRAINT ${constraintName}';
            END IF;
          END $$;
        `
      });
      
      if (error) {
        console.warn(`Warning dropping constraint ${constraintName}:`, error.message);
      }
    } catch (err) {
      console.warn(`Warning dropping constraint ${constraintName}:`, err);
    }
  }

  private async addConstraintIfNotExists(tableName: string, constraintName: string, constraintDef: string): Promise<void> {
    try {
      const { error } = await supabase.rpc('execute_sql', {
        sql: `
          DO $$ 
          BEGIN
            IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                          WHERE constraint_name = '${constraintName}' 
                          AND table_name = '${tableName}') THEN
              EXECUTE 'ALTER TABLE public.${tableName} ADD CONSTRAINT ${constraintName} ${constraintDef}';
            END IF;
          END $$;
        `
      });
      
      if (error) {
        throw new Error(`Failed to add constraint ${constraintName}: ${error.message}`);
      }
    } catch (err) {
      throw new Error(`Failed to add constraint ${constraintName}: ${err}`);
    }
  }

  async fixAllForeignKeys(): Promise<{ success: boolean; message: string; details: string[] }> {
    const details: string[] = [];
    
    try {
      // Define all foreign key fixes needed
      const foreignKeyFixes = [
        // Profiles table
        {
          table: 'profiles',
          oldConstraint: 'profiles_user_id_fkey',
          newConstraint: 'profiles_user_id_fkey',
          column: 'user_id',
          references: 'auth.users(id)' // This one should stay as auth.users
        },
        
        // Departments table
        {
          table: 'departments',
          oldConstraint: 'departments_manager_id_fkey',
          newConstraint: 'departments_manager_id_fkey',
          column: 'manager_id',
          references: 'public.profiles(id)'
        },
        
        // Projects table
        {
          table: 'projects',
          oldConstraint: 'projects_manager_id_fkey',
          newConstraint: 'projects_manager_id_fkey',
          column: 'manager_id',
          references: 'public.profiles(id)'
        },
        {
          table: 'projects',
          oldConstraint: 'projects_created_by_fkey',
          newConstraint: 'projects_created_by_fkey',
          column: 'created_by',
          references: 'public.profiles(id)'
        },
        
        // Tasks table
        {
          table: 'tasks',
          oldConstraint: 'tasks_assigned_to_id_fkey',
          newConstraint: 'tasks_assigned_to_id_fkey',
          column: 'assigned_to_id',
          references: 'public.profiles(id)'
        },
        {
          table: 'tasks',
          oldConstraint: 'tasks_created_by_fkey',
          newConstraint: 'tasks_created_by_fkey',
          column: 'created_by',
          references: 'public.profiles(id)'
        },
        
        // Time logs table
        {
          table: 'time_logs',
          oldConstraint: 'time_logs_user_id_fkey',
          newConstraint: 'time_logs_user_id_fkey',
          column: 'user_id',
          references: 'public.profiles(id)'
        },
        
        // Invoices table
        {
          table: 'invoices',
          oldConstraint: 'invoices_created_by_fkey',
          newConstraint: 'invoices_created_by_fkey',
          column: 'created_by',
          references: 'public.profiles(id)'
        },
        
        // Memos table
        {
          table: 'memos',
          oldConstraint: 'memos_created_by_fkey',
          newConstraint: 'memos_created_by_fkey',
          column: 'created_by',
          references: 'public.profiles(id)'
        },
        
        // Project assignments table
        {
          table: 'project_assignments',
          oldConstraint: 'project_assignments_assigned_to_fkey',
          newConstraint: 'project_assignments_assigned_to_fkey',
          column: 'assigned_to',
          references: 'public.profiles(id)'
        },
        
        // Task assignments table
        {
          table: 'task_assignments',
          oldConstraint: 'task_assignments_assigned_to_fkey',
          newConstraint: 'task_assignments_assigned_to_fkey',
          column: 'assigned_to',
          references: 'public.profiles(id)'
        },
        {
          table: 'task_assignments',
          oldConstraint: 'task_assignments_assigned_by_fkey',
          newConstraint: 'task_assignments_assigned_by_fkey',
          column: 'assigned_by',
          references: 'public.profiles(id)'
        },
        
        // Document archive table
        {
          table: 'document_archive',
          oldConstraint: 'document_archive_uploaded_by_fkey',
          newConstraint: 'document_archive_uploaded_by_fkey',
          column: 'uploaded_by',
          references: 'public.profiles(id)'
        },
        
        // Notifications table
        {
          table: 'notifications',
          oldConstraint: 'notifications_user_id_fkey',
          newConstraint: 'notifications_user_id_fkey',
          column: 'user_id',
          references: 'public.profiles(id)'
        },
        
        // User presence table
        {
          table: 'user_presence',
          oldConstraint: 'user_presence_user_id_fkey',
          newConstraint: 'user_presence_user_id_fkey',
          column: 'user_id',
          references: 'public.profiles(id)'
        },
        
        // Activity logs table
        {
          table: 'activity_logs',
          oldConstraint: 'activity_logs_user_id_fkey',
          newConstraint: 'activity_logs_user_id_fkey',
          column: 'user_id',
          references: 'public.profiles(id)'
        }
      ];

      details.push('Starting comprehensive foreign key fixes...');

      for (const fix of foreignKeyFixes) {
        try {
          // Skip profiles.user_id as it should reference auth.users
          if (fix.table === 'profiles' && fix.column === 'user_id') {
            details.push(`Skipping ${fix.table}.${fix.column} - should reference auth.users`);
            continue;
          }

          details.push(`Fixing ${fix.table}.${fix.column}...`);
          
          // Drop old constraint
          await this.dropConstraintIfExists(fix.table, fix.oldConstraint);
          
          // Add new constraint
          const constraintDef = `FOREIGN KEY (${fix.column}) REFERENCES ${fix.references} ON DELETE CASCADE`;
          await this.addConstraintIfNotExists(fix.table, fix.newConstraint, constraintDef);
          
          details.push(`✓ Fixed ${fix.table}.${fix.column} -> ${fix.references}`);
        } catch (error) {
          const errorMsg = `✗ Failed to fix ${fix.table}.${fix.column}: ${error}`;
          details.push(errorMsg);
          console.error(errorMsg);
        }
      }

      details.push('Comprehensive foreign key fixes completed!');
      return {
        success: true,
        message: 'All foreign key relationships have been updated successfully',
        details
      };
      
    } catch (error) {
      const errorMsg = `Failed to fix foreign keys: ${error}`;
      details.push(errorMsg);
      return {
        success: false,
        message: errorMsg,
        details
      };
    }
  }

  async checkAllConstraints(): Promise<{ success: boolean; constraints: any[]; message: string }> {
    try {
      const { data, error } = await supabase.rpc('execute_sql', {
        sql: `
          SELECT 
            tc.table_name,
            tc.constraint_name,
            kcu.column_name,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name,
            tc.constraint_type
          FROM information_schema.table_constraints tc
          JOIN information_schema.key_column_usage kcu 
            ON tc.constraint_name = kcu.constraint_name
          JOIN information_schema.constraint_column_usage ccu 
            ON ccu.constraint_name = tc.constraint_name
          WHERE tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_schema = 'public'
          ORDER BY tc.table_name, tc.constraint_name;
        `
      });

      if (error) {
        throw new Error(`Failed to check constraints: ${error.message}`);
      }

      return {
        success: true,
        constraints: data || [],
        message: 'Successfully retrieved all foreign key constraints'
      };
    } catch (error) {
      return {
        success: false,
        constraints: [],
        message: `Failed to check constraints: ${error}`
      };
    }
  }
}

export const comprehensiveForeignKeyFixer = new ComprehensiveForeignKeyFixer();