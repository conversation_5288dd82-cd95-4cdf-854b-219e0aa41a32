/**
 * Enhanced Staff Management Component
 * Comprehensive staff management with role assignment and department organization
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { 
  Users, 
  UserPlus, 
  Edit, 
  Trash2, 
  Search, 
  Filter,
  Building2,
  Mail,
  Phone,
  Calendar,
  Shield,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface StaffMember {
  id: string;
  full_name: string;
  email: string;
  role: string;
  department_id?: string;
  department_name?: string;
  phone?: string;
  hire_date?: string;
  status: 'active' | 'inactive' | 'pending';
  last_login?: string;
  avatar_url?: string;
}

interface Department {
  id: string;
  name: string;
  manager_id?: string;
  member_count?: number;
}

export const EnhancedStaffManagement: React.FC = () => {
  const [staffMembers, setStaffMembers] = useState<StaffMember[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [filteredStaff, setFilteredStaff] = useState<StaffMember[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState('all');
  const [filterDepartment, setFilterDepartment] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [isLoading, setIsLoading] = useState(true);
  const [selectedMember, setSelectedMember] = useState<StaffMember | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadStaffMembers();
    loadDepartments();
  }, []);

  useEffect(() => {
    filterStaffMembers();
  }, [staffMembers, searchTerm, filterRole, filterDepartment, filterStatus]);

  const loadStaffMembers = async () => {
    try {
      setIsLoading(true);
      
      // Get profiles with department information
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select(`
          id,
          full_name,
          email,
          role,
          department_id,
          phone,
          hire_date,
          status,
          last_login,
          avatar_url
        `)
        .order('full_name');

      if (profilesError) throw profilesError;

      // Get department names
      const { data: departments, error: deptError } = await supabase
        .from('departments')
        .select('id, name');

      const departmentMap = new Map();
      if (!deptError && departments) {
        departments.forEach(dept => {
          departmentMap.set(dept.id, dept.name);
        });
      }

      // Combine data
      const staffWithDepartments = (profiles || []).map(profile => ({
        ...profile,
        department_name: profile.department_id ? departmentMap.get(profile.department_id) : 'Unassigned',
        status: profile.status || 'active'
      }));

      setStaffMembers(staffWithDepartments);
    } catch (error: any) {
      console.error('Error loading staff members:', error);
      toast({
        title: "Error",
        description: "Failed to load staff members",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadDepartments = async () => {
    try {
      const { data, error } = await supabase
        .from('departments')
        .select('id, name, manager_id')
        .order('name');

      if (error) {
        // If departments table doesn't exist, create mock data
        setDepartments([
          { id: '1', name: 'Engineering', member_count: 0 },
          { id: '2', name: 'Marketing', member_count: 0 },
          { id: '3', name: 'Sales', member_count: 0 },
          { id: '4', name: 'Operations', member_count: 0 }
        ]);
        return;
      }

      // Count members in each department
      const departmentsWithCounts = (data || []).map(dept => ({
        ...dept,
        member_count: staffMembers.filter(member => member.department_id === dept.id).length
      }));

      setDepartments(departmentsWithCounts);
    } catch (error: any) {
      console.error('Error loading departments:', error);
      setDepartments([
        { id: '1', name: 'Engineering', member_count: 0 },
        { id: '2', name: 'Marketing', member_count: 0 },
        { id: '3', name: 'Sales', member_count: 0 },
        { id: '4', name: 'Operations', member_count: 0 }
      ]);
    }
  };

  const filterStaffMembers = () => {
    let filtered = staffMembers;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(member =>
        member.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.role.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Role filter
    if (filterRole !== 'all') {
      filtered = filtered.filter(member => member.role === filterRole);
    }

    // Department filter
    if (filterDepartment !== 'all') {
      filtered = filtered.filter(member => member.department_id === filterDepartment);
    }

    // Status filter
    if (filterStatus !== 'all') {
      filtered = filtered.filter(member => member.status === filterStatus);
    }

    setFilteredStaff(filtered);
  };

  const updateMemberRole = async (memberId: string, newRole: string) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ role: newRole, updated_at: new Date().toISOString() })
        .eq('id', memberId);

      if (error) throw error;

      // Update local state
      setStaffMembers(prev => prev.map(member =>
        member.id === memberId ? { ...member, role: newRole } : member
      ));

      toast({
        title: "Success",
        description: "Member role updated successfully",
      });
    } catch (error: any) {
      console.error('Error updating role:', error);
      toast({
        title: "Error",
        description: "Failed to update member role",
        variant: "destructive",
      });
    }
  };

  const updateMemberDepartment = async (memberId: string, departmentId: string) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ department_id: departmentId, updated_at: new Date().toISOString() })
        .eq('id', memberId);

      if (error) throw error;

      // Update local state
      const departmentName = departments.find(d => d.id === departmentId)?.name || 'Unassigned';
      setStaffMembers(prev => prev.map(member =>
        member.id === memberId ? { ...member, department_id: departmentId, department_name: departmentName } : member
      ));

      toast({
        title: "Success",
        description: "Member department updated successfully",
      });
    } catch (error: any) {
      console.error('Error updating department:', error);
      toast({
        title: "Error",
        description: "Failed to update member department",
        variant: "destructive",
      });
    }
  };

  const updateMemberStatus = async (memberId: string, status: 'active' | 'inactive') => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ status, updated_at: new Date().toISOString() })
        .eq('id', memberId);

      if (error) throw error;

      // Update local state
      setStaffMembers(prev => prev.map(member =>
        member.id === memberId ? { ...member, status } : member
      ));

      toast({
        title: "Success",
        description: `Member ${status === 'active' ? 'activated' : 'deactivated'} successfully`,
      });
    } catch (error: any) {
      console.error('Error updating status:', error);
      toast({
        title: "Error",
        description: "Failed to update member status",
        variant: "destructive",
      });
    }
  };

  const getRoleColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'admin': return 'bg-red-100 text-red-700 border-red-200';
      case 'manager': return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'staff': return 'bg-green-100 text-green-700 border-green-200';
      case 'intern': return 'bg-purple-100 text-purple-700 border-purple-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'inactive': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'pending': return <Clock className="h-4 w-4 text-yellow-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const roles = ['admin', 'manager', 'staff', 'intern', 'contractor'];

  return (
    <div className="space-y-6" data-component="EnhancedStaffManagement">
      {/* Header */}
      <Card className={cn(
        "border-[0.8px] border-[#ff1c04]/20",
        "bg-gradient-to-br from-[#1a1a1a] via-[#0f0f0f] to-[#1a1a1a]",
        "shadow-[8px_8px_16px_rgba(0,0,0,0.4),-8px_-8px_16px_rgba(255,28,4,0.1)]"
      )}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between text-[#e5e5e5]">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-xl bg-gradient-to-br from-[#ff1c04]/10 to-[#ff1c04]/20 border border-[#ff1c04]/20">
                <Users className="h-6 w-6 text-[#ff1c04]" />
              </div>
              <div>
                <h2 className="text-2xl font-bold">STAFF MANAGEMENT</h2>
                <p className="text-sm text-[#a5a5a5] font-normal">Manage team members, roles, and departments</p>
              </div>
            </div>
            <Button
              onClick={() => setShowAddForm(true)}
              className="bg-[#ff1c04] hover:bg-[#ff1c04]/90 text-white"
            >
              <UserPlus className="h-4 w-4 mr-2" />
              Add Member
            </Button>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Filters */}
      <Card className={cn(
        "border-[0.8px] border-[#ff1c04]/20",
        "bg-gradient-to-br from-[#1a1a1a] via-[#0f0f0f] to-[#1a1a1a]",
        "shadow-[8px_8px_16px_rgba(0,0,0,0.4),-8px_-8px_16px_rgba(255,28,4,0.1)]"
      )}>
        <CardContent className="p-6">
          <div className="grid md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-[#e5e5e5]">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[#a5a5a5]" />
                <Input
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search staff..."
                  className="pl-10 bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-[#e5e5e5]">Role</label>
              <Select value={filterRole} onValueChange={setFilterRole}>
                <SelectTrigger className="bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  {roles.map(role => (
                    <SelectItem key={role} value={role}>
                      {role.charAt(0).toUpperCase() + role.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-[#e5e5e5]">Department</label>
              <Select value={filterDepartment} onValueChange={setFilterDepartment}>
                <SelectTrigger className="bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  {departments.map(dept => (
                    <SelectItem key={dept.id} value={dept.id}>
                      {dept.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-[#e5e5e5]">Status</label>
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Staff List */}
      <Card className={cn(
        "border-[0.8px] border-[#ff1c04]/20",
        "bg-gradient-to-br from-[#1a1a1a] via-[#0f0f0f] to-[#1a1a1a]",
        "shadow-[8px_8px_16px_rgba(0,0,0,0.4),-8px_-8px_16px_rgba(255,28,4,0.1)]"
      )}>
        <CardHeader>
          <CardTitle className="text-[#e5e5e5]">
            STAFF MEMBERS ({filteredStaff.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#ff1c04] mx-auto"></div>
              <p className="text-[#a5a5a5] mt-2">Loading staff members...</p>
            </div>
          ) : filteredStaff.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-[#a5a5a5] mx-auto mb-4" />
              <p className="text-[#a5a5a5]">No staff members found</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredStaff.map(member => (
                <div
                  key={member.id}
                  className="p-4 rounded-lg border border-[#ff1c04]/20 bg-[#1a1a1a] hover:bg-[#2a2a2a] transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 rounded-full bg-gradient-to-br from-[#ff1c04]/20 to-[#ff1c04]/40 flex items-center justify-center">
                        <span className="text-[#ff1c04] font-bold text-lg">
                          {member.full_name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      
                      <div>
                        <h3 className="font-semibold text-[#e5e5e5]">{member.full_name}</h3>
                        <div className="flex items-center gap-2 text-sm text-[#a5a5a5]">
                          <Mail className="h-3 w-3" />
                          {member.email}
                        </div>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge className={getRoleColor(member.role)}>
                            {member.role.toUpperCase()}
                          </Badge>
                          <Badge variant="outline" className="border-[#ff1c04]/30 text-[#ff1c04]">
                            {member.department_name}
                          </Badge>
                          <div className="flex items-center gap-1">
                            {getStatusIcon(member.status)}
                            <span className="text-xs text-[#a5a5a5]">{member.status}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Select
                        value={member.role}
                        onValueChange={(value) => updateMemberRole(member.id, value)}
                      >
                        <SelectTrigger className="w-32 bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {roles.map(role => (
                            <SelectItem key={role} value={role}>
                              {role.charAt(0).toUpperCase() + role.slice(1)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>

                      <Select
                        value={member.department_id || ''}
                        onValueChange={(value) => updateMemberDepartment(member.id, value)}
                      >
                        <SelectTrigger className="w-40 bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {departments.map(dept => (
                            <SelectItem key={dept.id} value={dept.id}>
                              {dept.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>

                      <Button
                        onClick={() => updateMemberStatus(
                          member.id, 
                          member.status === 'active' ? 'inactive' : 'active'
                        )}
                        variant="outline"
                        size="sm"
                        className={cn(
                          "border-[#ff1c04]/30",
                          member.status === 'active' 
                            ? "text-red-500 hover:bg-red-500/10" 
                            : "text-green-500 hover:bg-green-500/10"
                        )}
                      >
                        {member.status === 'active' ? 'Deactivate' : 'Activate'}
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
