/**
 * Real-time Document Collaboration
 * Handles live document editing, comments, and version control
 */

import { supabase } from '@/integrations/supabase/client';
import { realtimeService } from './realtime-service';

export interface DocumentOperation {
  id: string;
  type: 'insert' | 'delete' | 'retain' | 'format';
  position: number;
  content?: string;
  length?: number;
  attributes?: Record<string, any>;
  userId: string;
  timestamp: Date;
}

export interface DocumentComment {
  id: string;
  documentId: string;
  userId: string;
  userName: string;
  content: string;
  position: number;
  resolved: boolean;
  replies: DocumentComment[];
  createdAt: Date;
  updatedAt: Date;
}

export interface DocumentVersion {
  id: string;
  documentId: string;
  version: number;
  content: string;
  operations: DocumentOperation[];
  createdBy: string;
  createdAt: Date;
  description?: string;
}

export interface DocumentCursor {
  userId: string;
  userName: string;
  position: number;
  selection?: { start: number; end: number };
  color: string;
}

export interface CollaborativeDocument {
  id: string;
  title: string;
  content: string;
  version: number;
  cursors: Map<string, DocumentCursor>;
  comments: DocumentComment[];
  operations: DocumentOperation[];
  lastSaved: Date;
  isLocked: boolean;
  lockedBy?: string;
}

export class DocumentCollaborationService {
  private documents = new Map<string, CollaborativeDocument>();
  private operationQueue = new Map<string, DocumentOperation[]>();
  private saveTimeouts = new Map<string, NodeJS.Timeout>();
  private userColors = new Map<string, string>();

  /**
   * Initialize document collaboration
   */
  async initializeDocument(documentId: string): Promise<CollaborativeDocument> {
    try {
      // Load document from database
      const { data: docData, error: docError } = await supabase
        .from('documents')
        .select('*')
        .eq('id', documentId)
        .single();

      if (docError) {
        throw docError;
      }

      // Load comments
      const { data: comments, error: commentsError } = await supabase
        .from('document_comments')
        .select(`
          *,
          user:profiles(full_name),
          replies:document_comments!parent_id(*)
        `)
        .eq('document_id', documentId)
        .is('parent_id', null)
        .order('created_at');

      if (commentsError) {
        console.error('Failed to load comments:', commentsError);
      }

      // Create collaborative document
      const collaborativeDoc: CollaborativeDocument = {
        id: documentId,
        title: docData.title || 'Untitled Document',
        content: docData.content || '',
        version: docData.version || 1,
        cursors: new Map(),
        comments: this.formatComments(comments || []),
        operations: [],
        lastSaved: new Date(docData.updated_at),
        isLocked: false,
      };

      this.documents.set(documentId, collaborativeDoc);

      // Join collaboration session
      await realtimeService.joinSession(
        `document-${documentId}`,
        'document',
        documentId
      );

      // Subscribe to document events
      this.subscribeToDocumentEvents(documentId);

      return collaborativeDoc;
    } catch (error) {
      console.error('Failed to initialize document:', error);
      throw error;
    }
  }

  /**
   * Subscribe to document-specific events
   */
  private subscribeToDocumentEvents(documentId: string): void {
    // Listen for operations
    realtimeService.addEventListener('document_operation', (event) => {
      if (event.sessionId === `document-${documentId}`) {
        this.handleRemoteOperation(documentId, event.payload.operation);
      }
    });

    // Listen for cursor updates
    realtimeService.addEventListener('cursor_update', (event) => {
      if (event.sessionId === `document-${documentId}`) {
        this.handleCursorUpdate(documentId, event.payload.cursor);
      }
    });

    // Listen for comments
    realtimeService.addEventListener('comment_added', (event) => {
      if (event.sessionId === `document-${documentId}`) {
        this.handleCommentAdded(documentId, event.payload.comment);
      }
    });

    // Listen for locks
    realtimeService.addEventListener('document_locked', (event) => {
      if (event.sessionId === `document-${documentId}`) {
        this.handleDocumentLocked(documentId, event.payload);
      }
    });
  }

  /**
   * Apply operation to document
   */
  async applyOperation(documentId: string, operation: Omit<DocumentOperation, 'id' | 'timestamp'>): Promise<void> {
    const doc = this.documents.get(documentId);
    if (!doc || doc.isLocked) {
      return;
    }

    const fullOperation: DocumentOperation = {
      ...operation,
      id: `op_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`,
      timestamp: new Date(),
    };

    // Apply operation locally
    this.applyOperationToDocument(doc, fullOperation);

    // Add to operation queue
    if (!this.operationQueue.has(documentId)) {
      this.operationQueue.set(documentId, []);
    }
    this.operationQueue.get(documentId)!.push(fullOperation);

    // Broadcast operation
    await realtimeService.broadcastToSession(
      `document-${documentId}`,
      'document_operation',
      { operation: fullOperation }
    );

    // Schedule save
    this.scheduleSave(documentId);
  }

  /**
   * Apply operation to document content
   */
  private applyOperationToDocument(doc: CollaborativeDocument, operation: DocumentOperation): void {
    switch (operation.type) {
      case 'insert':
        if (operation.content) {
          doc.content = 
            doc.content.slice(0, operation.position) +
            operation.content +
            doc.content.slice(operation.position);
        }
        break;

      case 'delete':
        if (operation.length) {
          doc.content = 
            doc.content.slice(0, operation.position) +
            doc.content.slice(operation.position + operation.length);
        }
        break;

      case 'retain':
        // No content change, just position tracking
        break;

      case 'format':
        // Handle formatting operations
        break;
    }

    doc.operations.push(operation);
    doc.version++;
  }

  /**
   * Handle remote operation
   */
  private handleRemoteOperation(documentId: string, operation: DocumentOperation): void {
    const doc = this.documents.get(documentId);
    if (!doc) return;

    // Transform operation against local operations if needed
    const transformedOperation = this.transformOperation(doc, operation);
    
    // Apply transformed operation
    this.applyOperationToDocument(doc, transformedOperation);

    // Emit event for UI updates
    this.emitDocumentUpdate(documentId);
  }

  /**
   * Transform operation for conflict resolution
   */
  private transformOperation(doc: CollaborativeDocument, operation: DocumentOperation): DocumentOperation {
    // Simple transformation - in production, use operational transform algorithms
    const localOps = this.operationQueue.get(doc.id) || [];
    let transformedOp = { ...operation };

    for (const localOp of localOps) {
      if (localOp.timestamp > operation.timestamp) {
        // Adjust position based on local operations
        if (localOp.type === 'insert' && localOp.position <= transformedOp.position) {
          transformedOp.position += localOp.content?.length || 0;
        } else if (localOp.type === 'delete' && localOp.position < transformedOp.position) {
          transformedOp.position -= localOp.length || 0;
        }
      }
    }

    return transformedOp;
  }

  /**
   * Update cursor position
   */
  async updateCursor(
    documentId: string,
    position: number,
    selection?: { start: number; end: number }
  ): Promise<void> {
    const doc = this.documents.get(documentId);
    if (!doc) return;

    const currentPresence = realtimeService.getCurrentPresence();
    if (!currentPresence) return;

    const cursor: DocumentCursor = {
      userId: currentPresence.userId,
      userName: currentPresence.userName,
      position,
      selection,
      color: this.getUserColor(currentPresence.userId),
    };

    doc.cursors.set(currentPresence.userId, cursor);

    // Broadcast cursor update
    await realtimeService.broadcastToSession(
      `document-${documentId}`,
      'cursor_update',
      { cursor }
    );
  }

  /**
   * Handle cursor update from other users
   */
  private handleCursorUpdate(documentId: string, cursor: DocumentCursor): void {
    const doc = this.documents.get(documentId);
    if (!doc) return;

    doc.cursors.set(cursor.userId, cursor);
    this.emitDocumentUpdate(documentId);
  }

  /**
   * Add comment to document
   */
  async addComment(
    documentId: string,
    content: string,
    position: number,
    parentId?: string
  ): Promise<DocumentComment> {
    try {
      const currentPresence = realtimeService.getCurrentPresence();
      if (!currentPresence) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('document_comments')
        .insert({
          document_id: documentId,
          user_id: currentPresence.userId,
          content,
          position,
          parent_id: parentId,
        })
        .select(`
          *,
          user:profiles(full_name)
        `)
        .single();

      if (error) {
        throw error;
      }

      const comment: DocumentComment = {
        id: data.id,
        documentId,
        userId: data.user_id,
        userName: data.user.full_name,
        content: data.content,
        position: data.position,
        resolved: data.resolved,
        replies: [],
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
      };

      // Add to local document
      const doc = this.documents.get(documentId);
      if (doc) {
        if (parentId) {
          const parentComment = doc.comments.find(c => c.id === parentId);
          if (parentComment) {
            parentComment.replies.push(comment);
          }
        } else {
          doc.comments.push(comment);
        }
      }

      // Broadcast comment
      await realtimeService.broadcastToSession(
        `document-${documentId}`,
        'comment_added',
        { comment }
      );

      return comment;
    } catch (error) {
      console.error('Failed to add comment:', error);
      throw error;
    }
  }

  /**
   * Handle comment added by other users
   */
  private handleCommentAdded(documentId: string, comment: DocumentComment): void {
    const doc = this.documents.get(documentId);
    if (!doc) return;

    if (comment.replies && comment.replies.length > 0) {
      // This is a reply
      const parentComment = doc.comments.find(c => c.id === comment.id);
      if (parentComment) {
        parentComment.replies.push(comment);
      }
    } else {
      // This is a new comment
      doc.comments.push(comment);
    }

    this.emitDocumentUpdate(documentId);
  }

  /**
   * Resolve comment
   */
  async resolveComment(documentId: string, commentId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('document_comments')
        .update({ resolved: true })
        .eq('id', commentId);

      if (error) {
        throw error;
      }

      // Update local document
      const doc = this.documents.get(documentId);
      if (doc) {
        const comment = doc.comments.find(c => c.id === commentId);
        if (comment) {
          comment.resolved = true;
        }
      }

      // Broadcast update
      await realtimeService.broadcastToSession(
        `document-${documentId}`,
        'comment_resolved',
        { commentId }
      );
    } catch (error) {
      console.error('Failed to resolve comment:', error);
      throw error;
    }
  }

  /**
   * Lock document for exclusive editing
   */
  async lockDocument(documentId: string): Promise<boolean> {
    const doc = this.documents.get(documentId);
    if (!doc || doc.isLocked) {
      return false;
    }

    const currentPresence = realtimeService.getCurrentPresence();
    if (!currentPresence) {
      return false;
    }

    doc.isLocked = true;
    doc.lockedBy = currentPresence.userId;

    // Broadcast lock
    await realtimeService.broadcastToSession(
      `document-${documentId}`,
      'document_locked',
      {
        lockedBy: currentPresence.userId,
        lockedByName: currentPresence.userName,
      }
    );

    return true;
  }

  /**
   * Unlock document
   */
  async unlockDocument(documentId: string): Promise<void> {
    const doc = this.documents.get(documentId);
    if (!doc) return;

    doc.isLocked = false;
    doc.lockedBy = undefined;

    // Broadcast unlock
    await realtimeService.broadcastToSession(
      `document-${documentId}`,
      'document_unlocked',
      {}
    );
  }

  /**
   * Handle document locked by other user
   */
  private handleDocumentLocked(documentId: string, payload: any): void {
    const doc = this.documents.get(documentId);
    if (!doc) return;

    doc.isLocked = true;
    doc.lockedBy = payload.lockedBy;

    this.emitDocumentUpdate(documentId);
  }

  /**
   * Save document to database
   */
  private async saveDocument(documentId: string): Promise<void> {
    const doc = this.documents.get(documentId);
    if (!doc) return;

    try {
      const { error } = await supabase
        .from('documents')
        .update({
          content: doc.content,
          version: doc.version,
          updated_at: new Date().toISOString(),
        })
        .eq('id', documentId);

      if (error) {
        console.error('Failed to save document:', error);
        return;
      }

      // Clear operation queue
      this.operationQueue.delete(documentId);
      doc.lastSaved = new Date();

      console.log(`Document ${documentId} saved successfully`);
    } catch (error) {
      console.error('Failed to save document:', error);
    }
  }

  /**
   * Schedule document save
   */
  private scheduleSave(documentId: string): void {
    // Clear existing timeout
    const existingTimeout = this.saveTimeouts.get(documentId);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Schedule new save
    const timeout = setTimeout(() => {
      this.saveDocument(documentId);
      this.saveTimeouts.delete(documentId);
    }, 2000); // Save after 2 seconds of inactivity

    this.saveTimeouts.set(documentId, timeout);
  }

  /**
   * Get user color for cursor
   */
  private getUserColor(userId: string): string {
    if (!this.userColors.has(userId)) {
      const colors = [
        '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
        '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
      ];
      const color = colors[Math.floor(Math.random() * colors.length)];
      this.userColors.set(userId, color);
    }
    return this.userColors.get(userId)!;
  }

  /**
   * Format comments from database
   */
  private formatComments(comments: any[]): DocumentComment[] {
    return comments.map(comment => ({
      id: comment.id,
      documentId: comment.document_id,
      userId: comment.user_id,
      userName: comment.user?.full_name || 'Unknown User',
      content: comment.content,
      position: comment.position,
      resolved: comment.resolved,
      replies: comment.replies?.map((reply: any) => ({
        id: reply.id,
        documentId: reply.document_id,
        userId: reply.user_id,
        userName: reply.user?.full_name || 'Unknown User',
        content: reply.content,
        position: reply.position,
        resolved: reply.resolved,
        replies: [],
        createdAt: new Date(reply.created_at),
        updatedAt: new Date(reply.updated_at),
      })) || [],
      createdAt: new Date(comment.created_at),
      updatedAt: new Date(comment.updated_at),
    }));
  }

  /**
   * Emit document update event
   */
  private emitDocumentUpdate(documentId: string): void {
    const doc = this.documents.get(documentId);
    if (doc) {
      // Emit custom event for UI updates
      window.dispatchEvent(new CustomEvent('documentUpdate', {
        detail: { documentId, document: doc }
      }));
    }
  }

  /**
   * Get document
   */
  getDocument(documentId: string): CollaborativeDocument | undefined {
    return this.documents.get(documentId);
  }

  /**
   * Cleanup document collaboration
   */
  async cleanup(documentId: string): Promise<void> {
    // Save any pending changes
    await this.saveDocument(documentId);

    // Clear timeouts
    const timeout = this.saveTimeouts.get(documentId);
    if (timeout) {
      clearTimeout(timeout);
      this.saveTimeouts.delete(documentId);
    }

    // Leave session
    await realtimeService.leaveSession(`document-${documentId}`);

    // Remove from memory
    this.documents.delete(documentId);
    this.operationQueue.delete(documentId);
  }
}

export const documentCollaboration = new DocumentCollaborationService();
