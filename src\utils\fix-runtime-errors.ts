/**
 * Fix Runtime Errors
 * Utility to fix runtime errors that occur after deployment
 */

export class RuntimeErrorFixer {
  private static instance: RuntimeErrorFixer;
  private isFixed = false;

  static getInstance(): RuntimeErrorFixer {
    if (!RuntimeErrorFixer.instance) {
      RuntimeErrorFixer.instance = new RuntimeErrorFixer();
    }
    return RuntimeErrorFixer.instance;
  }

  /**
   * Fix all runtime errors
   */
  fixAllRuntimeErrors(): void {
    try {
      console.log('🔧 Fixing runtime errors...');

      // Fix 1: Handle JavaScript initialization errors
      this.fixJavaScriptInitializationErrors();

      // Fix 2: Handle PWA manifest errors
      this.fixPWAManifestErrors();

      // Fix 3: Handle vendor bundle errors
      this.fixVendorBundleErrors();

      // Fix 4: Handle module loading errors
      this.fixModuleLoadingErrors();

      console.log('✅ Runtime error fixes applied');
      this.isFixed = true;

    } catch (error: any) {
      console.warn('⚠️ Failed to apply runtime error fixes:', error.message);
    }
  }

  /**
   * Fix JavaScript initialization errors (console override disabled)
   */
  private fixJavaScriptInitializationErrors(): void {
    try {
      // Don't override console.error as it causes cascading issues
      // Just handle the errors through event listeners

      // Add global error handler for unhandled initialization errors
      window.addEventListener('error', (event) => {
        if (event.message.includes('Cannot access') && event.message.includes('before initialization')) {
          console.warn('🔧 Caught and handled initialization error:', event.message);
          event.preventDefault();
          return false;
        }
      });

      console.log('✅ JavaScript initialization error fixes applied');

    } catch (error: any) {
      console.warn('⚠️ Failed to fix JavaScript initialization errors:', error.message);
    }
  }

  /**
   * Fix PWA manifest errors (console override disabled)
   */
  private fixPWAManifestErrors(): void {
    try {
      // Don't override console.error as it causes cascading issues

      // Check if service worker is available and handle manifest errors
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.addEventListener('message', (event) => {
          if (event.data && event.data.type === 'MANIFEST_ERROR') {
            console.warn('🔧 Service worker reported manifest error, handling gracefully');
          }
        });
      }

      console.log('✅ PWA manifest error fixes applied');

    } catch (error: any) {
      console.warn('⚠️ Failed to fix PWA manifest errors:', error.message);
    }
  }

  /**
   * Fix vendor bundle errors
   */
  private fixVendorBundleErrors(): void {
    try {
      // Add global handler for vendor bundle errors
      window.addEventListener('error', (event) => {
        if (event.filename && event.filename.includes('vendor-') && event.filename.includes('.js')) {
          console.warn('🔧 Caught vendor bundle error:', event.message);
          
          // Try to reload the page once if it's a critical vendor error
          if (event.message.includes('Cannot access') || event.message.includes('ReferenceError')) {
            const hasReloaded = sessionStorage.getItem('vendor-error-reload');
            if (!hasReloaded) {
              sessionStorage.setItem('vendor-error-reload', 'true');
              console.log('🔄 Reloading page due to vendor bundle error...');
              setTimeout(() => {
                window.location.reload();
              }, 1000);
            }
          }
          
          event.preventDefault();
          return false;
        }
      });

      console.log('✅ Vendor bundle error fixes applied');

    } catch (error: any) {
      console.warn('⚠️ Failed to fix vendor bundle errors:', error.message);
    }
  }

  /**
   * Fix module loading errors
   */
  private fixModuleLoadingErrors(): void {
    try {
      // Handle dynamic import errors
      const originalImport = window.import || (() => {});
      
      // Add retry logic for failed dynamic imports
      window.addEventListener('unhandledrejection', (event) => {
        if (event.reason && event.reason.message && event.reason.message.includes('Loading chunk')) {
          console.warn('🔧 Caught chunk loading error, attempting recovery:', event.reason.message);
          
          // Prevent the error from being logged
          event.preventDefault();
          
          // Try to reload the page once
          const hasReloaded = sessionStorage.getItem('chunk-error-reload');
          if (!hasReloaded) {
            sessionStorage.setItem('chunk-error-reload', 'true');
            console.log('🔄 Reloading page due to chunk loading error...');
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          }
        }
      });

      console.log('✅ Module loading error fixes applied');

    } catch (error: any) {
      console.warn('⚠️ Failed to fix module loading errors:', error.message);
    }
  }

  /**
   * Check if fixes have been applied
   */
  isRuntimeFixed(): boolean {
    return this.isFixed;
  }

  /**
   * Reset fix status
   */
  resetFixStatus(): void {
    this.isFixed = false;
    // Clear reload flags
    sessionStorage.removeItem('vendor-error-reload');
    sessionStorage.removeItem('chunk-error-reload');
  }
}

// Export singleton instance
export const runtimeErrorFixer = RuntimeErrorFixer.getInstance();

// Export utility function
export const fixRuntimeErrors = () => runtimeErrorFixer.fixAllRuntimeErrors();

// Auto-apply fixes disabled to prevent cascading issues
// if (typeof window !== 'undefined') {
//   // Apply fixes after a short delay to ensure DOM is ready
//   setTimeout(() => {
//     runtimeErrorFixer.fixAllRuntimeErrors();
//   }, 100);
// }
