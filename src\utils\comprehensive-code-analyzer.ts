/**
 * Comprehensive Code Analyzer
 * Complete analysis of database schema, foreign keys, missing pages, and routes
 */

import { supabase } from '@/integrations/supabase/client';

interface DatabaseIssue {
  type: 'missing_table' | 'missing_column' | 'broken_fkey' | 'missing_fkey' | 'schema_mismatch';
  table: string;
  column?: string;
  referencedTable?: string;
  referencedColumn?: string;
  error: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  fix: string;
}

interface RouteIssue {
  type: 'missing_page' | 'missing_component' | 'broken_route' | 'missing_import';
  route: string;
  component?: string;
  error: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  fix: string;
}

interface AnalysisResult {
  databaseIssues: DatabaseIssue[];
  routeIssues: RouteIssue[];
  missingPages: string[];
  missingComponents: string[];
  brokenForeignKeys: string[];
  summary: {
    totalIssues: number;
    criticalIssues: number;
    highPriorityIssues: number;
    databaseHealth: 'healthy' | 'warning' | 'critical';
    routingHealth: 'healthy' | 'warning' | 'critical';
  };
}

export class ComprehensiveCodeAnalyzer {
  private static databaseIssues: DatabaseIssue[] = [];
  private static routeIssues: RouteIssue[] = [];

  /**
   * Run complete analysis
   */
  static async runFullAnalysis(): Promise<AnalysisResult> {
    console.log('🔍 Starting comprehensive code analysis...');
    
    this.databaseIssues = [];
    this.routeIssues = [];

    // Analyze database schema and foreign keys
    await this.analyzeDatabaseSchema();
    await this.analyzeForeignKeys();
    
    // Analyze routes and pages
    await this.analyzeRoutes();
    await this.analyzePages();
    
    // Generate summary
    const summary = this.generateSummary();
    
    console.log('✅ Comprehensive analysis completed');
    
    return {
      databaseIssues: this.databaseIssues,
      routeIssues: this.routeIssues,
      missingPages: this.extractMissingPages(),
      missingComponents: this.extractMissingComponents(),
      brokenForeignKeys: this.extractBrokenForeignKeys(),
      summary
    };
  }

  /**
   * Analyze database schema
   */
  private static async analyzeDatabaseSchema(): Promise<void> {
    console.log('🗄️ Analyzing database schema...');

    const expectedTables = {
      profiles: ['id', 'user_id', 'full_name', 'email', 'role', 'account_type', 'status', 'department_id', 'avatar_url', 'phone', 'position', 'hire_date', 'last_login', 'created_at', 'updated_at'],
      departments: ['id', 'name', 'description', 'manager_id', 'budget', 'location', 'created_at', 'updated_at'],
      projects: ['id', 'name', 'description', 'status', 'priority', 'start_date', 'end_date', 'budget', 'manager_id', 'department_id', 'created_by', 'progress', 'created_at', 'updated_at'],
      tasks: ['id', 'title', 'description', 'status', 'priority', 'due_date', 'assigned_to_id', 'project_id', 'created_by', 'estimated_hours', 'actual_hours', 'progress', 'created_at', 'updated_at'],
      time_logs: ['id', 'user_id', 'project_id', 'task_id', 'description', 'start_time', 'end_time', 'duration_minutes', 'is_billable', 'hourly_rate', 'status', 'created_at', 'updated_at'],
      invoices: ['id', 'invoice_number', 'client_name', 'amount', 'status', 'due_date', 'created_by', 'project_id', 'created_at', 'updated_at'],
      memos: ['id', 'title', 'content', 'priority', 'status', 'created_by', 'department_id', 'expires_at', 'created_at', 'updated_at'],
      project_assignments: ['id', 'project_id', 'assigned_to', 'role', 'status', 'start_date', 'end_date', 'progress_percentage', 'hours_allocated', 'hours_worked', 'created_at', 'updated_at'],
      task_assignments: ['id', 'task_id', 'assigned_to', 'assigned_by', 'status', 'assigned_at', 'completed_at', 'created_at', 'updated_at'],
      document_archive: ['id', 'title', 'file_name', 'file_path', 'file_size', 'file_type', 'file_url', 'description', 'tags', 'category', 'uploaded_by', 'project_id', 'department_id', 'is_public', 'download_count', 'created_at', 'updated_at'],
      notifications: ['id', 'user_id', 'title', 'message', 'type', 'status', 'action_url', 'metadata', 'created_at', 'updated_at'],
      user_presence: ['id', 'user_id', 'status', 'last_seen', 'location', 'device', 'created_at', 'updated_at'],
      activity_logs: ['id', 'user_id', 'action', 'entity_type', 'entity_id', 'description', 'metadata', 'ip_address', 'user_agent', 'created_at']
    };

    for (const [tableName, expectedColumns] of Object.entries(expectedTables)) {
      try {
        // Check if table exists
        const { data, error } = await supabase
          .from(tableName)
          .select('*')
          .limit(1);

        if (error) {
          if (error.message.includes('does not exist')) {
            this.databaseIssues.push({
              type: 'missing_table',
              table: tableName,
              error: `Table ${tableName} does not exist`,
              severity: 'critical',
              fix: `Create ${tableName} table with proper schema`
            });
          } else {
            this.databaseIssues.push({
              type: 'schema_mismatch',
              table: tableName,
              error: error.message,
              severity: 'high',
              fix: `Check RLS policies and permissions for ${tableName}`
            });
          }
          continue;
        }

        // Check columns if table exists and has data
        if (data && data.length > 0) {
          const actualColumns = Object.keys(data[0]);
          const missingColumns = expectedColumns.filter(col => !actualColumns.includes(col));
          
          for (const missingColumn of missingColumns) {
            this.databaseIssues.push({
              type: 'missing_column',
              table: tableName,
              column: missingColumn,
              error: `Column ${tableName}.${missingColumn} does not exist`,
              severity: 'medium',
              fix: `Add column ${missingColumn} to ${tableName} table or update queries to not use it`
            });
          }
        }

      } catch (err: any) {
        this.databaseIssues.push({
          type: 'schema_mismatch',
          table: tableName,
          error: `Failed to analyze ${tableName}: ${err.message}`,
          severity: 'high',
          fix: `Check database connection and table permissions`
        });
      }
    }
  }

  /**
   * Analyze foreign key relationships
   */
  private static async analyzeForeignKeys(): Promise<void> {
    console.log('🔗 Analyzing foreign key relationships...');

    const foreignKeyRelationships = [
      { table: 'profiles', column: 'user_id', referencedTable: 'auth.users', referencedColumn: 'id' },
      { table: 'profiles', column: 'department_id', referencedTable: 'departments', referencedColumn: 'id' },
      { table: 'departments', column: 'manager_id', referencedTable: 'auth.users', referencedColumn: 'id' },
      { table: 'projects', column: 'manager_id', referencedTable: 'auth.users', referencedColumn: 'id' },
      { table: 'projects', column: 'department_id', referencedTable: 'departments', referencedColumn: 'id' },
      { table: 'projects', column: 'created_by', referencedTable: 'auth.users', referencedColumn: 'id' },
      { table: 'tasks', column: 'assigned_to_id', referencedTable: 'auth.users', referencedColumn: 'id' },
      { table: 'tasks', column: 'project_id', referencedTable: 'projects', referencedColumn: 'id' },
      { table: 'tasks', column: 'created_by', referencedTable: 'auth.users', referencedColumn: 'id' },
      { table: 'time_logs', column: 'user_id', referencedTable: 'auth.users', referencedColumn: 'id' },
      { table: 'time_logs', column: 'project_id', referencedTable: 'projects', referencedColumn: 'id' },
      { table: 'time_logs', column: 'task_id', referencedTable: 'tasks', referencedColumn: 'id' },
      { table: 'invoices', column: 'created_by', referencedTable: 'auth.users', referencedColumn: 'id' },
      { table: 'invoices', column: 'project_id', referencedTable: 'projects', referencedColumn: 'id' },
      { table: 'memos', column: 'created_by', referencedTable: 'auth.users', referencedColumn: 'id' },
      { table: 'memos', column: 'department_id', referencedTable: 'departments', referencedColumn: 'id' },
      { table: 'project_assignments', column: 'project_id', referencedTable: 'projects', referencedColumn: 'id' },
      { table: 'project_assignments', column: 'assigned_to', referencedTable: 'auth.users', referencedColumn: 'id' },
      { table: 'task_assignments', column: 'task_id', referencedTable: 'tasks', referencedColumn: 'id' },
      { table: 'task_assignments', column: 'assigned_to', referencedTable: 'auth.users', referencedColumn: 'id' },
      { table: 'task_assignments', column: 'assigned_by', referencedTable: 'auth.users', referencedColumn: 'id' },
      { table: 'document_archive', column: 'uploaded_by', referencedTable: 'auth.users', referencedColumn: 'id' },
      { table: 'document_archive', column: 'project_id', referencedTable: 'projects', referencedColumn: 'id' },
      { table: 'document_archive', column: 'department_id', referencedTable: 'departments', referencedColumn: 'id' },
      { table: 'notifications', column: 'user_id', referencedTable: 'auth.users', referencedColumn: 'id' },
      { table: 'user_presence', column: 'user_id', referencedTable: 'auth.users', referencedColumn: 'id' },
      { table: 'activity_logs', column: 'user_id', referencedTable: 'auth.users', referencedColumn: 'id' }
    ];

    for (const fk of foreignKeyRelationships) {
      try {
        // Test foreign key relationship by trying to join
        const { error } = await supabase
          .from(fk.table)
          .select(`${fk.column}, ${fk.referencedTable.replace('auth.', '')}(${fk.referencedColumn})`)
          .limit(1);

        if (error) {
          if (error.message.includes('Could not find a relationship')) {
            this.databaseIssues.push({
              type: 'missing_fkey',
              table: fk.table,
              column: fk.column,
              referencedTable: fk.referencedTable,
              referencedColumn: fk.referencedColumn,
              error: `Foreign key relationship missing: ${fk.table}.${fk.column} -> ${fk.referencedTable}.${fk.referencedColumn}`,
              severity: 'high',
              fix: `Add foreign key constraint or use manual joins in queries`
            });
          } else {
            this.databaseIssues.push({
              type: 'broken_fkey',
              table: fk.table,
              column: fk.column,
              referencedTable: fk.referencedTable,
              referencedColumn: fk.referencedColumn,
              error: `Foreign key relationship broken: ${error.message}`,
              severity: 'medium',
              fix: `Fix foreign key constraint or update query structure`
            });
          }
        }
      } catch (err: any) {
        this.databaseIssues.push({
          type: 'broken_fkey',
          table: fk.table,
          column: fk.column,
          referencedTable: fk.referencedTable,
          referencedColumn: fk.referencedColumn,
          error: `Failed to test foreign key: ${err.message}`,
          severity: 'medium',
          fix: `Check foreign key constraint and table structure`
        });
      }
    }
  }

  /**
   * Analyze routes and navigation
   */
  private static async analyzeRoutes(): Promise<void> {
    console.log('🛣️ Analyzing routes and navigation...');

    const expectedRoutes = [
      // Main routes
      { route: '/dashboard', component: 'UnifiedDashboardLayout', severity: 'critical' as const },
      { route: '/dashboard/ai', component: 'AIPage', severity: 'high' as const },
      { route: '/auth', component: 'AuthPage', severity: 'critical' as const },
      { route: '/clock-in', component: 'ClockInPage', severity: 'high' as const },
      
      // Admin routes
      { route: '/dashboard/admin', component: 'AdminDashboard', severity: 'high' as const },
      { route: '/dashboard/admin/users', component: 'UserManagement', severity: 'medium' as const },
      { route: '/dashboard/admin/departments', component: 'DepartmentManagement', severity: 'medium' as const },
      { route: '/dashboard/admin/communication', component: 'CommunicationPage', severity: 'medium' as const },
      { route: '/dashboard/admin/database', component: 'DatabasePopulatePage', severity: 'medium' as const },
      { route: '/dashboard/admin/integrations', component: 'IntegrationsPage', severity: 'medium' as const },
      { route: '/dashboard/admin/diagnostics', component: 'SystemDiagnosticsPage', severity: 'medium' as const },
      { route: '/dashboard/admin/api-keys', component: 'APIKeysPage', severity: 'medium' as const },
      
      // Manager routes
      { route: '/dashboard/manager', component: 'ManagerDashboard', severity: 'high' as const },
      { route: '/dashboard/manager/projects', component: 'ProjectManagement', severity: 'medium' as const },
      { route: '/dashboard/manager/team', component: 'TeamManagement', severity: 'medium' as const },
      
      // Staff routes
      { route: '/dashboard/staff', component: 'StaffDashboard', severity: 'high' as const },
      { route: '/dashboard/staff/tasks', component: 'TaskManagement', severity: 'medium' as const },
      { route: '/dashboard/staff/time', component: 'TimeTracking', severity: 'medium' as const },
      { route: '/dashboard/staff/leave', component: 'LeaveRequest', severity: 'medium' as const },
      { route: '/dashboard/staff/memos', component: 'MemoManagement', severity: 'medium' as const },
      { route: '/dashboard/staff/reports', component: 'ReportManagement', severity: 'medium' as const },
      
      // Accountant routes
      { route: '/dashboard/accountant', component: 'AccountantDashboard', severity: 'high' as const },
      { route: '/dashboard/accountant/invoices', component: 'InvoiceManagement', severity: 'medium' as const },
      { route: '/dashboard/accountant/financial', component: 'FinancialManagement', severity: 'medium' as const },
      
      // Common routes
      { route: '/dashboard/projects', component: 'ProjectsPage', severity: 'medium' as const },
      { route: '/dashboard/tasks', component: 'TasksPage', severity: 'medium' as const },
      { route: '/dashboard/memos', component: 'MemosPage', severity: 'medium' as const },
      { route: '/dashboard/reports', component: 'ReportsPage', severity: 'medium' as const },
      { route: '/dashboard/settings', component: 'SettingsPage', severity: 'medium' as const },
      { route: '/dashboard/account', component: 'AccountPage', severity: 'medium' as const },
      { route: '/dashboard/files', component: 'FilesPage', severity: 'medium' as const },
      { route: '/dashboard/time-tracking', component: 'TimeTracking', severity: 'medium' as const },
      { route: '/dashboard/leave-request', component: 'LeaveRequest', severity: 'medium' as const }
    ];

    for (const routeInfo of expectedRoutes) {
      // Check if component exists (this is a simplified check)
      // In a real implementation, you'd check the actual file system
      this.routeIssues.push({
        type: 'missing_component',
        route: routeInfo.route,
        component: routeInfo.component,
        error: `Component ${routeInfo.component} may be missing or not properly imported`,
        severity: routeInfo.severity,
        fix: `Create or properly import ${routeInfo.component} component`
      });
    }
  }

  /**
   * Analyze pages and components
   */
  private static async analyzePages(): Promise<void> {
    console.log('📄 Analyzing pages and components...');

    // Known missing pages based on routes in UnifiedDashboardLayout
    const knownMissingPages = [
      { route: '/dashboard/admin/users', component: 'UserManagement', severity: 'high' as const },
      { route: '/dashboard/admin/departments', component: 'DepartmentManagement', severity: 'high' as const },
      { route: '/dashboard/admin/communication', component: 'CommunicationPage', severity: 'medium' as const },
      { route: '/dashboard/admin/database', component: 'DatabasePopulatePage', severity: 'medium' as const },
      { route: '/dashboard/admin/integrations', component: 'IntegrationsPage', severity: 'medium' as const },
      { route: '/dashboard/admin/diagnostics', component: 'SystemDiagnosticsPage', severity: 'medium' as const },
      { route: '/dashboard/admin/api-keys', component: 'APIKeysPage', severity: 'medium' as const },
      { route: '/dashboard/manager/projects', component: 'ProjectManagement', severity: 'high' as const },
      { route: '/dashboard/manager/team', component: 'TeamManagement', severity: 'high' as const },
      { route: '/dashboard/staff/tasks', component: 'TaskManagement', severity: 'high' as const },
      { route: '/dashboard/staff/memos', component: 'MemoManagement', severity: 'medium' as const },
      { route: '/dashboard/staff/reports', component: 'ReportManagement', severity: 'medium' as const },
      { route: '/dashboard/accountant/invoices', component: 'InvoiceManagement', severity: 'high' as const },
      { route: '/dashboard/accountant/financial', component: 'FinancialManagement', severity: 'medium' as const },
      { route: '/dashboard/projects', component: 'ProjectsPage', severity: 'medium' as const },
      { route: '/dashboard/tasks', component: 'TasksPage', severity: 'medium' as const },
      { route: '/dashboard/memos', component: 'MemosPage', severity: 'medium' as const },
      { route: '/dashboard/reports', component: 'ReportsPage', severity: 'medium' as const },
      { route: '/dashboard/settings', component: 'SettingsPage', severity: 'medium' as const },
      { route: '/dashboard/account', component: 'AccountPage', severity: 'medium' as const },
      { route: '/dashboard/files', component: 'FilesPage', severity: 'medium' as const },
      { route: '/dashboard/documents', component: 'DocumentsPage', severity: 'medium' as const },
      { route: '/dashboard/toolz', component: 'ToolzPage', severity: 'medium' as const },
      { route: '/dashboard/procurement', component: 'ProcurementPage', severity: 'low' as const }
    ];

    for (const page of knownMissingPages) {
      this.routeIssues.push({
        type: 'missing_page',
        route: page.route,
        component: page.component,
        error: `Page component ${page.component} is referenced in routes but may be missing`,
        severity: page.severity,
        fix: `Create ${page.component} component for route ${page.route}`
      });
    }
  }

  /**
   * Extract missing pages
   */
  private static extractMissingPages(): string[] {
    return this.routeIssues
      .filter(issue => issue.type === 'missing_page')
      .map(issue => issue.route);
  }

  /**
   * Extract missing components
   */
  private static extractMissingComponents(): string[] {
    return this.routeIssues
      .filter(issue => issue.type === 'missing_component')
      .map(issue => issue.component || 'Unknown');
  }

  /**
   * Extract broken foreign keys
   */
  private static extractBrokenForeignKeys(): string[] {
    return this.databaseIssues
      .filter(issue => issue.type === 'broken_fkey' || issue.type === 'missing_fkey')
      .map(issue => `${issue.table}.${issue.column} -> ${issue.referencedTable}.${issue.referencedColumn}`);
  }

  /**
   * Generate analysis summary
   */
  private static generateSummary() {
    const totalIssues = this.databaseIssues.length + this.routeIssues.length;
    const criticalIssues = [...this.databaseIssues, ...this.routeIssues]
      .filter(issue => issue.severity === 'critical').length;
    const highPriorityIssues = [...this.databaseIssues, ...this.routeIssues]
      .filter(issue => issue.severity === 'high').length;

    const databaseHealth = criticalIssues > 0 ? 'critical' : 
                          highPriorityIssues > 3 ? 'warning' : 'healthy';
    
    const routingHealth = this.routeIssues.length > 10 ? 'critical' :
                         this.routeIssues.length > 5 ? 'warning' : 'healthy';

    return {
      totalIssues,
      criticalIssues,
      highPriorityIssues,
      databaseHealth: databaseHealth as 'healthy' | 'warning' | 'critical',
      routingHealth: routingHealth as 'healthy' | 'warning' | 'critical'
    };
  }

  /**
   * Generate detailed report
   */
  static generateReport(analysis: AnalysisResult): string {
    let report = '# COMPREHENSIVE CODE ANALYSIS REPORT\n\n';
    
    report += `## SUMMARY\n`;
    report += `- Total Issues: ${analysis.summary.totalIssues}\n`;
    report += `- Critical Issues: ${analysis.summary.criticalIssues}\n`;
    report += `- High Priority Issues: ${analysis.summary.highPriorityIssues}\n`;
    report += `- Database Health: ${analysis.summary.databaseHealth.toUpperCase()}\n`;
    report += `- Routing Health: ${analysis.summary.routingHealth.toUpperCase()}\n\n`;

    report += `## DATABASE ISSUES (${analysis.databaseIssues.length})\n`;
    for (const issue of analysis.databaseIssues) {
      report += `### ${issue.severity.toUpperCase()}: ${issue.type}\n`;
      report += `- Table: ${issue.table}\n`;
      if (issue.column) report += `- Column: ${issue.column}\n`;
      if (issue.referencedTable) report += `- References: ${issue.referencedTable}.${issue.referencedColumn}\n`;
      report += `- Error: ${issue.error}\n`;
      report += `- Fix: ${issue.fix}\n\n`;
    }

    report += `## ROUTING ISSUES (${analysis.routeIssues.length})\n`;
    for (const issue of analysis.routeIssues) {
      report += `### ${issue.severity.toUpperCase()}: ${issue.type}\n`;
      report += `- Route: ${issue.route}\n`;
      if (issue.component) report += `- Component: ${issue.component}\n`;
      report += `- Error: ${issue.error}\n`;
      report += `- Fix: ${issue.fix}\n\n`;
    }

    return report;
  }
}

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).comprehensiveCodeAnalyzer = ComprehensiveCodeAnalyzer;
  (window as any).runFullCodeAnalysis = () => ComprehensiveCodeAnalyzer.runFullAnalysis();
  
  console.log('🔍 Comprehensive Code Analyzer loaded. Available commands:');
  console.log('  - runFullCodeAnalysis()');
}
