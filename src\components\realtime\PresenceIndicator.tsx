/**
 * Presence Indicator Component
 * Shows online users and their status
 */

import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { usePresence } from '@/hooks/useRealtime';
import { UserPresence } from '@/lib/realtime/realtime-service';
import { cn } from '@/lib/utils';

interface PresenceIndicatorProps {
  className?: string;
  maxVisible?: number;
  showStatus?: boolean;
  showCurrentPage?: boolean;
}

export function PresenceIndicator({ 
  className, 
  maxVisible = 5, 
  showStatus = true,
  showCurrentPage = false 
}: PresenceIndicatorProps) {
  const { onlineUsers, currentUser } = usePresence();

  const getStatusColor = (status: UserPresence['status']) => {
    switch (status) {
      case 'online': return 'bg-green-500';
      case 'away': return 'bg-yellow-500';
      case 'busy': return 'bg-red-500';
      case 'offline': return 'bg-gray-400';
      default: return 'bg-gray-400';
    }
  };

  const getStatusText = (status: UserPresence['status']) => {
    switch (status) {
      case 'online': return 'Online';
      case 'away': return 'Away';
      case 'busy': return 'Busy';
      case 'offline': return 'Offline';
      default: return 'Unknown';
    }
  };

  const visibleUsers = onlineUsers.slice(0, maxVisible);
  const hiddenCount = Math.max(0, onlineUsers.length - maxVisible);

  return (
    <TooltipProvider>
      <div className={cn("flex items-center space-x-2", className)}>
        {/* Current user */}
        {currentUser && (
          <Tooltip>
            <TooltipTrigger>
              <div className="relative">
                <Avatar className="h-8 w-8 border-2 border-primary">
                  <AvatarImage src={currentUser.avatar} />
                  <AvatarFallback>
                    {currentUser.userName.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                {showStatus && (
                  <div className={cn(
                    "absolute -bottom-1 -right-1 h-3 w-3 rounded-full border-2 border-white",
                    getStatusColor(currentUser.status)
                  )} />
                )}
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <div className="text-sm">
                <div className="font-medium">{currentUser.userName} (You)</div>
                <div className="text-muted-foreground">
                  {getStatusText(currentUser.status)}
                </div>
                {showCurrentPage && currentUser.currentPage && (
                  <div className="text-xs text-muted-foreground">
                    {currentUser.currentPage}
                  </div>
                )}
              </div>
            </TooltipContent>
          </Tooltip>
        )}

        {/* Separator */}
        {currentUser && onlineUsers.length > 0 && (
          <div className="h-6 w-px bg-border" />
        )}

        {/* Other online users */}
        {visibleUsers.map((user) => (
          <Tooltip key={user.userId}>
            <TooltipTrigger>
              <div className="relative">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user.avatar} />
                  <AvatarFallback>
                    {user.userName.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                {showStatus && (
                  <div className={cn(
                    "absolute -bottom-1 -right-1 h-3 w-3 rounded-full border-2 border-white",
                    getStatusColor(user.status)
                  )} />
                )}
                {user.isTyping && (
                  <div className="absolute -top-1 -right-1">
                    <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse" />
                  </div>
                )}
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <div className="text-sm">
                <div className="font-medium">{user.userName}</div>
                <div className="text-muted-foreground">
                  {getStatusText(user.status)}
                </div>
                {user.isTyping && (
                  <div className="text-xs text-blue-500">Typing...</div>
                )}
                {showCurrentPage && user.currentPage && (
                  <div className="text-xs text-muted-foreground">
                    {user.currentPage}
                  </div>
                )}
                <div className="text-xs text-muted-foreground">
                  Last seen: {user.lastSeen.toLocaleTimeString()}
                </div>
              </div>
            </TooltipContent>
          </Tooltip>
        ))}

        {/* Hidden users count */}
        {hiddenCount > 0 && (
          <Tooltip>
            <TooltipTrigger>
              <Badge variant="secondary" className="h-8 w-8 rounded-full p-0 flex items-center justify-center">
                +{hiddenCount}
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <div className="text-sm">
                {hiddenCount} more user{hiddenCount > 1 ? 's' : ''} online
              </div>
            </TooltipContent>
          </Tooltip>
        )}

        {/* Online count */}
        <div className="text-sm text-muted-foreground">
          {onlineUsers.length + (currentUser ? 1 : 0)} online
        </div>
      </div>
    </TooltipProvider>
  );
}

/**
 * Simple presence dot for minimal UI
 */
export function PresenceDot({ userId, className }: { userId: string; className?: string }) {
  const { onlineUsers } = usePresence();
  const user = onlineUsers.find(u => u.userId === userId);
  
  if (!user) return null;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger>
          <div className={cn(
            "h-2 w-2 rounded-full",
            getStatusColor(user.status),
            className
          )} />
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-sm">
            <div className="font-medium">{user.userName}</div>
            <div className="text-muted-foreground">
              {getStatusText(user.status)}
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

function getStatusColor(status: UserPresence['status']) {
  switch (status) {
    case 'online': return 'bg-green-500';
    case 'away': return 'bg-yellow-500';
    case 'busy': return 'bg-red-500';
    case 'offline': return 'bg-gray-400';
    default: return 'bg-gray-400';
  }
}

function getStatusText(status: UserPresence['status']) {
  switch (status) {
    case 'online': return 'Online';
    case 'away': return 'Away';
    case 'busy': return 'Busy';
    case 'offline': return 'Offline';
    default: return 'Unknown';
  }
}

/**
 * Typing indicator component
 */
export function TypingIndicator({ sessionId, className }: { sessionId: string; className?: string }) {
  const { typingUsers } = useTypingIndicator(sessionId);

  if (typingUsers.length === 0) return null;

  const getTypingText = () => {
    if (typingUsers.length === 1) {
      return `${typingUsers[0].userName} is typing...`;
    } else if (typingUsers.length === 2) {
      return `${typingUsers[0].userName} and ${typingUsers[1].userName} are typing...`;
    } else {
      return `${typingUsers[0].userName} and ${typingUsers.length - 1} others are typing...`;
    }
  };

  return (
    <div className={cn("flex items-center space-x-2 text-sm text-muted-foreground", className)}>
      <div className="flex space-x-1">
        <div className="h-1 w-1 bg-current rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
        <div className="h-1 w-1 bg-current rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
        <div className="h-1 w-1 bg-current rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
      </div>
      <span>{getTypingText()}</span>
    </div>
  );
}

// Re-export the hook for convenience
export { useTypingIndicator } from '@/hooks/useRealtime';
