/**
 * Comprehensive System Checker
 * Step-by-step analysis of all system components
 */

import { supabase } from '@/integrations/supabase/client';

export interface SystemCheckResult {
  category: string;
  component: string;
  status: 'working' | 'error' | 'missing' | 'incomplete';
  issues: string[];
  fixes: string[];
  priority: 'critical' | 'high' | 'medium' | 'low';
}

export class ComprehensiveSystemChecker {
  private static results: SystemCheckResult[] = [];

  /**
   * Run complete system check
   */
  static async runCompleteCheck(): Promise<SystemCheckResult[]> {
    this.results = [];
    console.log('🔍 Running comprehensive system check...');

    await this.checkDocumentModules();
    await this.checkAIFeatures();
    await this.checkStaffFeatures();
    await this.checkTaskAssignment();
    await this.checkProjectCreation();
    await this.checkAgentFunctionalities();
    await this.checkDatabaseTables();
    await this.checkAPIEndpoints();

    console.log(`✅ System check complete. Found ${this.results.length} items.`);
    return this.results;
  }

  /**
   * Check document modules and folders
   */
  private static async checkDocumentModules(): Promise<void> {
    console.log('📁 Checking document modules...');

    // Check document archive table
    try {
      const { data, error } = await supabase
        .from('document_archive')
        .select('*')
        .limit(1);

      if (error && error.message.includes('does not exist')) {
        this.addResult('Documents', 'document_archive table', 'missing', 
          ['Table does not exist'], 
          ['Create document_archive table with proper schema'], 
          'critical'
        );
      } else if (error) {
        this.addResult('Documents', 'document_archive table', 'error', 
          [error.message], 
          ['Check RLS policies and permissions'], 
          'high'
        );
      } else {
        this.addResult('Documents', 'document_archive table', 'working', 
          [], [], 'low'
        );
      }
    } catch (err: any) {
      this.addResult('Documents', 'document_archive table', 'error', 
        [err.message], 
        ['Fix database connection'], 
        'critical'
      );
    }

    // Check document management components
    const documentComponents = [
      'ModernDocumentCard',
      'DocumentManagement', 
      'CustomFolderManagement',
      'AIDocumentAnalyzer'
    ];

    for (const component of documentComponents) {
      const element = document.querySelector(`[data-component="${component}"]`);
      if (!element) {
        this.addResult('Documents', component, 'incomplete', 
          ['Component not found in DOM'], 
          ['Ensure component is properly rendered'], 
          'medium'
        );
      } else {
        this.addResult('Documents', component, 'working', 
          [], [], 'low'
        );
      }
    }
  }

  /**
   * Check AI features
   */
  private static async checkAIFeatures(): Promise<void> {
    console.log('🤖 Checking AI features...');

    // Check AI Document Analyzer
    try {
      const aiAnalyzer = document.querySelector('[data-component="AIDocumentAnalyzer"]');
      if (aiAnalyzer) {
        this.addResult('AI', 'Document Analyzer', 'working', 
          [], [], 'low'
        );
      } else {
        this.addResult('AI', 'Document Analyzer', 'incomplete', 
          ['Component not rendered'], 
          ['Check AI page rendering'], 
          'medium'
        );
      }
    } catch (err: any) {
      this.addResult('AI', 'Document Analyzer', 'error', 
        [err.message], 
        ['Fix component rendering'], 
        'high'
      );
    }

    // Check AI Edge Functions
    try {
      const { data, error } = await supabase.functions.invoke('ai-assistant', {
        body: { message: 'test', context: { task: 'health_check' } }
      });

      if (error) {
        this.addResult('AI', 'Edge Functions', 'error', 
          [`AI service error: ${error.message}`], 
          ['Configure OpenAI API key', 'Deploy edge functions'], 
          'critical'
        );
      } else {
        this.addResult('AI', 'Edge Functions', 'working', 
          [], [], 'low'
        );
      }
    } catch (err: any) {
      this.addResult('AI', 'Edge Functions', 'missing', 
        ['Edge functions not deployed'], 
        ['Deploy AI edge functions to Supabase'], 
        'critical'
      );
    }

    // Check Project Manager Assistant
    const projectAgent = document.querySelector('[data-component="ProjectManagerAssistant"]');
    if (projectAgent) {
      this.addResult('AI', 'Project Manager Assistant', 'working', 
        [], [], 'low'
      );
    } else {
      this.addResult('AI', 'Project Manager Assistant', 'incomplete', 
        ['Component not found'], 
        ['Ensure component is properly loaded'], 
        'medium'
      );
    }
  }

  /**
   * Check staff features
   */
  private static async checkStaffFeatures(): Promise<void> {
    console.log('👥 Checking staff features...');

    // Check profiles table
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, email, role, department_id')
        .limit(1);

      if (error) {
        this.addResult('Staff', 'User Profiles', 'error', 
          [error.message], 
          ['Check profiles table and RLS policies'], 
          'critical'
        );
      } else {
        this.addResult('Staff', 'User Profiles', 'working', 
          [], [], 'low'
        );
      }
    } catch (err: any) {
      this.addResult('Staff', 'User Profiles', 'error', 
        [err.message], 
        ['Fix database connection'], 
        'critical'
      );
    }

    // Check departments table
    try {
      const { data, error } = await supabase
        .from('departments')
        .select('*')
        .limit(1);

      if (error && error.message.includes('does not exist')) {
        this.addResult('Staff', 'Departments', 'missing', 
          ['Departments table does not exist'], 
          ['Create departments table'], 
          'high'
        );
      } else if (error) {
        this.addResult('Staff', 'Departments', 'error', 
          [error.message], 
          ['Check table permissions'], 
          'medium'
        );
      } else {
        this.addResult('Staff', 'Departments', 'working', 
          [], [], 'low'
        );
      }
    } catch (err: any) {
      this.addResult('Staff', 'Departments', 'error', 
        [err.message], 
        ['Fix database connection'], 
        'high'
      );
    }

    // Check staff management components
    const staffComponents = [
      'TeamOverview',
      'LeaveManagement', 
      'TeamTimeManagement',
      'ExpenseManagement'
    ];

    for (const component of staffComponents) {
      // These components exist in the codebase but may not be rendered
      this.addResult('Staff', component, 'incomplete', 
        ['Component exists but may not be fully functional'], 
        ['Test component functionality'], 
        'medium'
      );
    }
  }

  /**
   * Check task assignment functionality
   */
  private static async checkTaskAssignment(): Promise<void> {
    console.log('📋 Checking task assignment...');

    // Check tasks table
    try {
      const { data, error } = await supabase
        .from('tasks')
        .select('id, title, status, assigned_to_id, created_by')
        .limit(1);

      if (error) {
        this.addResult('Tasks', 'Tasks Table', 'error', 
          [error.message], 
          ['Check tasks table and permissions'], 
          'critical'
        );
      } else {
        this.addResult('Tasks', 'Tasks Table', 'working', 
          [], [], 'low'
        );
      }
    } catch (err: any) {
      this.addResult('Tasks', 'Tasks Table', 'error', 
        [err.message], 
        ['Fix database connection'], 
        'critical'
      );
    }

    // Check task assignments table
    try {
      const { data, error } = await supabase
        .from('task_assignments')
        .select('*')
        .limit(1);

      if (error && error.message.includes('does not exist')) {
        this.addResult('Tasks', 'Task Assignments', 'missing', 
          ['task_assignments table does not exist'], 
          ['Create task_assignments table'], 
          'high'
        );
      } else if (error) {
        this.addResult('Tasks', 'Task Assignments', 'error', 
          [error.message], 
          ['Check table permissions'], 
          'medium'
        );
      } else {
        this.addResult('Tasks', 'Task Assignments', 'working', 
          [], [], 'low'
        );
      }
    } catch (err: any) {
      this.addResult('Tasks', 'Task Assignments', 'error', 
        [err.message], 
        ['Fix database connection'], 
        'high'
      );
    }

    // Test task creation functionality
    try {
      // This is a dry run test - we won't actually create a task
      const testTaskData = {
        title: 'Test Task',
        description: 'Test task creation',
        priority: 'medium',
        status: 'pending'
      };

      // Check if TaskProjectFixUtility is available
      if (typeof (window as any).TaskProjectFixUtility !== 'undefined') {
        this.addResult('Tasks', 'Task Creation Service', 'working', 
          [], [], 'low'
        );
      } else {
        this.addResult('Tasks', 'Task Creation Service', 'incomplete', 
          ['TaskProjectFixUtility not available'], 
          ['Load task creation utilities'], 
          'medium'
        );
      }
    } catch (err: any) {
      this.addResult('Tasks', 'Task Creation Service', 'error', 
        [err.message], 
        ['Fix task creation functionality'], 
        'high'
      );
    }
  }

  /**
   * Check project creation functionality
   */
  private static async checkProjectCreation(): Promise<void> {
    console.log('🏗️ Checking project creation...');

    // Check projects table
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('id, name, status, manager_id, department_id')
        .limit(1);

      if (error) {
        this.addResult('Projects', 'Projects Table', 'error', 
          [error.message], 
          ['Check projects table and permissions'], 
          'critical'
        );
      } else {
        this.addResult('Projects', 'Projects Table', 'working', 
          [], [], 'low'
        );
      }
    } catch (err: any) {
      this.addResult('Projects', 'Projects Table', 'error', 
        [err.message], 
        ['Fix database connection'], 
        'critical'
      );
    }

    // Check project assignments table
    try {
      const { data, error } = await supabase
        .from('project_assignments')
        .select('*')
        .limit(1);

      if (error && error.message.includes('does not exist')) {
        this.addResult('Projects', 'Project Assignments', 'missing', 
          ['project_assignments table does not exist'], 
          ['Create project_assignments table'], 
          'high'
        );
      } else if (error) {
        this.addResult('Projects', 'Project Assignments', 'error', 
          [error.message], 
          ['Check table permissions'], 
          'medium'
        );
      } else {
        this.addResult('Projects', 'Project Assignments', 'working', 
          [], [], 'low'
        );
      }
    } catch (err: any) {
      this.addResult('Projects', 'Project Assignments', 'error', 
        [err.message], 
        ['Fix database connection'], 
        'high'
      );
    }
  }

  /**
   * Check agent functionalities
   */
  private static async checkAgentFunctionalities(): Promise<void> {
    console.log('🤖 Checking agent functionalities...');

    // Check if AI agents are working
    const agentComponents = [
      'ProjectManagerAssistant',
      'AIAgent',
      'ModernAIInterface'
    ];

    for (const component of agentComponents) {
      const element = document.querySelector(`[data-component="${component}"]`);
      if (element) {
        this.addResult('Agents', component, 'working', 
          [], [], 'low'
        );
      } else {
        this.addResult('Agents', component, 'incomplete', 
          ['Component not found in DOM'], 
          ['Ensure component is properly rendered'], 
          'medium'
        );
      }
    }

    // Check agent services
    const agentServices = [
      'AdvancedAIService',
      'EnhancedAIService',
      'TaskAssignmentService'
    ];

    for (const service of agentServices) {
      if (typeof (window as any)[service] !== 'undefined') {
        this.addResult('Agents', service, 'working', 
          [], [], 'low'
        );
      } else {
        this.addResult('Agents', service, 'incomplete', 
          ['Service not available globally'], 
          ['Load service properly'], 
          'medium'
        );
      }
    }
  }

  /**
   * Check database tables
   */
  private static async checkDatabaseTables(): Promise<void> {
    console.log('🗄️ Checking database tables...');

    const requiredTables = [
      'profiles',
      'departments',
      'projects', 
      'tasks',
      'invoices',
      'memos',
      'time_logs',
      'project_assignments',
      'task_assignments',
      'document_archive',
      'notifications'
    ];

    for (const table of requiredTables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);

        if (error && error.message.includes('does not exist')) {
          this.addResult('Database', table, 'missing', 
            [`Table ${table} does not exist`], 
            [`Create ${table} table with proper schema`], 
            'high'
          );
        } else if (error) {
          this.addResult('Database', table, 'error', 
            [error.message], 
            ['Check table permissions and RLS policies'], 
            'medium'
          );
        } else {
          this.addResult('Database', table, 'working', 
            [], [], 'low'
          );
        }
      } catch (err: any) {
        this.addResult('Database', table, 'error', 
          [err.message], 
          ['Fix database connection'], 
          'critical'
        );
      }
    }
  }

  /**
   * Check API endpoints
   */
  private static async checkAPIEndpoints(): Promise<void> {
    console.log('🔗 Checking API endpoints...');

    // Check if ComprehensiveAPI is available
    if (typeof (window as any).ComprehensiveAPI !== 'undefined') {
      this.addResult('API', 'ComprehensiveAPI', 'working', 
        [], [], 'low'
      );
    } else {
      this.addResult('API', 'ComprehensiveAPI', 'incomplete', 
        ['API service not available'], 
        ['Load API service properly'], 
        'medium'
      );
    }

    // Check Supabase connection
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error) {
        this.addResult('API', 'Supabase Auth', 'error', 
          [error.message], 
          ['Check Supabase configuration'], 
          'critical'
        );
      } else {
        this.addResult('API', 'Supabase Auth', 'working', 
          [], [], 'low'
        );
      }
    } catch (err: any) {
      this.addResult('API', 'Supabase Connection', 'error', 
        [err.message], 
        ['Check Supabase URL and keys'], 
        'critical'
      );
    }
  }

  /**
   * Add result to the results array
   */
  private static addResult(
    category: string,
    component: string,
    status: 'working' | 'error' | 'missing' | 'incomplete',
    issues: string[],
    fixes: string[],
    priority: 'critical' | 'high' | 'medium' | 'low'
  ): void {
    this.results.push({
      category,
      component,
      status,
      issues,
      fixes,
      priority
    });

    const emoji = {
      working: '✅',
      error: '❌',
      missing: '❓',
      incomplete: '⚠️'
    }[status];

    console.log(`${emoji} ${category}/${component}: ${status}`);
    if (issues.length > 0) {
      console.log(`  Issues: ${issues.join(', ')}`);
    }
  }

  /**
   * Generate comprehensive report
   */
  static generateReport(results: SystemCheckResult[]): string {
    const working = results.filter(r => r.status === 'working');
    const errors = results.filter(r => r.status === 'error');
    const missing = results.filter(r => r.status === 'missing');
    const incomplete = results.filter(r => r.status === 'incomplete');

    let report = '🔍 COMPREHENSIVE SYSTEM CHECK REPORT\n';
    report += '=====================================\n\n';
    
    report += `📊 SUMMARY:\n`;
    report += `✅ Working: ${working.length}\n`;
    report += `❌ Errors: ${errors.length}\n`;
    report += `❓ Missing: ${missing.length}\n`;
    report += `⚠️ Incomplete: ${incomplete.length}\n\n`;

    if (errors.length > 0) {
      report += `❌ CRITICAL ERRORS:\n`;
      errors.forEach(result => {
        report += `  - ${result.category}/${result.component}: ${result.issues.join(', ')}\n`;
        result.fixes.forEach(fix => report += `    Fix: ${fix}\n`);
      });
      report += '\n';
    }

    if (missing.length > 0) {
      report += `❓ MISSING COMPONENTS:\n`;
      missing.forEach(result => {
        report += `  - ${result.category}/${result.component}: ${result.issues.join(', ')}\n`;
        result.fixes.forEach(fix => report += `    Fix: ${fix}\n`);
      });
      report += '\n';
    }

    if (incomplete.length > 0) {
      report += `⚠️ INCOMPLETE FEATURES:\n`;
      incomplete.forEach(result => {
        report += `  - ${result.category}/${result.component}: ${result.issues.join(', ')}\n`;
        result.fixes.forEach(fix => report += `    Fix: ${fix}\n`);
      });
      report += '\n';
    }

    return report;
  }
}

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).ComprehensiveSystemChecker = ComprehensiveSystemChecker;
  (window as any).runSystemCheck = () => ComprehensiveSystemChecker.runCompleteCheck();
  
  console.log('🔍 Comprehensive System Checker loaded. Available commands:');
  console.log('  - runSystemCheck()');
}
