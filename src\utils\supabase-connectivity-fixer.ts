/**
 * Comprehensive Supabase Connectivity Fixer
 * Handles all Supabase connection issues, network errors, and service unavailability
 */

import { createClient } from '@supabase/supabase-js';
import { showErrorToast, showWarningToast, showSuccessToast } from './comprehensive-toast-system';
import { safeWindow } from './safe-type-utils';

export interface SupabaseConnectionStatus {
  isConnected: boolean;
  lastChecked: Date;
  errorCount: number;
  lastError?: string;
  connectionQuality: 'excellent' | 'good' | 'poor' | 'offline';
  retryAttempts: number;
}

export interface SupabaseFixResult {
  success: boolean;
  message: string;
  fixes: string[];
  connectionStatus: SupabaseConnectionStatus;
}

export class SupabaseConnectivityFixer {
  private static instance: SupabaseConnectivityFixer;
  private connectionStatus: SupabaseConnectionStatus;
  private retryQueue: Array<() => Promise<any>> = [];
  private isRetrying = false;
  private maxRetries = 5;
  private retryDelay = 2000;
  private healthCheckInterval?: number;

  static getInstance(): SupabaseConnectivityFixer {
    if (!SupabaseConnectivityFixer.instance) {
      SupabaseConnectivityFixer.instance = new SupabaseConnectivityFixer();
    }
    return SupabaseConnectivityFixer.instance;
  }

  constructor() {
    this.connectionStatus = {
      isConnected: false,
      lastChecked: new Date(),
      errorCount: 0,
      connectionQuality: 'offline',
      retryAttempts: 0
    };
  }

  /**
   * Initialize comprehensive Supabase connectivity fixing
   */
  async initialize(): Promise<void> {
    console.log('🔧 Initializing Supabase connectivity fixer...');

    // Check initial connection
    await this.checkConnection();
    
    // Setup connection monitoring
    this.setupConnectionMonitoring();
    
    // Setup error handling
    this.setupErrorHandling();
    
    // Setup automatic retry system
    this.setupRetrySystem();
    
    // Setup health checks
    this.setupHealthChecks();

    console.log('✅ Supabase connectivity fixer initialized');
  }

  /**
   * Check Supabase connection status
   */
  async checkConnection(): Promise<SupabaseConnectionStatus> {
    console.log('🔍 Checking Supabase connection...');

    try {
      // Test basic connectivity
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/rest/v1/`, {
        method: 'HEAD',
        headers: {
          'apikey': import.meta.env.VITE_SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
        }
      });

      if (response.ok) {
        this.connectionStatus = {
          isConnected: true,
          lastChecked: new Date(),
          errorCount: 0,
          connectionQuality: 'excellent',
          retryAttempts: 0
        };
        
        console.log('✅ Supabase connection healthy');
        return this.connectionStatus;
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error: any) {
      console.error('❌ Supabase connection failed:', error);
      
      this.connectionStatus = {
        isConnected: false,
        lastChecked: new Date(),
        errorCount: this.connectionStatus.errorCount + 1,
        lastError: error.message,
        connectionQuality: 'offline',
        retryAttempts: this.connectionStatus.retryAttempts + 1
      };

      return this.connectionStatus;
    }
  }

  /**
   * Setup connection monitoring
   */
  private setupConnectionMonitoring(): void {
    // Monitor fetch requests to Supabase
    const originalFetch = window.fetch;
    
    window.fetch = async (input, init) => {
      const url = typeof input === 'string' ? input : input.url;
      
      // Check if this is a Supabase request
      if (url.includes('supabase.co')) {
        console.log('🔍 Monitoring Supabase request:', url);
        
        try {
          const response = await originalFetch.call(window, input, init);
          
          if (!response.ok) {
            await this.handleSupabaseError(response, url);
          } else {
            // Update connection status on successful request
            this.connectionStatus.isConnected = true;
            this.connectionStatus.errorCount = 0;
            this.connectionStatus.connectionQuality = 'excellent';
          }
          
          return response;
        } catch (error: any) {
          console.error('🔧 Supabase request failed:', error);
          await this.handleSupabaseError(error, url);
          throw error;
        }
      }
      
      return originalFetch.call(window, input, init);
    };
  }

  /**
   * Handle Supabase errors
   */
  private async handleSupabaseError(error: any, url: string): Promise<void> {
    const errorMessage = error.message || error.statusText || 'Unknown error';
    
    console.log('🔧 Handling Supabase error:', errorMessage, 'for URL:', url);

    // Update connection status
    this.connectionStatus.isConnected = false;
    this.connectionStatus.errorCount++;
    this.connectionStatus.lastError = errorMessage;
    this.connectionStatus.lastChecked = new Date();

    // Determine error type and response
    if (error.status === 503 || errorMessage.includes('Service Unavailable')) {
      this.connectionStatus.connectionQuality = 'offline';
      
      showWarningToast({
        title: 'Database Service Unavailable',
        description: 'The database service is temporarily unavailable. Retrying automatically...',
        duration: 5000
      });
      
      // Add to retry queue
      this.addToRetryQueue(async () => {
        await this.checkConnection();
      });
      
    } else if (errorMessage.includes('ERR_TUNNEL_CONNECTION_FAILED') || 
               errorMessage.includes('Failed to fetch')) {
      this.connectionStatus.connectionQuality = 'poor';
      
      showWarningToast({
        title: 'Connection Issue',
        description: 'Network connection problem detected. Attempting to reconnect...',
        duration: 4000
      });
      
      // Try alternative connection methods
      await this.tryAlternativeConnection();
      
    } else if (error.status === 429) {
      this.connectionStatus.connectionQuality = 'poor';
      
      showWarningToast({
        title: 'Rate Limited',
        description: 'Too many requests. Waiting before retrying...',
        duration: 3000
      });
      
      // Wait longer for rate limit
      await new Promise(resolve => setTimeout(resolve, 5000));
      
    } else {
      this.connectionStatus.connectionQuality = 'poor';
      
      showErrorToast({
        title: 'Database Error',
        description: `Database operation failed: ${errorMessage}`,
        duration: 4000
      });
    }
  }

  /**
   * Try alternative connection methods
   */
  private async tryAlternativeConnection(): Promise<void> {
    console.log('🔧 Trying alternative connection methods...');

    // Method 1: Try with different headers
    try {
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/rest/v1/`, {
        method: 'GET',
        headers: {
          'apikey': import.meta.env.VITE_SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        }
      });

      if (response.ok) {
        console.log('✅ Alternative connection method successful');
        this.connectionStatus.isConnected = true;
        this.connectionStatus.connectionQuality = 'good';
        return;
      }
    } catch (error) {
      console.warn('⚠️ Alternative connection method 1 failed');
    }

    // Method 2: Try with timeout
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/rest/v1/`, {
        method: 'HEAD',
        headers: {
          'apikey': import.meta.env.VITE_SUPABASE_ANON_KEY
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        console.log('✅ Alternative connection method 2 successful');
        this.connectionStatus.isConnected = true;
        this.connectionStatus.connectionQuality = 'good';
        return;
      }
    } catch (error) {
      console.warn('⚠️ Alternative connection method 2 failed');
    }

    console.warn('❌ All alternative connection methods failed');
  }

  /**
   * Setup error handling
   */
  private setupErrorHandling(): void {
    // Handle unhandled promise rejections from Supabase
    safeWindow.addEventListener('unhandledrejection', (event) => {
      const error = event.reason;
      
      if (this.isSupabaseError(error)) {
        console.log('🔧 Handling unhandled Supabase rejection:', error);
        event.preventDefault();
        this.handleSupabaseError(error, 'unhandled-rejection');
      }
    });

    // Handle global errors from Supabase
    safeWindow.addEventListener('error', (event) => {
      const error = event.error;
      
      if (this.isSupabaseError(error)) {
        console.log('🔧 Handling global Supabase error:', error);
        event.preventDefault();
        this.handleSupabaseError(error, 'global-error');
      }
    });
  }

  /**
   * Check if error is Supabase-related
   */
  private isSupabaseError(error: any): boolean {
    if (!error) return false;
    
    const message = error.message || error.toString() || '';
    const stack = error.stack || '';
    
    return message.includes('supabase') ||
           message.includes('dvflgnqwbsjityrowatf') ||
           stack.includes('supabase') ||
           message.includes('Failed to fetch') ||
           message.includes('ERR_TUNNEL_CONNECTION_FAILED') ||
           message.includes('Service Unavailable');
  }

  /**
   * Setup retry system
   */
  private setupRetrySystem(): void {
    // Process retry queue every 5 seconds
    setInterval(() => {
      if (!this.isRetrying && this.retryQueue.length > 0) {
        this.processRetryQueue();
      }
    }, 5000);
  }

  /**
   * Add operation to retry queue
   */
  private addToRetryQueue(operation: () => Promise<any>): void {
    this.retryQueue.push(operation);
    console.log(`📋 Added operation to retry queue. Queue length: ${this.retryQueue.length}`);
  }

  /**
   * Process retry queue
   */
  private async processRetryQueue(): Promise<void> {
    if (this.isRetrying || this.retryQueue.length === 0) return;
    
    this.isRetrying = true;
    console.log('🔄 Processing retry queue...');

    while (this.retryQueue.length > 0) {
      const operation = this.retryQueue.shift();
      
      if (operation) {
        try {
          await operation();
          console.log('✅ Retry operation successful');
        } catch (error) {
          console.warn('⚠️ Retry operation failed:', error);
        }
      }
      
      // Wait between retries
      await new Promise(resolve => setTimeout(resolve, this.retryDelay));
    }

    this.isRetrying = false;
    console.log('✅ Retry queue processed');
  }

  /**
   * Setup health checks
   */
  private setupHealthChecks(): void {
    // Check connection every 30 seconds
    this.healthCheckInterval = safeWindow.setInterval(async () => {
      if (!this.connectionStatus.isConnected) {
        console.log('🔍 Running health check...');
        await this.checkConnection();
        
        if (this.connectionStatus.isConnected) {
          showSuccessToast({
            title: 'Connection Restored',
            description: 'Database connection has been restored successfully.',
            duration: 3000
          });
        }
      }
    }, 30000) as any;
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): SupabaseConnectionStatus {
    return { ...this.connectionStatus };
  }

  /**
   * Force reconnection
   */
  async forceReconnect(): Promise<SupabaseFixResult> {
    console.log('🔧 Forcing Supabase reconnection...');
    
    const fixes: string[] = [];
    
    // Reset connection status
    this.connectionStatus.errorCount = 0;
    this.connectionStatus.retryAttempts = 0;
    fixes.push('Reset connection status');
    
    // Clear retry queue
    this.retryQueue = [];
    fixes.push('Cleared retry queue');
    
    // Check connection
    await this.checkConnection();
    fixes.push('Performed connection check');
    
    // Try alternative methods if still failing
    if (!this.connectionStatus.isConnected) {
      await this.tryAlternativeConnection();
      fixes.push('Tried alternative connection methods');
    }

    const result: SupabaseFixResult = {
      success: this.connectionStatus.isConnected,
      message: this.connectionStatus.isConnected 
        ? 'Supabase connection restored successfully'
        : 'Unable to restore Supabase connection',
      fixes,
      connectionStatus: this.connectionStatus
    };

    if (result.success) {
      showSuccessToast({
        title: 'Connection Fixed',
        description: 'Supabase connection has been restored.',
        duration: 4000
      });
    } else {
      showErrorToast({
        title: 'Connection Failed',
        description: 'Unable to restore Supabase connection. Please check your internet connection.',
        duration: 6000
      });
    }

    return result;
  }

  /**
   * Get connection diagnostics
   */
  getDiagnostics(): any {
    return {
      connectionStatus: this.connectionStatus,
      retryQueueLength: this.retryQueue.length,
      isRetrying: this.isRetrying,
      supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
      hasApiKey: !!import.meta.env.VITE_SUPABASE_ANON_KEY,
      environment: import.meta.env.MODE,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Cleanup
   */
  cleanup(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    this.retryQueue = [];
    this.isRetrying = false;
  }
}

// Export singleton instance
export const supabaseConnectivityFixer = SupabaseConnectivityFixer.getInstance();

// Export utility functions
export const initializeSupabaseConnectivityFixer = () => supabaseConnectivityFixer.initialize();
export const checkSupabaseConnection = () => supabaseConnectivityFixer.checkConnection();
export const forceSupabaseReconnect = () => supabaseConnectivityFixer.forceReconnect();
export const getSupabaseConnectionStatus = () => supabaseConnectivityFixer.getConnectionStatus();
export const getSupabaseDiagnostics = () => supabaseConnectivityFixer.getDiagnostics();

// Make available globally for debugging
if (typeof window !== 'undefined') {
  safeWindow.set('supabaseConnectivityFixer', supabaseConnectivityFixer);
  safeWindow.set('checkSupabaseConnection', checkSupabaseConnection);
  safeWindow.set('forceSupabaseReconnect', forceSupabaseReconnect);
  safeWindow.set('getSupabaseDiagnostics', getSupabaseDiagnostics);
  
  console.log('🔧 Supabase Connectivity Fixer loaded. Available commands:');
  console.log('  - checkSupabaseConnection()');
  console.log('  - forceSupabaseReconnect()');
  console.log('  - getSupabaseDiagnostics()');
}

// Auto-initialize when module loads
if (typeof window !== 'undefined') {
  setTimeout(() => {
    supabaseConnectivityFixer.initialize();
  }, 1000);
}
