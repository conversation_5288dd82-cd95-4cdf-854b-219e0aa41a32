/**
 * Task and Project Management Fix Utility
 * Comprehensive fixes for task creation, project updates, and task updates
 */

import { supabase } from '@/integrations/supabase/client';
import { DatabaseFixUtility } from './database-fix-utility';

export interface TaskData {
  title: string;
  description?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  assigned_to_id?: string;
  created_by?: string;
  project_id?: string;
  due_date?: string;
  estimated_hours?: number;
  actual_hours?: number;
}

export interface ProjectData {
  name: string;
  description?: string;
  client_name?: string;
  budget?: number;
  start_date?: string;
  end_date?: string;
  status: 'planning' | 'active' | 'on_hold' | 'completed' | 'cancelled';
  manager_id?: string;
  department_id?: string;
}

export class TaskProjectFixUtility {
  /**
   * Create task with proper error handling and fallbacks
   */
  static async createTask(taskData: TaskData): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // Get current user
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        return { success: false, error: 'User not authenticated' };
      }

      // Prepare task data with defaults
      const newTask = {
        ...taskData,
        created_by: user.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        progress_percentage: 0,
        status: taskData.status || 'pending'
      };

      // Validate required fields
      if (!newTask.title?.trim()) {
        return { success: false, error: 'Task title is required' };
      }

      // Try to create the task
      const { data, error } = await supabase
        .from('tasks')
        .insert(newTask)
        .select()
        .single();

      if (error) {
        console.error('Task creation error:', error);
        
        // Try fallback approach without foreign key relationships
        if (error.message.includes('foreign key') || error.message.includes('relationship')) {
          const basicTask = {
            title: newTask.title,
            description: newTask.description,
            priority: newTask.priority,
            status: newTask.status,
            created_by: newTask.created_by,
            created_at: newTask.created_at,
            updated_at: newTask.updated_at
          };

          const { data: fallbackData, error: fallbackError } = await supabase
            .from('tasks')
            .insert(basicTask)
            .select()
            .single();

          if (fallbackError) {
            return { success: false, error: fallbackError.message };
          }

          return { success: true, data: fallbackData };
        }

        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error: any) {
      console.error('Task creation failed:', error);
      return { success: false, error: error.message || 'Failed to create task' };
    }
  }

  /**
   * Update task with proper error handling
   */
  static async updateTask(
    taskId: string, 
    updates: Partial<TaskData>
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // Get current user
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        return { success: false, error: 'User not authenticated' };
      }

      // Prepare update data
      const updateData = {
        ...updates,
        updated_at: new Date().toISOString()
      };

      // Remove undefined values
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key];
        }
      });

      const { data, error } = await supabase
        .from('tasks')
        .update(updateData)
        .eq('id', taskId)
        .select()
        .single();

      if (error) {
        console.error('Task update error:', error);
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error: any) {
      console.error('Task update failed:', error);
      return { success: false, error: error.message || 'Failed to update task' };
    }
  }

  /**
   * Create project with proper error handling and fallbacks
   */
  static async createProject(projectData: ProjectData): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // Get current user
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        return { success: false, error: 'User not authenticated' };
      }

      // Prepare project data with defaults
      const newProject = {
        ...projectData,
        created_by: user.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        status: projectData.status || 'planning'
      };

      // Validate required fields
      if (!newProject.name?.trim()) {
        return { success: false, error: 'Project name is required' };
      }

      // Try to create the project
      const { data, error } = await supabase
        .from('projects')
        .insert(newProject)
        .select()
        .single();

      if (error) {
        console.error('Project creation error:', error);
        
        // Try fallback approach without foreign key relationships
        if (error.message.includes('foreign key') || error.message.includes('relationship')) {
          const basicProject = {
            name: newProject.name,
            description: newProject.description,
            client_name: newProject.client_name,
            budget: newProject.budget,
            start_date: newProject.start_date,
            end_date: newProject.end_date,
            status: newProject.status,
            created_by: newProject.created_by,
            created_at: newProject.created_at,
            updated_at: newProject.updated_at
          };

          const { data: fallbackData, error: fallbackError } = await supabase
            .from('projects')
            .insert(basicProject)
            .select()
            .single();

          if (fallbackError) {
            return { success: false, error: fallbackError.message };
          }

          return { success: true, data: fallbackData };
        }

        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error: any) {
      console.error('Project creation failed:', error);
      return { success: false, error: error.message || 'Failed to create project' };
    }
  }

  /**
   * Update project with proper error handling
   */
  static async updateProject(
    projectId: string, 
    updates: Partial<ProjectData>
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // Get current user
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        return { success: false, error: 'User not authenticated' };
      }

      // Prepare update data
      const updateData = {
        ...updates,
        updated_at: new Date().toISOString()
      };

      // Remove undefined values
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key];
        }
      });

      const { data, error } = await supabase
        .from('projects')
        .update(updateData)
        .eq('id', projectId)
        .select()
        .single();

      if (error) {
        console.error('Project update error:', error);
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error: any) {
      console.error('Project update failed:', error);
      return { success: false, error: error.message || 'Failed to update project' };
    }
  }

  /**
   * Get tasks with safe query and fallbacks
   */
  static async getTasks(filters?: { 
    assigned_to?: string; 
    project_id?: string; 
    status?: string; 
    limit?: number 
  }): Promise<{ success: boolean; data?: any[]; error?: string }> {
    try {
      const result = await DatabaseFixUtility.getSafeQuery('tasks', '*', filters);
      return { success: true, data: result };
    } catch (error: any) {
      console.error('Failed to get tasks:', error);
      return { success: false, error: error.message || 'Failed to fetch tasks' };
    }
  }

  /**
   * Get projects with safe query and fallbacks
   */
  static async getProjects(filters?: { 
    manager_id?: string; 
    department_id?: string; 
    status?: string; 
    limit?: number 
  }): Promise<{ success: boolean; data?: any[]; error?: string }> {
    try {
      const result = await DatabaseFixUtility.getSafeQuery('projects', '*', filters);
      return { success: true, data: result };
    } catch (error: any) {
      console.error('Failed to get projects:', error);
      return { success: false, error: error.message || 'Failed to fetch projects' };
    }
  }

  /**
   * Bulk update task statuses
   */
  static async bulkUpdateTaskStatus(
    taskIds: string[], 
    status: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .update({ 
          status, 
          updated_at: new Date().toISOString() 
        })
        .in('id', taskIds)
        .select();

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error: any) {
      console.error('Bulk update failed:', error);
      return { success: false, error: error.message || 'Failed to update tasks' };
    }
  }

  /**
   * Test task and project functionality
   */
  static async runTests(): Promise<{ passed: number; failed: number; results: any[] }> {
    const results: any[] = [];
    let passed = 0;
    let failed = 0;

    console.log('🧪 Testing task and project functionality...');

    // Test task creation
    try {
      const taskResult = await this.createTask({
        title: 'Test Task',
        description: 'Test task creation',
        priority: 'medium',
        status: 'pending'
      });

      if (taskResult.success) {
        results.push({ test: 'Task Creation', status: 'passed', data: taskResult.data });
        passed++;

        // Test task update
        if (taskResult.data?.id) {
          const updateResult = await this.updateTask(taskResult.data.id, {
            status: 'in_progress',
            description: 'Updated description'
          });

          if (updateResult.success) {
            results.push({ test: 'Task Update', status: 'passed', data: updateResult.data });
            passed++;
          } else {
            results.push({ test: 'Task Update', status: 'failed', error: updateResult.error });
            failed++;
          }
        }
      } else {
        results.push({ test: 'Task Creation', status: 'failed', error: taskResult.error });
        failed++;
      }
    } catch (error: any) {
      results.push({ test: 'Task Creation', status: 'failed', error: error.message });
      failed++;
    }

    // Test project creation
    try {
      const projectResult = await this.createProject({
        name: 'Test Project',
        description: 'Test project creation',
        status: 'planning'
      });

      if (projectResult.success) {
        results.push({ test: 'Project Creation', status: 'passed', data: projectResult.data });
        passed++;

        // Test project update
        if (projectResult.data?.id) {
          const updateResult = await this.updateProject(projectResult.data.id, {
            status: 'active',
            description: 'Updated project description'
          });

          if (updateResult.success) {
            results.push({ test: 'Project Update', status: 'passed', data: updateResult.data });
            passed++;
          } else {
            results.push({ test: 'Project Update', status: 'failed', error: updateResult.error });
            failed++;
          }
        }
      } else {
        results.push({ test: 'Project Creation', status: 'failed', error: projectResult.error });
        failed++;
      }
    } catch (error: any) {
      results.push({ test: 'Project Creation', status: 'failed', error: error.message });
      failed++;
    }

    console.log(`✅ Tests complete: ${passed} passed, ${failed} failed`);
    return { passed, failed, results };
  }
}

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).TaskProjectFixUtility = TaskProjectFixUtility;
  (window as any).testTaskProjectFunctionality = () => TaskProjectFixUtility.runTests();
  
  console.log('🛠️ Task Project Fix Utility loaded. Available commands:');
  console.log('  - testTaskProjectFunctionality()');
}
