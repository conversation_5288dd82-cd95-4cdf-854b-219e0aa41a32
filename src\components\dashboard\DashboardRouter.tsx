import { useAuth } from "@/components/auth/AuthProvider";
import { useNavigate } from "react-router-dom";
import { useEffect } from "react";
import { EmergencyDashboard } from "./EmergencyDashboard";
import { DashboardSkeleton } from "./DashboardSkeleton";
import { AuthenticationFix } from "@/components/auth/AuthenticationFix";

export const DashboardRouter = () => {
  const { userProfile, loading, initialized, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  console.log('🔍 DashboardRouter Debug:', {
    userProfile: userProfile?.role,
    loading,
    initialized,
    isAuthenticated,
    currentPath: window.location.pathname
  });

  useEffect(() => {
    // Only redirect if we have complete auth information
    if (initialized && !loading) {
      if (!isAuthenticated || !userProfile) {
        console.log('❌ DashboardRouter: Not authenticated, redirecting to auth');
        navigate('/auth', { replace: true });
        return;
      }

      // Redirect to role-specific dashboard
      const roleRoutes = {
        admin: '/dashboard/admin',
        manager: '/dashboard/manager',
        staff: '/dashboard/staff',
        accountant: '/dashboard/accountant',
        'staff-admin': '/dashboard/staff-admin'
      };

      const targetRoute = roleRoutes[userProfile.role];
      
      if (targetRoute && window.location.pathname === '/dashboard') {
        console.log(`✅ DashboardRouter: Redirecting ${userProfile.role} to ${targetRoute}`);
        navigate(targetRoute, { replace: true });
        return;
      }
    }
  }, [initialized, loading, isAuthenticated, userProfile, navigate]);

  // Show loading while authentication is being determined
  if (loading || !initialized) {
    console.log('⏳ DashboardRouter: Showing loading state');
    return <DashboardSkeleton />;
  }

  // If still loading or not initialized, show loading state
  if (loading || !initialized) {
    console.log('⏳ DashboardRouter: Still loading authentication state');
    return <DashboardSkeleton />;
  }

  // If not authenticated after loading is complete, show authentication fix component
  if (!isAuthenticated) {
    console.log('🚨 DashboardRouter: Not authenticated, showing authentication fix');
    return <AuthenticationFix />;
  }

  // If authenticated but no profile yet, show loading (profile might still be loading)
  if (!userProfile) {
    console.log('⏳ DashboardRouter: Authenticated but profile loading...');
    return <DashboardSkeleton />;
  }

  // If we reach here, user is authenticated but hasn't been redirected yet
  // This should be rare, but show emergency dashboard as fallback
  console.log('🔄 DashboardRouter: Authenticated but not redirected, showing emergency dashboard');
  return (
    <div className="space-y-6 p-4">
      <div className="bg-blue-100 dark:bg-blue-900 p-4 rounded-lg border">
        <h2 className="font-bold text-lg mb-2">🔄 Redirecting...</h2>
        <p>Setting up your {userProfile.role} dashboard...</p>
      </div>
      <EmergencyDashboard />
    </div>
  );
};
