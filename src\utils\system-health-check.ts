/**
 * System Health Check Utility
 * Tests all critical functionality to ensure everything is working
 */

import { supabase } from '@/integrations/supabase/client';

export interface HealthCheckResult {
  component: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: any;
}

export class SystemHealthChecker {
  private results: HealthCheckResult[] = [];

  /**
   * Run comprehensive health check
   */
  async runHealthCheck(): Promise<HealthCheckResult[]> {
    this.results = [];
    
    console.log('🔍 Starting system health check...');
    
    await Promise.all([
      this.checkSupabaseConnection(),
      this.checkTimeLogsTable(),
      this.checkNotificationsTable(),
      this.checkUserProfile(),
      this.checkRLSPolicies(),
    ]);
    
    this.logResults();
    return this.results;
  }

  /**
   * Test Supabase connection
   */
  private async checkSupabaseConnection(): Promise<void> {
    try {
      const { data, error } = await supabase.auth.getSession();
      
      if (error) {
        this.addResult('Supabase Connection', 'fail', `Auth error: ${error.message}`);
        return;
      }
      
      this.addResult('Supabase Connection', 'pass', 'Successfully connected to Supabase');
    } catch (error: any) {
      this.addResult('Supabase Connection', 'fail', `Connection failed: ${error.message}`);
    }
  }

  /**
   * Test time_logs table functionality
   */
  private async checkTimeLogsTable(): Promise<void> {
    try {
      // Test basic query (should not cause 406 error)
      const { data, error } = await supabase
        .from('time_logs')
        .select('id')
        .limit(1);

      if (error) {
        if (error.status === 406) {
          this.addResult('Time Logs Table', 'fail', '406 error still occurring - query needs fixing');
        } else {
          this.addResult('Time Logs Table', 'warning', `Query error: ${error.message}`);
        }
        return;
      }

      this.addResult('Time Logs Table', 'pass', 'Time logs table accessible without 406 errors');
    } catch (error: any) {
      this.addResult('Time Logs Table', 'fail', `Unexpected error: ${error.message}`);
    }
  }

  /**
   * Test notifications table schema
   */
  private async checkNotificationsTable(): Promise<void> {
    try {
      // Test if we can query with the 'data' column
      const { data, error } = await supabase
        .from('notifications')
        .select('id, data, read')
        .limit(1);

      if (error) {
        if (error.message.includes('data')) {
          this.addResult('Notifications Table', 'fail', 'Missing data column in notifications table');
        } else {
          this.addResult('Notifications Table', 'warning', `Schema issue: ${error.message}`);
        }
        return;
      }

      this.addResult('Notifications Table', 'pass', 'Notifications table has correct schema with data column');
    } catch (error: any) {
      this.addResult('Notifications Table', 'fail', `Schema check failed: ${error.message}`);
    }
  }

  /**
   * Test user profile access
   */
  private async checkUserProfile(): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        this.addResult('User Profile', 'warning', 'No authenticated user found');
        return;
      }

      const { data, error } = await supabase
        .from('profiles')
        .select('id, role, account_type')
        .eq('id', user.id)
        .single();

      if (error) {
        this.addResult('User Profile', 'fail', `Profile query failed: ${error.message}`);
        return;
      }

      this.addResult('User Profile', 'pass', `User profile accessible: ${data.role || data.account_type}`);
    } catch (error: any) {
      this.addResult('User Profile', 'fail', `Profile check failed: ${error.message}`);
    }
  }

  /**
   * Test RLS policies
   */
  private async checkRLSPolicies(): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        this.addResult('RLS Policies', 'warning', 'Cannot test RLS - no authenticated user');
        return;
      }

      // Test time_logs RLS
      const { data, error } = await supabase
        .from('time_logs')
        .select('id')
        .eq('user_id', user.id)
        .limit(1);

      if (error && error.message.includes('RLS')) {
        this.addResult('RLS Policies', 'fail', 'RLS policy blocking legitimate access');
        return;
      }

      this.addResult('RLS Policies', 'pass', 'RLS policies working correctly');
    } catch (error: any) {
      this.addResult('RLS Policies', 'fail', `RLS check failed: ${error.message}`);
    }
  }

  /**
   * Add result to the collection
   */
  private addResult(component: string, status: 'pass' | 'fail' | 'warning', message: string, details?: any): void {
    this.results.push({ component, status, message, details });
  }

  /**
   * Log results to console
   */
  private logResults(): void {
    console.log('\n🏥 System Health Check Results:');
    console.log('================================');
    
    this.results.forEach(result => {
      const icon = result.status === 'pass' ? '✅' : result.status === 'fail' ? '❌' : '⚠️';
      console.log(`${icon} ${result.component}: ${result.message}`);
    });

    const passCount = this.results.filter(r => r.status === 'pass').length;
    const failCount = this.results.filter(r => r.status === 'fail').length;
    const warnCount = this.results.filter(r => r.status === 'warning').length;

    console.log('\n📊 Summary:');
    console.log(`✅ Passed: ${passCount}`);
    console.log(`❌ Failed: ${failCount}`);
    console.log(`⚠️ Warnings: ${warnCount}`);
    
    if (failCount === 0) {
      console.log('\n🎉 All critical systems are working!');
    } else {
      console.log('\n🚨 Some systems need attention!');
    }
  }

  /**
   * Get overall system status
   */
  getOverallStatus(): 'healthy' | 'degraded' | 'critical' {
    const failCount = this.results.filter(r => r.status === 'fail').length;
    const warnCount = this.results.filter(r => r.status === 'warning').length;
    
    if (failCount > 0) return 'critical';
    if (warnCount > 0) return 'degraded';
    return 'healthy';
  }
}

// Export singleton instance
export const systemHealthChecker = new SystemHealthChecker();

// Make health checker available globally for manual testing
if (typeof window !== 'undefined') {
  (window as any).runHealthCheck = () => systemHealthChecker.runHealthCheck();
  console.log('🔍 Health checker available: runHealthCheck()');
}

// Note: Auto health check removed to prevent unauthenticated API calls on startup
// Use runHealthCheck() in browser console or call it manually when needed
