import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Progress } from '@/components/ui/progress';
import { 
  Brain, 
  Zap, 
  Database, 
  Users, 
  ClipboardList,
  FileText,
  Calendar,
  Settings,
  Activity,
  MonitorDot
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/auth/AuthProvider';

interface AICommand {
  id: string;
  command: string;
  timestamp: Date;
  status: 'processing' | 'completed' | 'error';
  result?: any;
  modules?: string[];
}

interface SystemModule {
  name: string;
  status: 'active' | 'inactive' | 'processing';
  actions: string[];
  icon: React.ReactNode;
}

export const AISystemController = () => {
  const [commands, setCommands] = useState<AICommand[]>([]);
  const [input, setInput] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [systemModules, setSystemModules] = useState<SystemModule[]>([]);
  const { userProfile } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    initializeSystemModules();
  }, []);

  const initializeSystemModules = () => {
    const modules: SystemModule[] = [
      {
        name: 'User Management',
        status: 'active',
        actions: ['create_user', 'modify_permissions', 'assign_roles', 'deactivate_user'],
        icon: <Users className="h-4 w-4" />
      },
      {
        name: 'Project Management',
        status: 'active',
        actions: ['create_project', 'assign_team', 'update_status', 'generate_reports'],
        icon: <ClipboardList className="h-4 w-4" />
      },
      {
        name: 'Task Management',
        status: 'active',
        actions: ['create_task', 'assign_task', 'update_priority', 'track_progress'],
        icon: <Calendar className="h-4 w-4" />
      },
      {
        name: 'Document System',
        status: 'active',
        actions: ['analyze_document', 'generate_report', 'extract_data', 'categorize'],
        icon: <FileText className="h-4 w-4" />
      },
      {
        name: 'Database Operations',
        status: 'active',
        actions: ['query_data', 'update_records', 'generate_analytics', 'backup_data'],
        icon: <Database className="h-4 w-4" />
      },
      {
        name: 'System Configuration',
        status: 'active',
        actions: ['modify_settings', 'update_permissions', 'configure_modules', 'maintenance'],
        icon: <Settings className="h-4 w-4" />
      }
    ];
    setSystemModules(modules);
  };

  const executeAICommand = async () => {
    if (!input.trim() || isProcessing) return;

    const newCommand: AICommand = {
      id: Date.now().toString(),
      command: input.trim(),
      timestamp: new Date(),
      status: 'processing'
    };

    setCommands(prev => [newCommand, ...prev]);
    setInput('');
    setIsProcessing(true);
    setProgress(0);

    try {
      // Step 1: Analyze command intent
      setProgress(20);
      const { data: intentData, error: intentError } = await supabase.functions.invoke('ai-agent-intent', {
        body: {
          message: input.trim(),
          userId: userProfile?.id,
          userRole: userProfile?.role,
          context: {
            systemControl: true,
            allModules: true,
            previousCommands: commands.slice(0, 3)
          }
        }
      });

      if (intentError) throw intentError;

      // Step 2: Execute across all required modules
      setProgress(50);
      const moduleResults = [];
      
      for (const action of intentData?.actions || []) {
        const { data: execData, error: execError } = await supabase.functions.invoke('ai-agent-executor', {
          body: {
            intent: action.type || action.action || action, // Use 'intent' instead of 'action'
            query: action.description || `Execute ${action.type || action.action || action}`,
            userId: userProfile?.id,
            userRole: userProfile?.role,
            context: {
              systemWide: true,
              enhancedMode: true
            }
          }
        });

        if (execError) {
          console.error(`Module execution error for ${action.type}:`, execError);
        } else {
          moduleResults.push({ action: action.type, result: execData });
        }
      }

      // Step 3: Generate comprehensive response
      setProgress(80);
      const { data: responseData, error: responseError } = await supabase.functions.invoke('ai-agent-response', {
        body: {
          originalMessage: input.trim(),
          actions: moduleResults,
          userRole: userProfile?.role,
          context: intentData?.context,
          systemControl: true
        }
      });

      if (responseError) throw responseError;

      setProgress(100);

      // Update command status
      setCommands(prev => prev.map(cmd => 
        cmd.id === newCommand.id 
          ? { 
              ...cmd, 
              status: 'completed',
              result: responseData?.response,
              modules: moduleResults.map(r => r.action)
            }
          : cmd
      ));

      toast({
        title: "🤖 AI System Command Executed",
        description: `Successfully processed across ${moduleResults.length} system modules`,
      });

    } catch (error: any) {
      if (process.env.NODE_ENV === 'development') {
        console.error('AI System Control Error:', error);
      }

      setCommands(prev => prev.map(cmd =>
        cmd.id === newCommand.id
          ? { ...cmd, status: 'error', result: error.message }
          : cmd
      ));

      toast({
        title: "⚡ AI System Alert",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
      setProgress(0);
    }
  };

  const getModuleStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-600/20 text-green-300 border-green-500/50';
      case 'processing': return 'bg-yellow-600/20 text-yellow-300 border-yellow-500/50';
      case 'inactive': return 'bg-red-600/20 text-red-300 border-red-500/50';
      default: return 'bg-gray-600/20 text-gray-300 border-gray-500/50';
    }
  };

  const getCommandStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-600/20 text-green-300 border-green-500/50';
      case 'processing': return 'bg-blue-600/20 text-blue-300 border-blue-500/50';
      case 'error': return 'bg-red-600/20 text-red-300 border-red-500/50';
      default: return 'bg-gray-600/20 text-gray-300 border-gray-500/50';
    }
  };

  return (
    <div className="space-y-6">
      {/* AI Command Center */}
      <Card className="bg-card border-border">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-foreground">
            <Brain className="h-5 w-5 animate-pulse text-primary" />
            AI System Controller
            <Badge className="bg-primary/20 text-primary border-primary/50">
              Full System Access
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="🤖 Command the entire system: 'Create project Alpha with team John, Sarah' or 'Generate weekly reports for all departments'"
              className="flex-1 bg-background border-border text-foreground"
              onKeyPress={(e) => e.key === 'Enter' && executeAICommand()}
            />
            <Button
              onClick={executeAICommand}
              disabled={isProcessing || !input.trim()}
              className="bg-primary hover:bg-primary/90"
            >
              <Zap className="h-4 w-4 mr-2" />
              Execute
            </Button>
          </div>

          {isProcessing && (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-foreground">
                <Activity className="h-4 w-4 animate-spin" />
                <span className="text-sm">AI processing across all system modules...</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          )}

          <div className="text-xs text-muted-foreground">
            Examples: "Create telecom project with budget $50k", "Assign all overdue tasks to managers", 
            "Generate expense reports for Q4", "Update all user permissions for new security policy"
          </div>
        </CardContent>
      </Card>

      {/* System Modules Status */}
      <Card className="bg-card border-border">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-foreground">
            <MonitorDot className="h-5 w-5 text-primary" />
            System Modules Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {systemModules.map((module, index) => (
              <div
                key={index}
                className="p-4 rounded-lg bg-black/20 border border-green-500/20 hover:border-green-500/40 transition-colors"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    {module.icon}
                    <h4 className="font-medium text-green-100">{module.name}</h4>
                  </div>
                  <Badge className={getModuleStatusColor(module.status)}>
                    {module.status}
                  </Badge>
                </div>
                <div className="space-y-1">
                  {module.actions.map((action, actionIndex) => (
                    <div key={actionIndex} className="text-xs text-green-400/70 flex items-center gap-1">
                      <div className="w-1.5 h-1.5 rounded-full bg-green-500/50"></div>
                      {action.replace('_', ' ').toUpperCase()}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Command History */}
      {commands.length > 0 && (
        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-foreground">
              <Activity className="h-5 w-5 text-primary" />
              AI Command History
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[300px]">
              <div className="space-y-3">
                {commands.map((command) => (
                  <div
                    key={command.id}
                    className="p-3 rounded-lg bg-muted border border-border"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <p className="text-foreground text-sm font-medium">{command.command}</p>
                        <p className="text-xs text-muted-foreground">
                          {command.timestamp.toLocaleString()}
                        </p>
                      </div>
                      <Badge className={getCommandStatusColor(command.status)}>
                        {command.status}
                      </Badge>
                    </div>
                    
                    {command.modules && command.modules.length > 0 && (
                      <div className="mb-2">
                        <p className="text-xs text-muted-foreground mb-1">Affected Modules:</p>
                        <div className="flex flex-wrap gap-1">
                          {command.modules.map((module, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {module.replace('_', ' ')}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    {command.result && (
                      <div className="text-xs text-purple-200 bg-black/20 p-2 rounded">
                        {typeof command.result === 'string' ? command.result : JSON.stringify(command.result, null, 2)}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}
    </div>
  );
};