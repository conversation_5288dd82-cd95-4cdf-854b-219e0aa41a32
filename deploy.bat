@echo off
REM 🚀 CT Nigeria AI Workboard - Automated Deployment Script (Windows)
REM This script handles the complete deployment process with all fixes

echo 🚀 Starting CT Nigeria AI Workboard Deployment...
echo ==================================================

REM Check if we're in the right directory
if not exist "package.json" (
    echo [ERROR] package.json not found. Please run this script from the project root.
    pause
    exit /b 1
)

REM Check if npm is available
where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] npm is not installed. Please install Node.js and npm.
    pause
    exit /b 1
)

echo [SUCCESS] All required tools are available

REM Step 1: Clean and prepare
echo [INFO] Step 1: Cleaning previous builds...
call npm run clear-cache
if %errorlevel% neq 0 (
    echo [WARNING] Cache clearing failed, continuing anyway...
)

REM Step 2: Install dependencies
echo [INFO] Step 2: Installing dependencies...
call npm ci
if %errorlevel% neq 0 (
    echo [ERROR] npm install failed. Please check the errors above.
    pause
    exit /b 1
)

REM Step 3: Run production build
echo [INFO] Step 3: Building for production...
call npm run build:prod
if %errorlevel% neq 0 (
    echo [ERROR] Build failed. Please check the errors above.
    pause
    exit /b 1
)

echo [SUCCESS] Build completed successfully

REM Step 4: Check deployment options
echo [INFO] Step 4: Checking deployment options...

REM Check if Vercel CLI is available
where vercel >nul 2>nul
if %errorlevel% equ 0 (
    echo [INFO] Vercel CLI found. Deploying to Vercel...
    call vercel --prod
    if %errorlevel% equ 0 (
        echo [SUCCESS] Deployment to Vercel completed successfully!
        echo [INFO] Your app should be available at: https://ai.ctnigeria.com
    ) else (
        echo [ERROR] Vercel deployment failed.
        pause
        exit /b 1
    )
) else (
    echo [WARNING] Vercel CLI not found. Please install it with: npm install -g vercel
    echo [INFO] Alternative: Push to git and let Vercel auto-deploy
    
    REM Check if we're in a git repository
    if exist ".git" (
        echo [INFO] Git repository detected. Preparing for git-based deployment...
        
        REM Add all changes
        git add .
        
        REM Commit with timestamp
        for /f "tokens=1-4 delims=/ " %%i in ('date /t') do set mydate=%%i-%%j-%%k
        for /f "tokens=1-2 delims=: " %%i in ('time /t') do set mytime=%%i:%%j
        git commit -m "feat: comprehensive fixes and deployment - %mydate% %mytime%"
        
        REM Push to main branch
        echo [INFO] Pushing to main branch...
        git push origin main
        if %errorlevel% equ 0 (
            echo [SUCCESS] Code pushed to git successfully!
            echo [INFO] Vercel should auto-deploy from git. Check your Vercel dashboard.
        ) else (
            echo [ERROR] Git push failed.
            pause
            exit /b 1
        )
    ) else (
        echo [ERROR] No git repository found and Vercel CLI not available.
        echo [INFO] Please either:
        echo [INFO] 1. Install Vercel CLI: npm install -g vercel
        echo [INFO] 2. Initialize git repository: git init
        pause
        exit /b 1
    )
)

REM Step 5: Post-deployment verification
echo [INFO] Step 5: Post-deployment verification...
echo [INFO] Please verify the following manually:
echo.
echo ✅ Database Migrations Applied:
echo    - user_presence table
echo    - tasks table
echo    - profiles table
echo    - time_logs table
echo.
echo ✅ Test the application:
echo    - Visit: https://ai.ctnigeria.com
echo    - Test page: https://ai.ctnigeria.com/dashboard/fix-test
echo    - Run all fixes and verify they pass
echo.
echo ✅ Critical functionality:
echo    - User authentication
echo    - Time tracking (clock in/out)
echo    - Navigation to all routes
echo    - Database operations
echo.

echo [SUCCESS] Deployment script completed!
echo [INFO] Next steps:
echo 1. Apply database migrations (see DEPLOYMENT_INSTRUCTIONS.md)
echo 2. Test the application thoroughly
echo 3. Monitor for any issues

echo.
echo ==================================================
echo 🎉 Deployment Complete!
echo ==================================================

pause
