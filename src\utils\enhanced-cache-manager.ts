/**
 * Enhanced Cache Manager
 * Fixes cache clearing warnings and improves performance
 */

export class EnhancedCacheManager {
  private static instance: EnhancedCacheManager;
  private queryClient: any = null;
  private isInitialized = false;

  static getInstance(): EnhancedCacheManager {
    if (!EnhancedCacheManager.instance) {
      EnhancedCacheManager.instance = new EnhancedCacheManager();
    }
    return EnhancedCacheManager.instance;
  }

  /**
   * Initialize with QueryClient
   */
  initialize(queryClient: any): void {
    this.queryClient = queryClient;
    this.isInitialized = true;
    console.log('✅ Enhanced Cache Manager initialized');
  }

  /**
   * Safe cache clearing with proper error handling
   */
  async clearAll(): Promise<void> {
    try {
      console.log('🧹 Starting enhanced cache clearing...');

      // Clear React Query cache if available
      if (this.queryClient && this.isInitialized) {
        try {
          await this.queryClient.clear();
          console.log('✅ React Query cache cleared');
        } catch (error) {
          console.warn('⚠️ React Query cache clearing failed:', error);
        }
      } else {
        console.log('ℹ️ QueryClient not available, skipping React Query cache');
      }

      // Clear browser caches
      await this.clearBrowserCaches();

      // Clear localStorage safely
      this.clearLocalStorage();

      // Clear sessionStorage safely
      this.clearSessionStorage();

      console.log('✅ Enhanced cache clearing completed');
    } catch (error) {
      console.error('❌ Enhanced cache clearing failed:', error);
    }
  }

  /**
   * Clear browser caches safely
   */
  private async clearBrowserCaches(): Promise<void> {
    if ('caches' in window) {
      try {
        const cacheNames = await caches.keys();
        console.log(`🔍 Found ${cacheNames.length} browser caches`);

        const clearPromises = cacheNames.map(async (cacheName) => {
          try {
            const success = await caches.delete(cacheName);
            console.log(`${success ? '✅' : '❌'} Cache ${cacheName}: ${success ? 'cleared' : 'failed'}`);
            return success;
          } catch (error) {
            console.warn(`⚠️ Failed to clear cache ${cacheName}:`, error);
            return false;
          }
        });

        await Promise.allSettled(clearPromises);
      } catch (error) {
        console.warn('⚠️ Browser cache clearing failed:', error);
      }
    } else {
      console.log('ℹ️ Browser caches API not available');
    }
  }

  /**
   * Clear localStorage safely
   */
  private clearLocalStorage(): void {
    try {
      const keysToKeep = [
        'auth-token',
        'user-preferences',
        'theme-settings',
        'language-setting'
      ];

      const allKeys = Object.keys(localStorage);
      const keysToRemove = allKeys.filter(key => !keysToKeep.includes(key));

      console.log(`🧹 Clearing ${keysToRemove.length} localStorage keys`);
      
      keysToRemove.forEach(key => {
        try {
          localStorage.removeItem(key);
        } catch (error) {
          console.warn(`⚠️ Failed to remove localStorage key ${key}:`, error);
        }
      });

      console.log('✅ localStorage cleared (keeping essential keys)');
    } catch (error) {
      console.warn('⚠️ localStorage clearing failed:', error);
    }
  }

  /**
   * Clear sessionStorage safely
   */
  private clearSessionStorage(): void {
    try {
      const keysToKeep = [
        'navigation-state',
        'form-data'
      ];

      const allKeys = Object.keys(sessionStorage);
      const keysToRemove = allKeys.filter(key => !keysToKeep.includes(key));

      console.log(`🧹 Clearing ${keysToRemove.length} sessionStorage keys`);
      
      keysToRemove.forEach(key => {
        try {
          sessionStorage.removeItem(key);
        } catch (error) {
          console.warn(`⚠️ Failed to remove sessionStorage key ${key}:`, error);
        }
      });

      console.log('✅ sessionStorage cleared (keeping essential keys)');
    } catch (error) {
      console.warn('⚠️ sessionStorage clearing failed:', error);
    }
  }

  /**
   * Clear specific cache by key
   */
  async clearCache(cacheKey: string): Promise<boolean> {
    try {
      if ('caches' in window) {
        const success = await caches.delete(cacheKey);
        console.log(`${success ? '✅' : '❌'} Cache ${cacheKey}: ${success ? 'cleared' : 'not found'}`);
        return success;
      }
      return false;
    } catch (error) {
      console.warn(`⚠️ Failed to clear cache ${cacheKey}:`, error);
      return false;
    }
  }

  /**
   * Clear React Query cache for specific key
   */
  clearQueryCache(queryKey: string[]): void {
    if (this.queryClient && this.isInitialized) {
      try {
        this.queryClient.removeQueries(queryKey);
        console.log('✅ Query cache cleared for:', queryKey);
      } catch (error) {
        console.warn('⚠️ Query cache clearing failed for:', queryKey, error);
      }
    }
  }

  /**
   * Invalidate React Query cache for specific key
   */
  async invalidateQueryCache(queryKey: string[]): Promise<void> {
    if (this.queryClient && this.isInitialized) {
      try {
        await this.queryClient.invalidateQueries(queryKey);
        console.log('✅ Query cache invalidated for:', queryKey);
      } catch (error) {
        console.warn('⚠️ Query cache invalidation failed for:', queryKey, error);
      }
    }
  }

  /**
   * Handle server restart detection
   */
  handleServerRestart(): void {
    console.log('🔄 Server restart detected, performing safe cache refresh...');
    
    // Only clear non-essential caches on server restart
    this.clearQueryCache(['projects', 'tasks', 'documents']);
    
    // Refresh critical data
    if (this.queryClient && this.isInitialized) {
      this.invalidateQueryCache(['user-profile']);
      this.invalidateQueryCache(['auth-status']);
    }
  }

  /**
   * Optimize cache performance
   */
  optimizeCache(): void {
    console.log('⚡ Optimizing cache performance...');
    
    // Clear old cache entries
    this.clearOldCacheEntries();
    
    // Preload critical data
    this.preloadCriticalData();
  }

  /**
   * Clear old cache entries
   */
  private clearOldCacheEntries(): void {
    try {
      const now = Date.now();
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours

      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('cache-')) {
          try {
            const item = localStorage.getItem(key);
            if (item) {
              const data = JSON.parse(item);
              if (data.timestamp && (now - data.timestamp) > maxAge) {
                localStorage.removeItem(key);
                console.log(`🧹 Removed old cache entry: ${key}`);
              }
            }
          } catch (error) {
            // Invalid JSON, remove it
            localStorage.removeItem(key);
          }
        }
      });
    } catch (error) {
      console.warn('⚠️ Old cache entries cleanup failed:', error);
    }
  }

  /**
   * Preload critical data
   */
  private preloadCriticalData(): void {
    if (this.queryClient && this.isInitialized) {
      try {
        // Prefetch user profile
        this.queryClient.prefetchQuery({
          queryKey: ['user-profile'],
          staleTime: 5 * 60 * 1000 // 5 minutes
        });

        console.log('✅ Critical data preloading initiated');
      } catch (error) {
        console.warn('⚠️ Critical data preloading failed:', error);
      }
    }
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<{
    browserCaches: number;
    localStorageKeys: number;
    sessionStorageKeys: number;
    queryClientStatus: string;
  }> {
    const stats = {
      browserCaches: 0,
      localStorageKeys: Object.keys(localStorage).length,
      sessionStorageKeys: Object.keys(sessionStorage).length,
      queryClientStatus: this.isInitialized ? 'initialized' : 'not initialized'
    };

    if ('caches' in window) {
      try {
        const cacheNames = await caches.keys();
        stats.browserCaches = cacheNames.length;
      } catch (error) {
        console.warn('⚠️ Failed to get browser cache stats:', error);
      }
    }

    return stats;
  }
}

// Global instance
export const enhancedCacheManager = EnhancedCacheManager.getInstance();

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).enhancedCacheManager = enhancedCacheManager;
  (window as any).clearEnhancedCache = () => enhancedCacheManager.clearAll();
  (window as any).getCacheStats = () => enhancedCacheManager.getCacheStats();
  
  console.log('🧹 Enhanced Cache Manager loaded. Available commands:');
  console.log('  - clearEnhancedCache()');
  console.log('  - getCacheStats()');
}
