/**
 * Modern Document Card Component
 * Neumorphic design with brand colors and contemporary UI trends
 */

import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  Download, 
  Eye, 
  Trash2, 
  MoreVertical,
  Calendar,
  User,
  HardDrive,
  Star
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface DocumentType {
  id: string;
  title: string;
  file_name: string;
  file_type: string;
  file_size: number;
  category: string;
  tags: string[];
  uploaded_by: string;
  created_at: string;
  uploader_name?: string;
}

interface ModernDocumentCardProps {
  document: DocumentType;
  onView?: (document: DocumentType) => void;
  onDownload?: (document: DocumentType) => void;
  onDelete?: (document: DocumentType) => void;
  className?: string;
}

export const ModernDocumentCard = ({
  document,
  onView,
  onDownload,
  onDelete,
  className
}: ModernDocumentCardProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);

  const getFileIcon = (fileType: string) => {
    if (fileType.includes('image')) return '🖼️';
    if (fileType.includes('pdf')) return '📄';
    if (fileType.includes('word') || fileType.includes('doc')) return '📝';
    if (fileType.includes('excel') || fileType.includes('sheet')) return '📊';
    if (fileType.includes('powerpoint') || fileType.includes('presentation')) return '📈';
    return '📄';
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <Card 
      className={cn(
        "group relative overflow-hidden transition-all duration-500 ease-out",
        "bg-gradient-to-br from-[#1a1a1a] via-[#0f0f0f] to-[#1a1a1a]",
        "border-[0.8px] border-[#ff1c04]/30",
        "shadow-[8px_8px_16px_rgba(0,0,0,0.4),-8px_-8px_16px_rgba(255,28,4,0.1)]",
        "hover:shadow-[12px_12px_24px_rgba(0,0,0,0.6),-12px_-12px_24px_rgba(255,28,4,0.2)]",
        "hover:transform hover:-translate-y-2 hover:scale-[1.02]",
        "rounded-2xl",
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      data-aos="fade-up"
      data-aos-duration="600"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div 
          className="absolute inset-0"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ff1c04' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }}
        />
      </div>

      {/* Favorite Button */}
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          "absolute top-3 right-3 z-10 p-2 rounded-full transition-all duration-300",
          "bg-white/80 backdrop-blur-sm border border-white/20",
          "hover:bg-[#ff1c04]/10 hover:border-[#ff1c04]/30",
          isHovered ? "opacity-100 translate-y-0" : "opacity-0 translate-y-2"
        )}
        onClick={() => setIsFavorite(!isFavorite)}
      >
        <Star 
          className={cn(
            "h-4 w-4 transition-colors duration-300",
            isFavorite ? "fill-[#ff1c04] text-[#ff1c04]" : "text-gray-400"
          )} 
        />
      </Button>

      <CardContent className="p-6 relative z-10">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className={cn(
              "w-12 h-12 rounded-xl flex items-center justify-center text-2xl",
              "bg-gradient-to-br from-[#ff1c04]/10 to-[#ff1c04]/20",
              "border border-[#ff1c04]/20",
              "shadow-[4px_4px_8px_rgba(255,28,4,0.1),-4px_-4px_8px_rgba(255,255,255,0.8)]"
            )}>
              {getFileIcon(document.file_type)}
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-bold text-[#e5e5e5] text-lg leading-tight mb-1 line-clamp-2">
                {document.title}
              </h3>
              <p className="text-sm text-[#a5a5a5] truncate">
                {document.file_name}
              </p>
            </div>
          </div>

          <Button 
            variant="ghost" 
            size="sm"
            className={cn(
              "p-2 rounded-full transition-all duration-300",
              "hover:bg-[#ff1c04]/10 hover:text-[#ff1c04]",
              isHovered ? "opacity-100" : "opacity-60"
            )}
          >
            <MoreVertical className="h-4 w-4" />
          </Button>
        </div>

        {/* File Type Badge */}
        <div className="mb-4">
          <Badge 
            variant="outline" 
            className="text-xs font-medium border-[0.8px] bg-[#ff1c04]/10 text-[#ff1c04] border-[#ff1c04]/30"
          >
            {document.file_type.split('/')[1]?.toUpperCase() || 'FILE'}
          </Badge>
        </div>

        {/* Tags */}
        {document.tags && document.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-4">
            {document.tags.slice(0, 3).map((tag, index) => (
              <Badge 
                key={index}
                variant="secondary"
                className="text-xs px-2 py-1 bg-gray-100 text-gray-600 border-0"
              >
                #{tag}
              </Badge>
            ))}
            {document.tags.length > 3 && (
              <Badge variant="secondary" className="text-xs px-2 py-1 bg-gray-100 text-gray-600 border-0">
                +{document.tags.length - 3}
              </Badge>
            )}
          </div>
        )}

        {/* Metadata */}
        <div className="space-y-2 text-xs text-[#a5a5a5]">
          <div className="flex items-center gap-2">
            <HardDrive className="h-3 w-3" />
            <span>{formatBytes(document.file_size)}</span>
          </div>
          <div className="flex items-center gap-2">
            <Calendar className="h-3 w-3" />
            <span>{formatDate(document.created_at)}</span>
          </div>
          {document.uploader_name && (
            <div className="flex items-center gap-2">
              <User className="h-3 w-3" />
              <span className="truncate">{document.uploader_name}</span>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className={cn(
          "flex gap-2 mt-4 transition-all duration-300",
          isHovered ? "opacity-100 translate-y-0" : "opacity-0 translate-y-2"
        )}>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onView?.(document)}
            className={cn(
              "flex-1 border-[0.8px] border-[#ff1c04]/30 text-[#ff1c04]",
              "hover:bg-[#ff1c04]/10 hover:border-[#ff1c04]",
              "shadow-[2px_2px_4px_rgba(255,28,4,0.1),-2px_-2px_4px_rgba(255,255,255,0.8)]"
            )}
          >
            <Eye className="h-4 w-4 mr-2" />
            View
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onDownload?.(document)}
            className={cn(
              "flex-1 border-[0.8px] border-gray-300",
              "hover:bg-gray-50 hover:border-gray-400",
              "shadow-[2px_2px_4px_rgba(0,0,0,0.1),-2px_-2px_4px_rgba(255,255,255,0.8)]"
            )}
          >
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
        </div>
      </CardContent>

      {/* Hover Glow Effect */}
      <div className={cn(
        "absolute inset-0 rounded-2xl transition-opacity duration-500",
        "bg-gradient-to-r from-[#ff1c04]/5 via-transparent to-[#ff1c04]/5",
        isHovered ? "opacity-100" : "opacity-0"
      )} />
    </Card>
  );
};
