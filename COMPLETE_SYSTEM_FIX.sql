-- COMPLETE SYSTEM FIX - Run this in Supabase SQL Editor
-- This fixes ALL database issues and mismatches comprehensively

-- =====================================================
-- 1. FIX PROFILES TABLE - ADD ALL MISSING COLUMNS
-- =====================================================

-- Add all missing columns to profiles table
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS position TEXT;
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS hire_date DATE;
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS location TEXT;
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS employment_status TEXT DEFAULT 'active';
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS employee_id TEXT;
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS manager_id UUID REFERENCES auth.users(id);
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS salary NUMERIC(12,2);
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS emergency_contact JSONB;

-- =====================================================
-- 2. CREATE USER_PRESENCE TABLE
-- =====================================================

DROP TABLE IF EXISTS public.user_presence CASCADE;
CREATE TABLE public.user_presence (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    status TEXT DEFAULT 'offline',
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    current_page TEXT,
    location_data JSONB,
    device_info JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- =====================================================
-- 3. CREATE TIME_LOGS TABLE WITH CORRECT COLUMNS
-- =====================================================

DROP TABLE IF EXISTS public.time_logs CASCADE;
CREATE TABLE public.time_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    clock_in_time TIMESTAMP WITH TIME ZONE NOT NULL,
    clock_in_timestamp TIMESTAMP WITH TIME ZONE NOT NULL, -- Both column names for compatibility
    clock_out_time TIMESTAMP WITH TIME ZONE,
    clock_out_timestamp TIMESTAMP WITH TIME ZONE,
    total_hours NUMERIC(5,2) DEFAULT 0,
    break_duration NUMERIC(5,2) DEFAULT 0,
    overtime_hours NUMERIC(5,2) DEFAULT 0,
    status TEXT DEFAULT 'clocked_in',
    location_data JSONB,
    device_info JSONB,
    ip_address INET,
    notes TEXT,
    project_id UUID,
    task_id UUID,
    approved_by UUID REFERENCES auth.users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    is_approved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 4. CREATE DOCUMENT_ARCHIVE TABLE
-- =====================================================

DROP TABLE IF EXISTS public.document_archive CASCADE;
CREATE TABLE public.document_archive (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT,
    file_type TEXT,
    uploaded_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    department_id UUID,
    project_id UUID,
    folder_id UUID,
    tags TEXT[],
    version INTEGER DEFAULT 1,
    is_public BOOLEAN DEFAULT FALSE,
    download_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 5. CREATE USER_PREFERENCES TABLE
-- =====================================================

DROP TABLE IF EXISTS public.user_preferences CASCADE;
CREATE TABLE public.user_preferences (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    theme TEXT DEFAULT 'light',
    language TEXT DEFAULT 'en',
    timezone TEXT DEFAULT 'UTC',
    notifications JSONB DEFAULT '{"email": true, "push": true, "sms": false}'::jsonb,
    dashboard_layout JSONB DEFAULT '{}'::jsonb,
    preferences JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- =====================================================
-- 6. CREATE MEMOS TABLE WITH ALL COLUMNS
-- =====================================================

DROP TABLE IF EXISTS public.memos CASCADE;
CREATE TABLE public.memos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT,
    memo_type TEXT DEFAULT 'general',
    priority TEXT DEFAULT 'medium',
    visibility TEXT DEFAULT 'department',
    target_audience TEXT[],
    created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    department_id UUID,
    effective_date DATE,
    expiry_date DATE,
    tags TEXT[],
    status TEXT DEFAULT 'active',
    attachments JSONB DEFAULT '[]'::jsonb,
    read_by JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 7. CREATE PROCUREMENT_REQUESTS TABLE
-- =====================================================

DROP TABLE IF EXISTS public.procurement_requests CASCADE;
CREATE TABLE public.procurement_requests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    requested_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    department_id UUID,
    project_id UUID,
    status TEXT DEFAULT 'pending',
    priority TEXT DEFAULT 'medium',
    budget_allocated NUMERIC(12,2),
    total_cost NUMERIC(12,2) DEFAULT 0,
    approval_workflow JSONB DEFAULT '[]'::jsonb,
    attachments JSONB DEFAULT '[]'::jsonb,
    vendor_info JSONB,
    delivery_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 8. CREATE DEPARTMENTS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS public.departments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    manager_id UUID REFERENCES auth.users(id),
    budget NUMERIC(12,2),
    location TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 9. CREATE PROJECTS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS public.projects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'active',
    priority TEXT DEFAULT 'medium',
    start_date DATE,
    end_date DATE,
    budget NUMERIC(12,2),
    manager_id UUID REFERENCES auth.users(id),
    created_by UUID REFERENCES auth.users(id),
    department_id UUID REFERENCES public.departments(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 10. CREATE TASKS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS public.tasks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'pending',
    priority TEXT DEFAULT 'medium',
    assigned_to_id UUID REFERENCES auth.users(id),
    created_by UUID REFERENCES auth.users(id),
    project_id UUID REFERENCES public.projects(id),
    due_date DATE,
    completed_at TIMESTAMP WITH TIME ZONE,
    estimated_hours NUMERIC(5,2),
    actual_hours NUMERIC(5,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 11. ENABLE ROW LEVEL SECURITY
-- =====================================================

ALTER TABLE public.user_presence ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.time_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.document_archive ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.memos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.procurement_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.departments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 12. DROP EXISTING POLICIES
-- =====================================================

DROP POLICY IF EXISTS "Users can manage their own presence" ON public.user_presence;
DROP POLICY IF EXISTS "Users can manage their own time logs" ON public.time_logs;
DROP POLICY IF EXISTS "Users can manage documents" ON public.document_archive;
DROP POLICY IF EXISTS "Users can manage preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can view memos" ON public.memos;
DROP POLICY IF EXISTS "Users can view procurement" ON public.procurement_requests;
DROP POLICY IF EXISTS "Users can view departments" ON public.departments;
DROP POLICY IF EXISTS "Users can view projects" ON public.projects;
DROP POLICY IF EXISTS "Users can view tasks" ON public.tasks;

-- =====================================================
-- 13. CREATE COMPREHENSIVE POLICIES
-- =====================================================

-- User presence policies
CREATE POLICY "Users can manage their own presence"
ON public.user_presence FOR ALL TO authenticated
USING (auth.uid() = user_id);

-- Time logs policies
CREATE POLICY "Users can manage their own time logs"
ON public.time_logs FOR ALL TO authenticated
USING (auth.uid() = user_id);

-- Document archive policies
CREATE POLICY "Users can manage documents"
ON public.document_archive FOR ALL TO authenticated
USING (auth.uid() = uploaded_by OR is_public = true);

-- User preferences policies
CREATE POLICY "Users can manage preferences"
ON public.user_preferences FOR ALL TO authenticated
USING (auth.uid() = user_id);

-- Memos policies
CREATE POLICY "Users can view memos"
ON public.memos FOR ALL TO authenticated
USING (true);

-- Procurement policies
CREATE POLICY "Users can view procurement"
ON public.procurement_requests FOR ALL TO authenticated
USING (true);

-- Departments policies
CREATE POLICY "Users can view departments"
ON public.departments FOR ALL TO authenticated
USING (true);

-- Projects policies
CREATE POLICY "Users can view projects"
ON public.projects FOR ALL TO authenticated
USING (true);

-- Tasks policies
CREATE POLICY "Users can view tasks"
ON public.tasks FOR ALL TO authenticated
USING (true);

-- =====================================================
-- 14. CREATE CLOCK IN/OUT FUNCTIONS
-- =====================================================

-- Create clock in function with both timestamp columns
CREATE OR REPLACE FUNCTION public.clock_in(
    p_user_id UUID,
    p_location_data JSONB DEFAULT NULL,
    p_device_info JSONB DEFAULT NULL,
    p_notes TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_time_log_id UUID;
    v_now TIMESTAMP WITH TIME ZONE := NOW();
BEGIN
    INSERT INTO public.time_logs (
        user_id,
        clock_in_time,
        clock_in_timestamp,
        location_data,
        device_info,
        notes,
        status
    ) VALUES (
        p_user_id,
        v_now,
        v_now,
        p_location_data,
        p_device_info,
        p_notes,
        'clocked_in'
    ) RETURNING id INTO v_time_log_id;

    RETURN v_time_log_id;
END;
$$;

-- Create clock out function
CREATE OR REPLACE FUNCTION public.clock_out(
    p_user_id UUID,
    p_notes TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_time_log_id UUID;
    v_now TIMESTAMP WITH TIME ZONE := NOW();
BEGIN
    UPDATE public.time_logs
    SET
        clock_out_time = v_now,
        clock_out_timestamp = v_now,
        notes = COALESCE(p_notes, notes),
        status = 'clocked_out',
        updated_at = v_now
    WHERE user_id = p_user_id
    AND clock_out_time IS NULL
    RETURNING id INTO v_time_log_id;

    RETURN v_time_log_id;
END;
$$;

-- =====================================================
-- 15. GRANT ALL PERMISSIONS
-- =====================================================

GRANT ALL ON public.user_presence TO authenticated;
GRANT ALL ON public.time_logs TO authenticated;
GRANT ALL ON public.document_archive TO authenticated;
GRANT ALL ON public.user_preferences TO authenticated;
GRANT ALL ON public.memos TO authenticated;
GRANT ALL ON public.procurement_requests TO authenticated;
GRANT ALL ON public.departments TO authenticated;
GRANT ALL ON public.projects TO authenticated;
GRANT ALL ON public.tasks TO authenticated;

GRANT EXECUTE ON FUNCTION public.clock_in TO authenticated;
GRANT EXECUTE ON FUNCTION public.clock_out TO authenticated;

-- =====================================================
-- 16. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_user_presence_user_id ON public.user_presence(user_id);
CREATE INDEX IF NOT EXISTS idx_time_logs_user_id ON public.time_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_time_logs_clock_in ON public.time_logs(clock_in_time);
CREATE INDEX IF NOT EXISTS idx_document_archive_uploaded_by ON public.document_archive(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON public.user_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_memos_created_by ON public.memos(created_by);
CREATE INDEX IF NOT EXISTS idx_procurement_requested_by ON public.procurement_requests(requested_by);

-- =====================================================
-- 17. CREATE MISSING TABLES FOR COMPLETE SYSTEM
-- =====================================================

-- Create procurement_approvals table
CREATE TABLE IF NOT EXISTS public.procurement_approvals (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    procurement_request_id UUID REFERENCES public.procurement_requests(id) ON DELETE CASCADE,
    approver_id UUID REFERENCES auth.users(id),
    approver_role TEXT NOT NULL,
    approval_step TEXT NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    comments TEXT,
    approved_amount NUMERIC(12,2),
    conditions TEXT,
    approval_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create procurement_items table
CREATE TABLE IF NOT EXISTS public.procurement_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    procurement_request_id UUID REFERENCES public.procurement_requests(id) ON DELETE CASCADE,
    item_name TEXT NOT NULL,
    description TEXT,
    quantity INTEGER NOT NULL DEFAULT 1,
    unit_price NUMERIC(12,2),
    estimated_total_cost NUMERIC(12,2),
    procurement_status TEXT DEFAULT 'pending',
    supplier TEXT,
    specifications JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create notifications table
CREATE TABLE IF NOT EXISTS public.notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    message TEXT,
    type TEXT DEFAULT 'info',
    read BOOLEAN DEFAULT FALSE,
    action_url TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create activity_logs table
CREATE TABLE IF NOT EXISTS public.activity_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    action TEXT NOT NULL,
    resource_type TEXT,
    resource_id UUID,
    details JSONB DEFAULT '{}'::jsonb,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create project_assignments table
CREATE TABLE IF NOT EXISTS public.project_assignments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    assigned_to UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    assigned_by UUID REFERENCES auth.users(id),
    role TEXT DEFAULT 'member',
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(project_id, assigned_to)
);

-- Create task_assignments table
CREATE TABLE IF NOT EXISTS public.task_assignments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    task_id UUID REFERENCES public.tasks(id) ON DELETE CASCADE,
    assigned_to UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    assigned_by UUID REFERENCES auth.users(id),
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(task_id, assigned_to)
);

-- Create invoices table
CREATE TABLE IF NOT EXISTS public.invoices (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    invoice_number TEXT UNIQUE NOT NULL,
    client_name TEXT NOT NULL,
    client_email TEXT,
    project_id UUID REFERENCES public.projects(id),
    amount NUMERIC(12,2) NOT NULL,
    tax_amount NUMERIC(12,2) DEFAULT 0,
    total_amount NUMERIC(12,2) NOT NULL,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'sent', 'paid', 'overdue', 'cancelled')),
    approval_status TEXT DEFAULT 'pending' CHECK (approval_status IN ('pending', 'approved', 'rejected')),
    due_date DATE,
    issue_date DATE DEFAULT CURRENT_DATE,
    created_by UUID REFERENCES auth.users(id),
    approved_by UUID REFERENCES auth.users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 18. ENABLE RLS ON NEW TABLES
-- =====================================================

ALTER TABLE public.procurement_approvals ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.procurement_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.task_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 19. CREATE POLICIES FOR NEW TABLES
-- =====================================================

-- Procurement approvals policies
DROP POLICY IF EXISTS "Users can view procurement approvals" ON public.procurement_approvals;
CREATE POLICY "Users can view procurement approvals"
ON public.procurement_approvals FOR ALL TO authenticated
USING (true);

-- Procurement items policies
DROP POLICY IF EXISTS "Users can view procurement items" ON public.procurement_items;
CREATE POLICY "Users can view procurement items"
ON public.procurement_items FOR ALL TO authenticated
USING (true);

-- Notifications policies
DROP POLICY IF EXISTS "Users can manage their notifications" ON public.notifications;
CREATE POLICY "Users can manage their notifications"
ON public.notifications FOR ALL TO authenticated
USING (auth.uid() = user_id);

-- Activity logs policies
DROP POLICY IF EXISTS "Users can view activity logs" ON public.activity_logs;
CREATE POLICY "Users can view activity logs"
ON public.activity_logs FOR ALL TO authenticated
USING (true);

-- Project assignments policies
DROP POLICY IF EXISTS "Users can view project assignments" ON public.project_assignments;
CREATE POLICY "Users can view project assignments"
ON public.project_assignments FOR ALL TO authenticated
USING (true);

-- Task assignments policies
DROP POLICY IF EXISTS "Users can view task assignments" ON public.task_assignments;
CREATE POLICY "Users can view task assignments"
ON public.task_assignments FOR ALL TO authenticated
USING (true);

-- Invoices policies
DROP POLICY IF EXISTS "Users can view invoices" ON public.invoices;
CREATE POLICY "Users can view invoices"
ON public.invoices FOR ALL TO authenticated
USING (true);

-- =====================================================
-- 20. GRANT PERMISSIONS ON NEW TABLES
-- =====================================================

GRANT ALL ON public.procurement_approvals TO authenticated;
GRANT ALL ON public.procurement_items TO authenticated;
GRANT ALL ON public.notifications TO authenticated;
GRANT ALL ON public.activity_logs TO authenticated;
GRANT ALL ON public.project_assignments TO authenticated;
GRANT ALL ON public.task_assignments TO authenticated;
GRANT ALL ON public.invoices TO authenticated;

-- =====================================================
-- 21. CREATE ADDITIONAL INDEXES
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_procurement_approvals_request_id ON public.procurement_approvals(procurement_request_id);
CREATE INDEX IF NOT EXISTS idx_procurement_items_request_id ON public.procurement_items(procurement_request_id);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON public.activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_project_assignments_project_id ON public.project_assignments(project_id);
CREATE INDEX IF NOT EXISTS idx_task_assignments_task_id ON public.task_assignments(task_id);
CREATE INDEX IF NOT EXISTS idx_invoices_project_id ON public.invoices(project_id);

-- =====================================================
-- 22. SUCCESS MESSAGE
-- =====================================================

SELECT 'COMPLETE SYSTEM FIX COMPLETED SUCCESSFULLY! All database issues should now be resolved.' as result;
