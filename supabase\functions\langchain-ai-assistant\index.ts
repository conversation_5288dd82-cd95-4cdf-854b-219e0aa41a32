/**
 * <PERSON><PERSON><PERSON><PERSON> AI Assistant Supabase Edge Function
 * Enhanced AI assistant using <PERSON><PERSON><PERSON><PERSON> capabilities
 */

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { ChatOpenAI } from 'https://esm.sh/@langchain/openai';
import { ChatAnthropic } from 'https://esm.sh/@langchain/anthropic';
import { PromptTemplate } from 'https://esm.sh/@langchain/core/prompts';
import { LLMChain } from 'https://esm.sh/langchain/chains';
import { BufferMemory } from 'https://esm.sh/langchain/memory';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface AIRequest {
  message: string;
  userId?: string;
  sessionId?: string;
  context?: {
    interface?: string;
    role?: string;
    department?: string;
    modules?: string[];
    previousMessages?: any[];
    actions?: any[];
  };
  options?: {
    useRAG?: boolean;
    useMemory?: boolean;
    useAgent?: boolean;
    model?: 'openai' | 'anthropic';
    temperature?: number;
    maxTokens?: number;
  };
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { message, userId, sessionId, context = {}, options = {} }: AIRequest = await req.json();

    if (!message?.trim()) {
      return new Response(
        JSON.stringify({ error: 'Message is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Initialize language model
    const model = await initializeModel(options.model);
    
    // Create enhanced prompt based on interface type
    const prompt = createPromptTemplate(context.interface);
    
    // Create LLM chain
    const chain = new LLMChain({ llm: model, prompt });

    // Build context string
    const contextString = buildContextString(context, userId);

    // Process the request
    const startTime = Date.now();
    
    let response: string;
    let metadata: any = {
      processingTime: 0,
      model: options.model || 'openai',
      interface: context.interface || 'standard',
    };

    // Handle different interface types
    switch (context.interface) {
      case 'hacker_terminal':
        response = await processHackerTerminalRequest(chain, message, contextString, context);
        break;
        
      case 'futuristic':
        response = await processFuturisticRequest(chain, message, contextString, context);
        break;
        
      case 'enhanced':
        response = await processEnhancedRequest(chain, message, contextString, context);
        break;
        
      default:
        response = await processStandardRequest(chain, message, contextString, context);
    }

    metadata.processingTime = Date.now() - startTime;

    // Save conversation to database if userId provided
    if (userId && sessionId) {
      await saveConversation(supabase, userId, sessionId, message, response, metadata);
    }

    return new Response(
      JSON.stringify({ 
        response,
        metadata,
        success: true 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('LangChain AI Assistant Error:', error);
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message,
        success: false 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});

async function initializeModel(modelType: string = 'openai') {
  if (modelType === 'anthropic') {
    return new ChatAnthropic({
      anthropicApiKey: Deno.env.get('ANTHROPIC_API_KEY'),
      modelName: 'claude-3-sonnet-20240229',
      temperature: 0.7,
      maxTokens: 4096,
    });
  } else {
    return new ChatOpenAI({
      openAIApiKey: Deno.env.get('OPENAI_API_KEY'),
      modelName: 'gpt-4-turbo-preview',
      temperature: 0.7,
      maxTokens: 4096,
    });
  }
}

function createPromptTemplate(interfaceType?: string): PromptTemplate {
  let systemPrompt = '';

  switch (interfaceType) {
    case 'hacker_terminal':
      systemPrompt = `You are a sophisticated AI system operating in a hacker-style terminal interface for the CTNL AI Workboard. 

Respond in a technical, precise manner with:
- Terminal-style formatting when appropriate
- Technical insights and system information
- Command-like responses for system queries
- Cybersecurity and system administration context
- Use technical jargon appropriately

Context: {context}
User Input: {input}

Response:`;
      break;

    case 'futuristic':
      systemPrompt = `You are an advanced AI assistant with futuristic capabilities for the CTNL AI Workboard system.

You can:
- Execute complex multi-step workflows
- Analyze data and generate insights
- Perform system actions and integrations
- Provide predictive analytics
- Handle advanced automation tasks

Context: {context}
User Request: {input}

AI Response:`;
      break;

    case 'enhanced':
      systemPrompt = `You are an enhanced AI assistant for the CTNL AI Workboard with advanced conversational abilities.

Provide:
- Detailed, helpful responses
- Context-aware suggestions
- Proactive assistance
- Rich formatting when appropriate
- Follow-up questions when needed

Context: {context}
User Message: {input}

Assistant Response:`;
      break;

    default:
      systemPrompt = `You are a helpful AI assistant for the CTNL AI Workboard system.

Provide accurate, helpful responses about:
- Work management and productivity
- System features and capabilities
- Data analysis and reporting
- Task management and collaboration
- General assistance with the platform

Context: {context}
User Question: {input}

Answer:`;
  }

  return PromptTemplate.fromTemplate(systemPrompt);
}

function buildContextString(context: any, userId?: string): string {
  const parts: string[] = [];

  if (userId) parts.push(`User ID: ${userId}`);
  if (context.role) parts.push(`User Role: ${context.role}`);
  if (context.department) parts.push(`Department: ${context.department}`);
  if (context.interface) parts.push(`Interface: ${context.interface}`);
  
  if (context.modules?.length > 0) {
    parts.push(`Available Modules: ${context.modules.join(', ')}`);
  }
  
  if (context.previousMessages?.length > 0) {
    const recentMessages = context.previousMessages.slice(-3);
    parts.push(`Recent Messages: ${JSON.stringify(recentMessages)}`);
  }

  if (context.actions?.length > 0) {
    parts.push(`Recent Actions: ${JSON.stringify(context.actions)}`);
  }

  return parts.join('\n');
}

async function processHackerTerminalRequest(
  chain: LLMChain, 
  message: string, 
  context: string, 
  requestContext: any
): Promise<string> {
  // Handle terminal-specific commands
  const lowerMessage = message.toLowerCase();
  
  if (lowerMessage.includes('status') || lowerMessage.includes('system')) {
    return await chain.call({
      context: `${context}\nCommand Type: System Status Query`,
      input: message
    }).then(result => result.text);
  }
  
  if (lowerMessage.includes('scan') || lowerMessage.includes('analyze')) {
    return await chain.call({
      context: `${context}\nCommand Type: Analysis Request`,
      input: message
    }).then(result => result.text);
  }

  return await chain.call({ context, input: message }).then(result => result.text);
}

async function processFuturisticRequest(
  chain: LLMChain, 
  message: string, 
  context: string, 
  requestContext: any
): Promise<string> {
  // Handle action-based requests
  const enhancedContext = `${context}\nActions Available: create_project, assign_task, query_database, analyze_file, generate_report`;
  
  return await chain.call({
    context: enhancedContext,
    input: message
  }).then(result => result.text);
}

async function processEnhancedRequest(
  chain: LLMChain, 
  message: string, 
  context: string, 
  requestContext: any
): Promise<string> {
  // Enhanced conversational processing
  return await chain.call({ context, input: message }).then(result => result.text);
}

async function processStandardRequest(
  chain: LLMChain, 
  message: string, 
  context: string, 
  requestContext: any
): Promise<string> {
  // Standard processing
  return await chain.call({ context, input: message }).then(result => result.text);
}

async function saveConversation(
  supabase: any,
  userId: string,
  sessionId: string,
  message: string,
  response: string,
  metadata: any
): Promise<void> {
  try {
    // Get existing conversation or create new one
    const { data: existingConversation } = await supabase
      .from('langchain_conversations')
      .select('messages')
      .eq('id', sessionId)
      .single();

    const messages = existingConversation?.messages || [];
    
    // Add user message
    messages.push({
      id: `msg_${Date.now()}_user`,
      role: 'user',
      content: message,
      timestamp: new Date().toISOString(),
      metadata: { interface: metadata.interface }
    });

    // Add AI response
    messages.push({
      id: `msg_${Date.now()}_assistant`,
      role: 'assistant',
      content: response,
      timestamp: new Date().toISOString(),
      metadata: metadata
    });

    // Upsert conversation
    await supabase.from('langchain_conversations').upsert({
      id: sessionId,
      user_id: userId,
      title: `Conversation ${new Date().toLocaleDateString()}`,
      messages: messages,
      context: {
        interface: metadata.interface,
        model: metadata.model,
        processingTime: metadata.processingTime
      },
      updated_at: new Date().toISOString()
    });
  } catch (error) {
    console.error('Failed to save conversation:', error);
  }
}
