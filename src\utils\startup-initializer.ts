/**
 * Startup Initializer
 * Automatically runs critical fixes and initializations on app startup
 */

import { SimpleDatabaseFixes } from './simple-database-fixes';
import { DatabaseRelationshipFixer } from './database-relationship-fixer';
import { databaseSchemaValidator } from './database-schema-validator';
import { ComprehensiveCodeAnalyzer } from './comprehensive-code-analyzer';
import { SystemAnalysisReport } from './system-analysis-report';
import { CompleteSystemFixer } from './complete-system-fixer';
import { CompleteDatabaseFixer } from './complete-database-fixer';
import { webSocketConnectionFixer } from './websocket-connection-fixer';
import { supabaseRealtimeManager } from './supabase-realtime-manager';
import { supabaseDatabaseService } from './supabase-database-service';
import { simplePresenceService } from '@/services/simple-presence-service';
import { fallbackPresenceService } from '@/services/fallback-presence-service';
import { enhancedCacheManager } from './enhanced-cache-manager';
import { webSocketManager } from './websocket-manager';

export class StartupInitializer {
  private static instance: StartupInitializer;
  private isInitialized = false;
  private initializationPromise: Promise<void> | null = null;

  static getInstance(): StartupInitializer {
    if (!StartupInitializer.instance) {
      StartupInitializer.instance = new StartupInitializer();
    }
    return StartupInitializer.instance;
  }

  /**
   * Initialize the application with all critical fixes
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('✅ Startup initialization already completed');
      return;
    }

    if (this.initializationPromise) {
      console.log('⏳ Startup initialization in progress, waiting...');
      return this.initializationPromise;
    }

    this.initializationPromise = this.performInitialization();
    return this.initializationPromise;
  }

  /**
   * Perform the actual initialization
   */
  private async performInitialization(): Promise<void> {
    try {
      console.log('🚀 Starting application initialization...');
      const startTime = Date.now();

      // Step 1: Initialize cache manager
      console.log('📦 Step 1: Initializing cache management...');
      try {
        // Cache manager should already be initialized in main.tsx
        console.log('✅ Cache management ready');
      } catch (error) {
        console.warn('⚠️ Cache management initialization failed:', error);
      }

      // Step 2: Skip database schema validation during startup (causes unauthenticated API calls)
      console.log('🔍 Step 2: Skipping database schema validation during startup...');
      console.log('✅ Database schema validation available via window.validateDatabaseSchema()');

      // Step 3: Skip database fixes during startup (causes unauthenticated API calls)
      console.log('🗄️ Step 3: Skipping database fixes during startup...');
      console.log('✅ Database fixes available via window.runDatabaseFixes()');

      // Step 4: Skip presence service initialization during startup (causes unauthenticated API calls)
      console.log('👥 Step 4: Skipping presence service initialization during startup...');
      console.log('✅ Presence services will initialize after user authentication');

      // Step 5: Skip Supabase Realtime initialization during startup (causes unauthenticated API calls)
      console.log('🔌 Step 5: Skipping Supabase Realtime initialization during startup...');
      console.log('✅ Realtime services will initialize after user authentication');
      console.log('✅ Real-time initialization skipped');

      // Step 6: Clear any stale data
      console.log('🧹 Step 6: Clearing stale data...');
      try {
        await this.clearStaleData();
        console.log('✅ Stale data cleared');
      } catch (error) {
        console.warn('⚠️ Stale data clearing failed:', error);
      }

      // Step 7: Run comprehensive analysis (optional, in background)
      console.log('🔍 Step 7: Running comprehensive system analysis...');
      try {
        // Run analysis in background without blocking
        ComprehensiveCodeAnalyzer.runFullAnalysis().then(analysis => {
          console.log('📊 Comprehensive analysis completed:', analysis.summary);

          // Generate report if there are issues
          if (analysis.summary.totalIssues > 0) {
            SystemAnalysisReport.generateCompleteReport().then(report => {
              console.log('📋 System Analysis Report Generated');
              console.log('Run generateSystemReport() in console to see full report');
            });
          }
        }).catch(error => {
          console.warn('⚠️ Comprehensive analysis failed (non-critical):', error);
        });

        console.log('✅ Comprehensive analysis initiated');
      } catch (error) {
        console.warn('⚠️ Comprehensive analysis failed:', error);
      }

      const duration = Date.now() - startTime;
      console.log(`🎉 Application initialization completed in ${duration}ms`);

      this.isInitialized = true;
    } catch (error) {
      console.error('❌ Application initialization failed:', error);
      // Don't throw the error, let the app continue
    } finally {
      this.initializationPromise = null;
    }
  }

  /**
   * Clear stale data and caches
   */
  private async clearStaleData(): Promise<void> {
    try {
      // Clear old cache entries
      const now = Date.now();
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours

      // Clear localStorage cache entries
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('cache-') || key.startsWith('temp-')) {
          try {
            const item = localStorage.getItem(key);
            if (item) {
              const data = JSON.parse(item);
              if (data.timestamp && (now - data.timestamp) > maxAge) {
                localStorage.removeItem(key);
                console.log(`🧹 Removed stale cache: ${key}`);
              }
            }
          } catch {
            // Invalid JSON, remove it
            localStorage.removeItem(key);
          }
        }
      });

      // Clear sessionStorage temp data
      Object.keys(sessionStorage).forEach(key => {
        if (key.startsWith('temp-') || key.startsWith('error-')) {
          sessionStorage.removeItem(key);
        }
      });

      console.log('✅ Stale data cleanup completed');
    } catch (error) {
      console.warn('⚠️ Stale data cleanup failed:', error);
    }
  }

  /**
   * Check if initialization is complete
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * Force re-initialization
   */
  async reinitialize(): Promise<void> {
    console.log('🔄 Forcing re-initialization...');
    this.isInitialized = false;
    this.initializationPromise = null;
    return this.initialize();
  }

  /**
   * Get initialization status
   */
  getStatus(): {
    isInitialized: boolean;
    isInitializing: boolean;
  } {
    return {
      isInitialized: this.isInitialized,
      isInitializing: this.initializationPromise !== null && !this.isInitialized
    };
  }
}

// Global instance
export const startupInitializer = StartupInitializer.getInstance();

// Auto-initialize when module loads
if (typeof window !== 'undefined') {
  // Initialize after a short delay to ensure DOM is ready
  setTimeout(() => {
    startupInitializer.initialize().catch(error => {
      console.error('❌ Auto-initialization failed:', error);
    });
  }, 100);

  // Make available globally for debugging
  (window as any).startupInitializer = startupInitializer;
  (window as any).reinitializeApp = () => startupInitializer.reinitialize();
  (window as any).getAppStatus = () => startupInitializer.getStatus();
  
  console.log('🚀 Startup Initializer loaded. Available commands:');
  console.log('  - reinitializeApp()');
  console.log('  - getAppStatus()');
}
