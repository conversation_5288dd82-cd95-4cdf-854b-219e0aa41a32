/**
 * WebSocket Connection Fixer
 * Handles WebSocket connection issues and Cloudflare cookie problems
 */

import { supabase } from '@/integrations/supabase/client';

export class WebSocketConnectionFixer {
  private static instance: WebSocketConnectionFixer;
  private connectionAttempts = 0;
  private maxAttempts = 3;
  private isFixing = false;

  static getInstance(): WebSocketConnectionFixer {
    if (!WebSocketConnectionFixer.instance) {
      WebSocketConnectionFixer.instance = new WebSocketConnectionFixer();
    }
    return WebSocketConnectionFixer.instance;
  }

  /**
   * Fix WebSocket connection issues
   */
  async fixWebSocketConnections(): Promise<{ success: boolean; message: string }> {
    if (this.isFixing) {
      return { success: false, message: 'Fix already in progress' };
    }

    this.isFixing = true;
    console.log('🔌 Fixing WebSocket connections...');

    try {
      // Step 1: Disconnect all existing channels
      await this.disconnectAllChannels();

      // Step 2: Clear any cached connection data
      await this.clearConnectionCache();

      // Step 3: Wait before reconnecting
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Step 4: Test basic connectivity
      const connectivityTest = await this.testConnectivity();
      if (!connectivityTest.success) {
        console.warn('⚠️ Connectivity test failed, continuing with limited functionality');
      }

      // Step 5: Initialize minimal real-time features
      const initResult = await this.initializeMinimalRealtime();

      this.connectionAttempts++;
      this.isFixing = false;

      if (initResult.success) {
        console.log('✅ WebSocket connections fixed successfully');
        return { success: true, message: 'WebSocket connections restored' };
      } else {
        console.warn('⚠️ WebSocket fix partially successful');
        return { success: true, message: 'Limited real-time functionality available' };
      }
    } catch (error: any) {
      this.isFixing = false;
      console.error('❌ WebSocket fix failed:', error);
      return { success: false, message: `Fix failed: ${error.message}` };
    }
  }

  /**
   * Disconnect all existing channels
   */
  private async disconnectAllChannels(): Promise<void> {
    try {
      console.log('🔌 Disconnecting all WebSocket channels...');
      
      // Get all channels and remove them
      const channels = supabase.getChannels();
      for (const channel of channels) {
        try {
          await supabase.removeChannel(channel);
        } catch (error) {
          console.warn('⚠️ Failed to remove channel:', error);
        }
      }

      console.log('✅ All channels disconnected');
    } catch (error) {
      console.warn('⚠️ Channel disconnection failed:', error);
    }
  }

  /**
   * Clear connection cache and cookies
   */
  private async clearConnectionCache(): Promise<void> {
    try {
      console.log('🧹 Clearing connection cache...');
      
      // Clear any WebSocket-related localStorage
      Object.keys(localStorage).forEach(key => {
        if (key.includes('websocket') || key.includes('realtime') || key.includes('supabase-realtime')) {
          localStorage.removeItem(key);
        }
      });

      // Clear sessionStorage
      Object.keys(sessionStorage).forEach(key => {
        if (key.includes('websocket') || key.includes('realtime')) {
          sessionStorage.removeItem(key);
        }
      });

      console.log('✅ Connection cache cleared');
    } catch (error) {
      console.warn('⚠️ Cache clearing failed:', error);
    }
  }

  /**
   * Test basic connectivity to Supabase
   */
  private async testConnectivity(): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🔍 Testing Supabase connectivity...');
      
      // Simple query to test connection
      const { data, error } = await supabase
        .from('profiles')
        .select('id')
        .limit(1);

      if (error) {
        console.warn('⚠️ Connectivity test failed:', error.message);
        return { success: false, error: error.message };
      }

      console.log('✅ Supabase connectivity confirmed');
      return { success: true };
    } catch (error: any) {
      console.warn('⚠️ Connectivity test error:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Initialize minimal real-time features
   */
  private async initializeMinimalRealtime(): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🔄 Initializing minimal real-time features...');
      
      // Check if user is authenticated
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        console.log('ℹ️ No active session, skipping real-time initialization');
        return { success: true };
      }

      // Try to create a simple channel for testing
      const testChannel = supabase.channel('connection-test', {
        config: {
          broadcast: { self: true },
          presence: { key: 'test' }
        }
      });

      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          console.warn('⚠️ Real-time initialization timeout');
          testChannel.unsubscribe();
          resolve({ success: false, error: 'Timeout' });
        }, 10000);

        testChannel
          .on('broadcast', { event: 'test' }, () => {
            console.log('✅ Real-time test successful');
          })
          .subscribe((status) => {
            console.log('📡 Test channel status:', status);
            
            if (status === 'SUBSCRIBED') {
              clearTimeout(timeout);
              testChannel.unsubscribe();
              console.log('✅ Minimal real-time features initialized');
              resolve({ success: true });
            } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
              clearTimeout(timeout);
              testChannel.unsubscribe();
              console.warn('⚠️ Real-time initialization failed:', status);
              resolve({ success: false, error: status });
            }
          });
      });
    } catch (error: any) {
      console.warn('⚠️ Real-time initialization error:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Handle Cloudflare cookie issues
   */
  async fixCloudflareIssues(): Promise<{ success: boolean; message: string }> {
    try {
      console.log('☁️ Fixing Cloudflare cookie issues...');
      
      // Clear any Cloudflare-related cookies
      document.cookie.split(";").forEach(cookie => {
        const eqPos = cookie.indexOf("=");
        const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
        if (name.includes('__cf_') || name.includes('cf_')) {
          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.supabase.co`;
          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
        }
      });

      // Force a new WebSocket connection
      await this.fixWebSocketConnections();

      console.log('✅ Cloudflare issues addressed');
      return { success: true, message: 'Cloudflare cookie issues fixed' };
    } catch (error: any) {
      console.error('❌ Cloudflare fix failed:', error);
      return { success: false, message: `Cloudflare fix failed: ${error.message}` };
    }
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): {
    attempts: number;
    maxAttempts: number;
    isFixing: boolean;
    canRetry: boolean;
  } {
    return {
      attempts: this.connectionAttempts,
      maxAttempts: this.maxAttempts,
      isFixing: this.isFixing,
      canRetry: this.connectionAttempts < this.maxAttempts
    };
  }

  /**
   * Reset connection attempts
   */
  resetAttempts(): void {
    this.connectionAttempts = 0;
    console.log('🔄 Connection attempts reset');
  }

  /**
   * Disable real-time features gracefully
   */
  async disableRealTimeFeatures(): Promise<void> {
    try {
      console.log('🔇 Disabling real-time features...');
      
      // Disconnect all channels
      await this.disconnectAllChannels();
      
      // Set a flag to indicate real-time is disabled
      localStorage.setItem('realtime-disabled', 'true');
      
      console.log('✅ Real-time features disabled gracefully');
    } catch (error) {
      console.warn('⚠️ Failed to disable real-time features:', error);
    }
  }

  /**
   * Check if real-time features are disabled
   */
  isRealTimeDisabled(): boolean {
    return localStorage.getItem('realtime-disabled') === 'true';
  }

  /**
   * Re-enable real-time features
   */
  async enableRealTimeFeatures(): Promise<void> {
    try {
      console.log('🔊 Re-enabling real-time features...');
      
      localStorage.removeItem('realtime-disabled');
      await this.fixWebSocketConnections();
      
      console.log('✅ Real-time features re-enabled');
    } catch (error) {
      console.warn('⚠️ Failed to re-enable real-time features:', error);
    }
  }
}

// Global instance
export const webSocketConnectionFixer = WebSocketConnectionFixer.getInstance();

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).webSocketConnectionFixer = webSocketConnectionFixer;
  (window as any).fixWebSocketConnections = () => webSocketConnectionFixer.fixWebSocketConnections();
  (window as any).fixCloudflareIssues = () => webSocketConnectionFixer.fixCloudflareIssues();
  (window as any).disableRealTime = () => webSocketConnectionFixer.disableRealTimeFeatures();
  (window as any).enableRealTime = () => webSocketConnectionFixer.enableRealTimeFeatures();
  
  console.log('🔌 WebSocket Connection Fixer loaded. Available commands:');
  console.log('  - fixWebSocketConnections()');
  console.log('  - fixCloudflareIssues()');
  console.log('  - disableRealTime()');
  console.log('  - enableRealTime()');
}
