-- Fix time_logs table column names to match application expectations
-- This migration renames clock_in_time/clock_out_time to clock_in/clock_out

-- Check if time_logs table exists and has the wrong column names
DO $$
BEGIN
    -- Check if the table exists with old column names
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'time_logs' 
        AND column_name = 'clock_in_time'
    ) THEN
        -- Rename columns to match application expectations
        ALTER TABLE public.time_logs RENAME COLUMN clock_in_time TO clock_in;
        ALTER TABLE public.time_logs RENAME COLUMN clock_out_time TO clock_out;
        
        -- Update any indexes that reference the old column names
        DROP INDEX IF EXISTS idx_time_logs_clock_in_time;
        CREATE INDEX IF NOT EXISTS idx_time_logs_clock_in ON public.time_logs(clock_in);
        
        RAISE NOTICE 'Renamed time_logs columns: clock_in_time -> clock_in, clock_out_time -> clock_out';
    ELSE
        RAISE NOTICE 'time_logs table already has correct column names or does not exist';
    END IF;
END
$$;

-- Insert a comment to track this migration (only if table exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'time_logs') THEN
        COMMENT ON TABLE public.time_logs IS 'Time tracking logs table - column names fixed by migration 20250805_fix_time_logs_columns';
    END IF;
END
$$;