/**
 * Comprehensive Toast and Error Handling System
 * Ensures all user actions provide proper feedback through toast notifications
 */

import { toast } from 'sonner';

export interface ToastOptions {
  title?: string;
  description?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export interface ErrorToastOptions extends ToastOptions {
  error?: Error | string;
  context?: string;
  showRetry?: boolean;
  onRetry?: () => void;
}

export class ComprehensiveToastSystem {
  private static instance: ComprehensiveToastSystem;
  private isInitialized = false;

  static getInstance(): ComprehensiveToastSystem {
    if (!ComprehensiveToastSystem.instance) {
      ComprehensiveToastSystem.instance = new ComprehensiveToastSystem();
    }
    return ComprehensiveToastSystem.instance;
  }

  /**
   * Initialize the comprehensive toast system
   */
  initialize(): void {
    if (this.isInitialized) return;

    console.log('🍞 Initializing comprehensive toast system...');

    // Override global error handlers to show toasts
    this.setupGlobalErrorHandling();
    
    // Setup form submission monitoring
    this.setupFormSubmissionMonitoring();
    
    // Setup API call monitoring
    this.setupAPICallMonitoring();
    
    // Setup authentication monitoring
    this.setupAuthenticationMonitoring();

    this.isInitialized = true;
    console.log('✅ Comprehensive toast system initialized');
  }

  /**
   * Show success toast
   */
  success(options: ToastOptions | string): void {
    if (typeof options === 'string') {
      toast.success(options);
      console.log('✅ Success toast:', options);
      return;
    }

    toast.success(options.title || 'Success', {
      description: options.description,
      duration: options.duration || 4000,
      action: options.action
    });

    console.log('✅ Success toast:', options.title, options.description);
  }

  /**
   * Show error toast
   */
  error(options: ErrorToastOptions | string): void {
    if (typeof options === 'string') {
      toast.error(options);
      console.log('❌ Error toast:', options);
      return;
    }

    const errorMessage = this.extractErrorMessage(options.error);
    const title = options.title || 'Error';
    const description = options.description || errorMessage;

    toast.error(title, {
      description,
      duration: options.duration || 6000,
      action: options.showRetry && options.onRetry ? {
        label: 'Retry',
        onClick: options.onRetry
      } : options.action
    });

    console.log('❌ Error toast:', title, description, options.context);
  }

  /**
   * Show warning toast
   */
  warning(options: ToastOptions | string): void {
    if (typeof options === 'string') {
      toast.warning(options);
      console.log('⚠️ Warning toast:', options);
      return;
    }

    toast.warning(options.title || 'Warning', {
      description: options.description,
      duration: options.duration || 5000,
      action: options.action
    });

    console.log('⚠️ Warning toast:', options.title, options.description);
  }

  /**
   * Show info toast
   */
  info(options: ToastOptions | string): void {
    if (typeof options === 'string') {
      toast.info(options);
      console.log('ℹ️ Info toast:', options);
      return;
    }

    toast.info(options.title || 'Info', {
      description: options.description,
      duration: options.duration || 4000,
      action: options.action
    });

    console.log('ℹ️ Info toast:', options.title, options.description);
  }

  /**
   * Show loading toast
   */
  loading(message: string, promise?: Promise<any>): string | Promise<any> {
    console.log('⏳ Loading toast:', message);
    
    if (promise) {
      return toast.promise(promise, {
        loading: message,
        success: 'Operation completed successfully',
        error: 'Operation failed'
      });
    }

    return toast.loading(message);
  }

  /**
   * Extract error message from various error types
   */
  private extractErrorMessage(error?: Error | string): string {
    if (!error) return 'An unknown error occurred';
    
    if (typeof error === 'string') return error;
    
    if (error instanceof Error) {
      // Handle specific error types
      if (error.message.includes('fetch')) {
        return 'Network error. Please check your connection and try again.';
      }
      
      if (error.message.includes('401') || error.message.includes('unauthorized')) {
        return 'Authentication failed. Please log in again.';
      }
      
      if (error.message.includes('403') || error.message.includes('forbidden')) {
        return 'You do not have permission to perform this action.';
      }
      
      if (error.message.includes('404') || error.message.includes('not found')) {
        return 'The requested resource was not found.';
      }
      
      if (error.message.includes('500') || error.message.includes('server')) {
        return 'Server error. Please try again later.';
      }
      
      return error.message;
    }
    
    return 'An unexpected error occurred';
  }

  /**
   * Setup global error handling
   */
  private setupGlobalErrorHandling(): void {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      console.log('🍞 Unhandled promise rejection caught:', event.reason);
      
      // Don't show toast for auth-related errors that are already handled
      const reason = event.reason?.message || event.reason;
      if (typeof reason === 'string' && (
        reason.includes('Invalid Refresh Token') ||
        reason.includes('AuthApiError') ||
        reason.includes('auth/v1/token')
      )) {
        return;
      }

      this.error({
        title: 'Unexpected Error',
        error: event.reason,
        context: 'unhandled-promise-rejection'
      });
    });

    // Handle global errors
    window.addEventListener('error', (event) => {
      console.log('🍞 Global error caught:', event.error);
      
      // Don't show toast for script loading errors
      if (event.filename && event.filename.includes('.js')) {
        return;
      }

      this.error({
        title: 'Application Error',
        error: event.error,
        context: 'global-error'
      });
    });
  }

  /**
   * Setup form submission monitoring
   */
  private setupFormSubmissionMonitoring(): void {
    document.addEventListener('submit', (event) => {
      const form = event.target as HTMLFormElement;
      const formName = form.getAttribute('name') || form.id || 'form';
      
      console.log('🍞 Form submission detected:', formName);

      // Add a small delay to allow form validation to complete
      setTimeout(() => {
        // Check if form submission was prevented (validation failed)
        if (event.defaultPrevented) {
          console.log('🍞 Form submission prevented (validation failed)');
          this.error({
            title: 'Form Validation Failed',
            description: 'Please check the form for errors and try again.',
            context: `form-validation-${formName}`
          });
        }
      }, 100);
    });

    // Monitor form input changes for real-time feedback
    document.addEventListener('input', (event) => {
      const input = event.target as HTMLInputElement;
      
      if (input.hasAttribute('required') && input.value.trim() && input.classList.contains('border-red-500')) {
        // Field was previously invalid but now has value
        input.classList.remove('border-red-500');
        console.log('🍞 Field validation cleared:', input.name || input.id);
      }
    });
  }

  /**
   * Setup API call monitoring
   */
  private setupAPICallMonitoring(): void {
    // DISABLED: Fetch override was causing cascading errors and network failures
    // const originalFetch = window.fetch;
    
    // window.fetch = async function(input, init) {
    //   const url = typeof input === 'string' ? input : input.url;
    //   const method = init?.method || 'GET';
    //   
    //   console.log('🍞 API call monitored:', method, url);

    //   try {
    //     const response = await originalFetch.call(this, input, init);
    //     
    //     // Handle non-ok responses
    //     if (!response.ok) {
    //       console.log('🍞 API call failed:', response.status, response.statusText);
    //       
    //       // Don't show toast for certain endpoints that handle their own errors
    //       if (url.includes('/auth/') || url.includes('/profiles') || url.includes('/user_presence')) {
    //         return response;
    //       }

    //       const errorMessage = `${method} request failed: ${response.status} ${response.statusText}`;
    //       
    //       ComprehensiveToastSystem.getInstance().error({
    //         title: 'Request Failed',
    //         description: errorMessage,
    //         context: `api-${method}-${response.status}`,
    //         showRetry: true,
    //         onRetry: () => {
    //           console.log('🍞 Retrying API call:', method, url);
    //           window.location.reload();
    //         }
    //       });
    //     }
    //     
    //     return response;
    //   } catch (error) {
    //     console.log('🍞 API call error:', error);
    //     
    //     ComprehensiveToastSystem.getInstance().error({
    //       title: 'Network Error',
    //       error: error as Error,
    //       context: `api-${method}-network`,
    //       showRetry: true,
    //       onRetry: () => {
    //         console.log('🍞 Retrying after network error');
    //         window.location.reload();
    //       }
    //     });
    //     
    //     throw error;
    //   }
    // };
    
    // Note: API monitoring should be handled via interceptors or event listeners instead
    console.log('🍞 API call monitoring disabled to prevent fetch override conflicts');
  }

  /**
   * Setup authentication monitoring
   */
  private setupAuthenticationMonitoring(): void {
    // Monitor authentication state changes
    document.addEventListener('auth-state-change', (event: any) => {
      const { type, user, error } = event.detail || {};
      
      console.log('🍞 Auth state change:', type, user?.email, error?.message);

      switch (type) {
        case 'SIGNED_IN':
          this.success({
            title: 'Welcome back!',
            description: `Successfully signed in as ${user?.email}`,
            duration: 3000
          });
          break;
          
        case 'SIGNED_OUT':
          this.info({
            title: 'Signed out',
            description: 'You have been successfully signed out',
            duration: 3000
          });
          break;
          
        case 'SIGN_IN_ERROR':
          this.error({
            title: 'Sign In Failed',
            error: error,
            context: 'authentication',
            showRetry: true,
            onRetry: () => {
              window.location.href = '/auth';
            }
          });
          break;
          
        case 'SIGN_UP_ERROR':
          this.error({
            title: 'Sign Up Failed',
            error: error,
            context: 'authentication'
          });
          break;
      }
    });
  }

  /**
   * Show toast for specific actions
   */
  showActionToast(action: string, success: boolean, details?: string, error?: Error): void {
    const actionMessages = {
      'clock-in': {
        success: { title: '✅ Clocked In', description: 'Successfully clocked in for today' },
        error: { title: '❌ Clock In Failed', description: 'Failed to clock in. Please try again.' }
      },
      'clock-out': {
        success: { title: '✅ Clocked Out', description: 'Successfully clocked out for today' },
        error: { title: '❌ Clock Out Failed', description: 'Failed to clock out. Please try again.' }
      },
      'report-submit': {
        success: { title: '✅ Report Submitted', description: 'Your report has been submitted successfully' },
        error: { title: '❌ Report Submission Failed', description: 'Failed to submit report. Please try again.' }
      },
      'login': {
        success: { title: '✅ Login Successful', description: 'Welcome back!' },
        error: { title: '❌ Login Failed', description: 'Invalid credentials. Please try again.' }
      },
      'signup': {
        success: { title: '✅ Account Created', description: 'Your account has been created successfully' },
        error: { title: '❌ Sign Up Failed', description: 'Failed to create account. Please try again.' }
      },
      'ai-query': {
        success: { title: '✅ AI Response', description: 'AI query completed successfully' },
        error: { title: '❌ AI Query Failed', description: 'Failed to process AI query. Please try again.' }
      }
    };

    const messages = actionMessages[action as keyof typeof actionMessages];
    if (!messages) {
      console.warn('🍞 Unknown action for toast:', action);
      return;
    }

    if (success) {
      this.success({
        title: messages.success.title,
        description: details || messages.success.description
      });
    } else {
      this.error({
        title: messages.error.title,
        description: details || messages.error.description,
        error: error,
        context: action,
        showRetry: true,
        onRetry: () => {
          console.log(`🍞 Retrying action: ${action}`);
          // The specific retry logic should be handled by the calling component
        }
      });
    }
  }
}

// Export singleton instance
export const comprehensiveToastSystem = ComprehensiveToastSystem.getInstance();

// Export utility functions
export const showSuccessToast = (options: ToastOptions | string) => comprehensiveToastSystem.success(options);
export const showErrorToast = (options: ErrorToastOptions | string) => comprehensiveToastSystem.error(options);
export const showWarningToast = (options: ToastOptions | string) => comprehensiveToastSystem.warning(options);
export const showInfoToast = (options: ToastOptions | string) => comprehensiveToastSystem.info(options);
export const showLoadingToast = (message: string, promise?: Promise<any>) => comprehensiveToastSystem.loading(message, promise);
export const showActionToast = (action: string, success: boolean, details?: string, error?: Error) => 
  comprehensiveToastSystem.showActionToast(action, success, details, error);

// Auto-initialize when module loads
if (typeof window !== 'undefined') {
  setTimeout(() => {
    comprehensiveToastSystem.initialize();
  }, 1000);
}
