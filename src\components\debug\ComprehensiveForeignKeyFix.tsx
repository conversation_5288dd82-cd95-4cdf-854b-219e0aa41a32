import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Loader2, CheckCircle, XCircle, Database, RefreshCw } from 'lucide-react';
import { comprehensiveForeignKeyFixer } from '@/utils/comprehensive-foreign-key-fixer';

interface ConstraintInfo {
  table_name: string;
  constraint_name: string;
  column_name: string;
  foreign_table_name: string;
  foreign_column_name: string;
  constraint_type: string;
}

export function ComprehensiveForeignKeyFix() {
  const [isFixing, setIsFixing] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [fixResult, setFixResult] = useState<{
    success: boolean;
    message: string;
    details: string[];
  } | null>(null);
  const [constraints, setConstraints] = useState<ConstraintInfo[]>([]);
  const [constraintsChecked, setConstraintsChecked] = useState(false);

  const handleCheckConstraints = async () => {
    setIsChecking(true);
    try {
      const result = await comprehensiveForeignKeyFixer.checkAllConstraints();
      if (result.success) {
        setConstraints(result.constraints);
        setConstraintsChecked(true);
      } else {
        setFixResult({
          success: false,
          message: result.message,
          details: []
        });
      }
    } catch (error) {
      setFixResult({
        success: false,
        message: `Error checking constraints: ${error}`,
        details: []
      });
    } finally {
      setIsChecking(false);
    }
  };

  const handleFixForeignKeys = async () => {
    setIsFixing(true);
    setFixResult(null);
    
    try {
      const result = await comprehensiveForeignKeyFixer.fixAllForeignKeys();
      setFixResult(result);
      
      // Refresh constraints after fixing
      if (result.success) {
        await handleCheckConstraints();
      }
    } catch (error) {
      setFixResult({
        success: false,
        message: `Error fixing foreign keys: ${error}`,
        details: []
      });
    } finally {
      setIsFixing(false);
    }
  };

  const getConstraintStatus = (constraint: ConstraintInfo) => {
    const isAuthUsers = constraint.foreign_table_name === 'users';
    const shouldBeProfiles = constraint.table_name !== 'profiles' || constraint.column_name !== 'user_id';
    
    if (isAuthUsers && shouldBeProfiles) {
      return { status: 'error', label: 'Needs Fix', color: 'destructive' };
    } else if (isAuthUsers && !shouldBeProfiles) {
      return { status: 'correct', label: 'Correct', color: 'default' };
    } else {
      return { status: 'fixed', label: 'Fixed', color: 'secondary' };
    }
  };

  const problemConstraints = constraints.filter(c => {
    const status = getConstraintStatus(c);
    return status.status === 'error';
  });

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Comprehensive Foreign Key Fix
        </CardTitle>
        <CardDescription>
          Fix all foreign key relationships that incorrectly reference auth.users instead of public.profiles
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button
            onClick={handleCheckConstraints}
            disabled={isChecking || isFixing}
            variant="outline"
          >
            {isChecking ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Checking...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Check Constraints
              </>
            )}
          </Button>
          
          <Button
            onClick={handleFixForeignKeys}
            disabled={isFixing || isChecking}
            variant={problemConstraints.length > 0 ? "destructive" : "default"}
          >
            {isFixing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Fixing...
              </>
            ) : (
              <>
                <Database className="mr-2 h-4 w-4" />
                Fix All Foreign Keys
              </>
            )}
          </Button>
        </div>

        {constraintsChecked && (
          <div className="space-y-2">
            <h4 className="font-semibold">Current Foreign Key Constraints:</h4>
            <div className="text-sm text-muted-foreground mb-2">
              Total: {constraints.length} | Problems: {problemConstraints.length}
            </div>
            <div className="max-h-60 overflow-y-auto space-y-1">
              {constraints.map((constraint, index) => {
                const status = getConstraintStatus(constraint);
                return (
                  <div key={index} className="flex items-center justify-between p-2 border rounded">
                    <div className="flex-1">
                      <span className="font-mono text-sm">
                        {constraint.table_name}.{constraint.column_name}
                      </span>
                      <span className="text-muted-foreground mx-2">→</span>
                      <span className="font-mono text-sm">
                        {constraint.foreign_table_name}.{constraint.foreign_column_name}
                      </span>
                    </div>
                    <Badge variant={status.color as any}>
                      {status.label}
                    </Badge>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {fixResult && (
          <Alert className={fixResult.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
            <div className="flex items-center gap-2">
              {fixResult.success ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <XCircle className="h-4 w-4 text-red-600" />
              )}
              <AlertDescription className="font-medium">
                {fixResult.message}
              </AlertDescription>
            </div>
            {fixResult.details.length > 0 && (
              <div className="mt-2 max-h-40 overflow-y-auto">
                <div className="text-sm space-y-1">
                  {fixResult.details.map((detail, index) => (
                    <div key={index} className="font-mono text-xs">
                      {detail}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </Alert>
        )}

        <div className="text-sm text-muted-foreground">
          <p><strong>What this fixes:</strong></p>
          <ul className="list-disc list-inside space-y-1 mt-1">
            <li>Updates all foreign keys to reference public.profiles instead of auth.users</li>
            <li>Preserves profiles.user_id → auth.users relationship (correct)</li>
            <li>Fixes 400 errors when querying related data</li>
            <li>Ensures proper data relationships across all tables</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}