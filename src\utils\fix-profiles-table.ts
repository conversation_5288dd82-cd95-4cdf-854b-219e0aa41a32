/**
 * Fix Profiles Table
 * Utility to fix the profiles table columns that are causing 400 errors
 */

import { supabase } from '@/integrations/supabase/client';

export interface ProfilesFixResult {
  success: boolean;
  message: string;
  details?: string[];
}

export class ProfilesTableFixer {
  private static instance: ProfilesTableFixer;
  private isFixed = false;

  static getInstance(): ProfilesTableFixer {
    if (!ProfilesTableFixer.instance) {
      ProfilesTableFixer.instance = new ProfilesTableFixer();
    }
    return ProfilesTableFixer.instance;
  }

  /**
   * Check if profiles table has required columns
   */
  async checkProfilesColumns(): Promise<{ position: boolean; hire_date: boolean; location: boolean }> {
    try {
      const results = {
        position: false,
        hire_date: false,
        location: false
      };

      // Test position column
      try {
        const { error: positionError } = await supabase
          .from('profiles')
          .select('position')
          .limit(1);
        results.position = !positionError;
      } catch (error) {
        console.log('❌ position column check failed');
      }

      // Test hire_date column
      try {
        const { error: hireDateError } = await supabase
          .from('profiles')
          .select('hire_date')
          .limit(1);
        results.hire_date = !hireDateError;
      } catch (error) {
        console.log('❌ hire_date column check failed');
      }

      // Test location column
      try {
        const { error: locationError } = await supabase
          .from('profiles')
          .select('location')
          .limit(1);
        results.location = !locationError;
      } catch (error) {
        console.log('❌ location column check failed');
      }

      console.log('✅ Profiles columns check:', results);
      return results;

    } catch (error) {
      console.log('❌ Profiles columns check error:', error);
      return { position: false, hire_date: false, location: false };
    }
  }

  /**
   * Add missing columns to profiles table
   */
  async addMissingColumns(): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🔧 Adding missing columns to profiles table...');

      // Add missing columns using RPC
      const { error } = await supabase.rpc('exec_sql', {
        sql: `
          -- Add position column if it doesn't exist
          DO $$ 
          BEGIN
              IF NOT EXISTS (
                  SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'profiles' 
                  AND column_name = 'position' 
                  AND table_schema = 'public'
              ) THEN
                  ALTER TABLE public.profiles ADD COLUMN position TEXT;
              END IF;
          END $$;

          -- Add hire_date column if it doesn't exist
          DO $$ 
          BEGIN
              IF NOT EXISTS (
                  SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'profiles' 
                  AND column_name = 'hire_date' 
                  AND table_schema = 'public'
              ) THEN
                  ALTER TABLE public.profiles ADD COLUMN hire_date DATE;
              END IF;
          END $$;

          -- Add location column if it doesn't exist
          DO $$ 
          BEGIN
              IF NOT EXISTS (
                  SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'profiles' 
                  AND column_name = 'location' 
                  AND table_schema = 'public'
              ) THEN
                  ALTER TABLE public.profiles ADD COLUMN location TEXT;
              END IF;
          END $$;

          -- Add department_id column if it doesn't exist
          DO $$ 
          BEGIN
              IF NOT EXISTS (
                  SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'profiles' 
                  AND column_name = 'department_id' 
                  AND table_schema = 'public'
              ) THEN
                  ALTER TABLE public.profiles ADD COLUMN department_id UUID;
              END IF;
          END $$;

          -- Add employment_status column if it doesn't exist
          DO $$ 
          BEGIN
              IF NOT EXISTS (
                  SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'profiles' 
                  AND column_name = 'employment_status' 
                  AND table_schema = 'public'
              ) THEN
                  ALTER TABLE public.profiles ADD COLUMN employment_status TEXT DEFAULT 'active';
              END IF;
          END $$;

          -- Add employee_id column if it doesn't exist
          DO $$ 
          BEGIN
              IF NOT EXISTS (
                  SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'profiles' 
                  AND column_name = 'employee_id' 
                  AND table_schema = 'public'
              ) THEN
                  ALTER TABLE public.profiles ADD COLUMN employee_id TEXT;
              END IF;
          END $$;
        `
      });

      if (error) {
        console.error('❌ Failed to add missing columns:', error.message);
        return { success: false, error: error.message };
      }

      console.log('✅ Missing columns added successfully');
      return { success: true };

    } catch (error: any) {
      console.error('❌ Error adding missing columns:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update existing profiles with sample data
   */
  async updateExistingProfiles(): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🔧 Updating existing profiles with sample data...');

      // Get all profiles that need updates
      const { data: profiles, error: fetchError } = await supabase
        .from('profiles')
        .select('id, role, created_at, position, hire_date, employment_status, employee_id')
        .or('position.is.null,hire_date.is.null,employment_status.is.null,employee_id.is.null');

      if (fetchError) {
        console.error('❌ Failed to fetch profiles:', fetchError.message);
        return { success: false, error: fetchError.message };
      }

      if (!profiles || profiles.length === 0) {
        console.log('✅ No profiles need updating');
        return { success: true };
      }

      // Update each profile
      for (const profile of profiles) {
        const updates: any = {};

        if (!profile.position) {
          updates.position = profile.role === 'admin' ? 'System Administrator' :
                           profile.role === 'manager' ? 'Department Manager' :
                           profile.role === 'staff' ? 'Staff Member' : 'Employee';
        }

        if (!profile.hire_date) {
          updates.hire_date = new Date(profile.created_at).toISOString().split('T')[0];
        }

        if (!profile.employment_status) {
          updates.employment_status = 'active';
        }

        if (!profile.employee_id) {
          const timestamp = new Date(profile.created_at).getTime();
          updates.employee_id = `EMP${timestamp.toString().slice(-8)}`;
        }

        if (Object.keys(updates).length > 0) {
          const { error: updateError } = await supabase
            .from('profiles')
            .update(updates)
            .eq('id', profile.id);

          if (updateError) {
            console.warn(`⚠️ Failed to update profile ${profile.id}:`, updateError.message);
          }
        }
      }

      console.log(`✅ Updated ${profiles.length} profiles with sample data`);
      return { success: true };

    } catch (error: any) {
      console.error('❌ Error updating profiles:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Fix all profiles-related issues
   */
  async fixAllIssues(): Promise<{ success: boolean; results: string[] }> {
    const results: string[] = [];

    try {
      console.log('🔧 Starting profiles table fixes...');

      // Step 1: Check current column status
      const columnStatus = await this.checkProfilesColumns();
      const missingColumns = Object.entries(columnStatus)
        .filter(([_, exists]) => !exists)
        .map(([column, _]) => column);

      if (missingColumns.length > 0) {
        results.push(`Missing columns: ${missingColumns.join(', ')}`);

        // Step 2: Add missing columns
        const addResult = await this.addMissingColumns();
        if (addResult.success) {
          results.push('Missing columns added successfully');
        } else {
          results.push(`Failed to add columns: ${addResult.error}`);
          return { success: false, results };
        }
      } else {
        results.push('All required columns exist');
      }

      // Step 3: Update existing profiles
      const updateResult = await this.updateExistingProfiles();
      if (updateResult.success) {
        results.push('Existing profiles updated with sample data');
      } else {
        results.push(`Failed to update profiles: ${updateResult.error}`);
      }

      // Step 4: Verify fixes
      const finalCheck = await this.checkProfilesColumns();
      const stillMissing = Object.entries(finalCheck)
        .filter(([_, exists]) => !exists)
        .map(([column, _]) => column);

      if (stillMissing.length === 0) {
        results.push('All column checks now pass');
        this.isFixed = true;
      } else {
        results.push(`Still missing: ${stillMissing.join(', ')}`);
      }

      console.log('✅ Profiles table fixes completed');
      return { success: stillMissing.length === 0, results };

    } catch (error: any) {
      console.error('❌ Error during profiles fixes:', error.message);
      results.push(`Error during fixes: ${error.message}`);
      return { success: false, results };
    }
  }

  /**
   * Get the current fix status
   */
  isTableFixed(): boolean {
    return this.isFixed;
  }

  /**
   * Reset the fix status (for testing)
   */
  resetFixStatus(): void {
    this.isFixed = false;
  }
}

// Export singleton instance
export const profilesTableFixer = ProfilesTableFixer.getInstance();

// Export utility functions
export const fixProfilesTable = () => profilesTableFixer.fixAllIssues();
export const checkProfilesColumns = () => profilesTableFixer.checkProfilesColumns();
