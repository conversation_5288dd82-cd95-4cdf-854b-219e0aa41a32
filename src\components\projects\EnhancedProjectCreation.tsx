/**
 * Enhanced Project Creation Component
 * Comprehensive project creation with AI assistance and team management
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { 
  Plus, 
  User, 
  Calendar, 
  DollarSign, 
  MapPin, 
  Flag, 
  Brain,
  Save,
  X,
  Users,
  Building2,
  Target,
  Clock
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ProjectFormData {
  name: string;
  description: string;
  client_name: string;
  budget: number;
  location: string;
  start_date: string;
  end_date: string;
  status: 'planning' | 'active' | 'on_hold' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  manager_id: string;
  department_id: string;
  tags: string[];
  objectives: string[];
  team_members: string[];
}

interface User {
  id: string;
  full_name: string;
  email: string;
  role: string;
  department_id?: string;
}

interface Department {
  id: string;
  name: string;
  manager_id?: string;
}

export const EnhancedProjectCreation: React.FC<{
  onProjectCreated?: (project: any) => void;
  onCancel?: () => void;
  initialData?: Partial<ProjectFormData>;
}> = ({ onProjectCreated, onCancel, initialData }) => {
  const [formData, setFormData] = useState<ProjectFormData>({
    name: '',
    description: '',
    client_name: '',
    budget: 0,
    location: '',
    start_date: '',
    end_date: '',
    status: 'planning',
    priority: 'medium',
    manager_id: '',
    department_id: '',
    tags: [],
    objectives: [],
    team_members: [],
    ...initialData
  });

  const [users, setUsers] = useState<User[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isAIAssisting, setIsAIAssisting] = useState(false);
  const [newTag, setNewTag] = useState('');
  const [newObjective, setNewObjective] = useState('');
  const { toast } = useToast();

  useEffect(() => {
    loadUsers();
    loadDepartments();
  }, []);

  const loadUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, email, role, department_id')
        .order('full_name');

      if (error) throw error;
      setUsers(data || []);
    } catch (error: any) {
      console.error('Error loading users:', error);
      toast({
        title: "Error",
        description: "Failed to load users",
        variant: "destructive",
      });
    }
  };

  const loadDepartments = async () => {
    try {
      const { data, error } = await supabase
        .from('departments')
        .select('id, name, manager_id')
        .order('name');

      if (error) {
        // If departments table doesn't exist, create mock data
        setDepartments([
          { id: '1', name: 'Engineering' },
          { id: '2', name: 'Marketing' },
          { id: '3', name: 'Sales' },
          { id: '4', name: 'Operations' }
        ]);
        return;
      }
      setDepartments(data || []);
    } catch (error: any) {
      console.error('Error loading departments:', error);
      // Fallback to mock data
      setDepartments([
        { id: '1', name: 'Engineering' },
        { id: '2', name: 'Marketing' },
        { id: '3', name: 'Sales' },
        { id: '4', name: 'Operations' }
      ]);
    }
  };

  const handleInputChange = (field: keyof ProjectFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const addObjective = () => {
    if (newObjective.trim() && !formData.objectives.includes(newObjective.trim())) {
      setFormData(prev => ({
        ...prev,
        objectives: [...prev.objectives, newObjective.trim()]
      }));
      setNewObjective('');
    }
  };

  const removeObjective = (objectiveToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      objectives: prev.objectives.filter(obj => obj !== objectiveToRemove)
    }));
  };

  const getAIAssistance = async () => {
    if (!formData.name.trim()) {
      toast({
        title: "Missing Information",
        description: "Please provide a project name first",
        variant: "destructive",
      });
      return;
    }

    setIsAIAssisting(true);
    try {
      const { data, error } = await supabase.functions.invoke('ai-assistant', {
        body: {
          message: `Help me create a detailed project plan for: "${formData.name}". ${formData.description ? `Additional context: ${formData.description}` : ''}`,
          context: {
            task: 'project_creation_assistance',
            userRole: 'project_manager'
          }
        }
      });

      if (error) throw error;

      // Parse AI response for suggestions
      const aiResponse = data.response;
      
      // Simple AI enhancement
      if (!formData.description) {
        setFormData(prev => ({
          ...prev,
          description: `AI-suggested description: ${aiResponse.substring(0, 300)}...`
        }));
      }

      // Suggest objectives based on project name
      const suggestedObjectives = [
        `Complete ${formData.name} within timeline`,
        `Deliver high-quality results`,
        `Maintain budget constraints`,
        `Ensure stakeholder satisfaction`
      ];
      
      setFormData(prev => ({
        ...prev,
        objectives: [...new Set([...prev.objectives, ...suggestedObjectives.slice(0, 2)])]
      }));

      toast({
        title: "AI Assistance",
        description: "Project details enhanced with AI suggestions",
      });
    } catch (error: any) {
      console.error('AI assistance error:', error);
      toast({
        title: "AI Assistance Unavailable",
        description: "AI features are currently unavailable",
        variant: "destructive",
      });
    } finally {
      setIsAIAssisting(false);
    }
  };

  const createProject = async () => {
    if (!formData.name.trim()) {
      toast({
        title: "Missing Information",
        description: "Project name is required",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        throw new Error('User not authenticated');
      }

      const projectData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        client_name: formData.client_name.trim() || null,
        budget: formData.budget || null,
        location: formData.location.trim() || null,
        start_date: formData.start_date || null,
        end_date: formData.end_date || null,
        status: formData.status,
        priority: formData.priority,
        manager_id: formData.manager_id || user.id,
        department_id: formData.department_id || null,
        created_by: user.id,
        metadata: {
          tags: formData.tags,
          objectives: formData.objectives,
          created_via: 'enhanced_project_creation'
        }
      };

      const { data, error } = await supabase
        .from('projects')
        .insert(projectData)
        .select()
        .single();

      if (error) throw error;

      // Create project assignments for team members
      if (formData.team_members.length > 0) {
        const assignments = formData.team_members.map(memberId => ({
          project_id: data.id,
          assigned_to: memberId,
          assigned_by: user.id,
          role: 'member',
          status: 'active'
        }));

        await supabase
          .from('project_assignments')
          .insert(assignments);
      }

      toast({
        title: "Success",
        description: "Project created successfully",
      });

      if (onProjectCreated) {
        onProjectCreated(data);
      }

      // Reset form
      setFormData({
        name: '',
        description: '',
        client_name: '',
        budget: 0,
        location: '',
        start_date: '',
        end_date: '',
        status: 'planning',
        priority: 'medium',
        manager_id: '',
        department_id: '',
        tags: [],
        objectives: [],
        team_members: []
      });

    } catch (error: any) {
      console.error('Error creating project:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create project",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-700 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-700 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-700 border-green-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  return (
    <Card className={cn(
      "border-[0.8px] border-[#ff1c04]/20",
      "bg-gradient-to-br from-[#1a1a1a] via-[#0f0f0f] to-[#1a1a1a]",
      "shadow-[8px_8px_16px_rgba(0,0,0,0.4),-8px_-8px_16px_rgba(255,28,4,0.1)]"
    )} data-component="EnhancedProjectCreation">
      <CardHeader>
        <CardTitle className="flex items-center gap-3 text-[#e5e5e5]">
          <div className="p-2 rounded-xl bg-gradient-to-br from-[#ff1c04]/10 to-[#ff1c04]/20 border border-[#ff1c04]/20">
            <Plus className="h-6 w-6 text-[#ff1c04]" />
          </div>
          <div>
            <h2 className="text-2xl font-bold">CREATE NEW PROJECT</h2>
            <p className="text-sm text-[#a5a5a5] font-normal">Enhanced project creation with AI assistance</p>
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <h3 className="text-lg font-semibold text-[#e5e5e5]">BASIC INFORMATION</h3>
            <Button
              onClick={getAIAssistance}
              disabled={isAIAssisting || !formData.name.trim()}
              variant="outline"
              size="sm"
              className="border-[#ff1c04]/30 text-[#ff1c04] hover:bg-[#ff1c04]/10"
            >
              <Brain className="h-4 w-4 mr-2" />
              {isAIAssisting ? 'AI Assisting...' : 'AI Assist'}
            </Button>
          </div>

          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-[#e5e5e5]">Project Name *</label>
              <Input
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Enter project name..."
                className="bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-[#e5e5e5]">Client Name</label>
              <Input
                value={formData.client_name}
                onChange={(e) => handleInputChange('client_name', e.target.value)}
                placeholder="Enter client name..."
                className="bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]"
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-[#e5e5e5]">Description</label>
            <Textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Describe the project in detail..."
              className="min-h-[100px] bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]"
            />
          </div>
        </div>

        {/* Project Details */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-[#e5e5e5]">PROJECT DETAILS</h3>
          
          <div className="grid md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-[#e5e5e5]">Priority</label>
              <Select value={formData.priority} onValueChange={(value) => handleInputChange('priority', value)}>
                <SelectTrigger className="bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="urgent">Urgent</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-[#e5e5e5]">Status</label>
              <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                <SelectTrigger className="bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="planning">Planning</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="on_hold">On Hold</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-[#e5e5e5]">Budget</label>
              <Input
                type="number"
                value={formData.budget}
                onChange={(e) => handleInputChange('budget', parseFloat(e.target.value) || 0)}
                placeholder="0"
                min="0"
                className="bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]"
              />
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-[#e5e5e5]">Location</label>
              <Input
                value={formData.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                placeholder="Project location..."
                className="bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-[#e5e5e5]">Department</label>
              <Select value={formData.department_id} onValueChange={(value) => handleInputChange('department_id', value)}>
                <SelectTrigger className="bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]">
                  <SelectValue placeholder="Select department..." />
                </SelectTrigger>
                <SelectContent>
                  {departments.map(dept => (
                    <SelectItem key={dept.id} value={dept.id}>
                      {dept.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Timeline */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-[#e5e5e5]">TIMELINE</h3>
          
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-[#e5e5e5]">Start Date</label>
              <Input
                type="date"
                value={formData.start_date}
                onChange={(e) => handleInputChange('start_date', e.target.value)}
                className="bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-[#e5e5e5]">End Date</label>
              <Input
                type="date"
                value={formData.end_date}
                onChange={(e) => handleInputChange('end_date', e.target.value)}
                className="bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]"
              />
            </div>
          </div>
        </div>

        {/* Management */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-[#e5e5e5]">MANAGEMENT</h3>
          
          <div className="space-y-2">
            <label className="text-sm font-medium text-[#e5e5e5]">Project Manager</label>
            <Select value={formData.manager_id} onValueChange={(value) => handleInputChange('manager_id', value)}>
              <SelectTrigger className="bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]">
                <SelectValue placeholder="Select project manager..." />
              </SelectTrigger>
              <SelectContent>
                {users.filter(user => ['admin', 'manager', 'project_manager'].includes(user.role)).map(user => (
                  <SelectItem key={user.id} value={user.id}>
                    {user.full_name} ({user.role})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Tags */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-[#e5e5e5]">TAGS</h3>
          
          <div className="flex gap-2">
            <Input
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              placeholder="Add tag..."
              className="bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]"
              onKeyPress={(e) => e.key === 'Enter' && addTag()}
            />
            <Button onClick={addTag} variant="outline" className="border-[#ff1c04]/30 text-[#ff1c04]">
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          {formData.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {formData.tags.map(tag => (
                <Badge key={tag} variant="outline" className="border-[#ff1c04]/30 text-[#ff1c04]">
                  {tag}
                  <X 
                    className="h-3 w-3 ml-1 cursor-pointer" 
                    onClick={() => removeTag(tag)}
                  />
                </Badge>
              ))}
            </div>
          )}
        </div>

        {/* Objectives */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-[#e5e5e5]">OBJECTIVES</h3>
          
          <div className="flex gap-2">
            <Input
              value={newObjective}
              onChange={(e) => setNewObjective(e.target.value)}
              placeholder="Add objective..."
              className="bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]"
              onKeyPress={(e) => e.key === 'Enter' && addObjective()}
            />
            <Button onClick={addObjective} variant="outline" className="border-[#ff1c04]/30 text-[#ff1c04]">
              <Target className="h-4 w-4" />
            </Button>
          </div>

          {formData.objectives.length > 0 && (
            <div className="space-y-2">
              {formData.objectives.map((objective, index) => (
                <div key={index} className="flex items-center gap-2 p-2 bg-[#1a1a1a] rounded border border-[#ff1c04]/20">
                  <span className="text-[#e5e5e5] flex-1">{objective}</span>
                  <X 
                    className="h-4 w-4 text-[#ff1c04] cursor-pointer" 
                    onClick={() => removeObjective(objective)}
                  />
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex gap-4 pt-4">
          <Button
            onClick={createProject}
            disabled={isLoading || !formData.name.trim()}
            className="bg-[#ff1c04] hover:bg-[#ff1c04]/90 text-white"
          >
            <Save className="h-4 w-4 mr-2" />
            {isLoading ? 'Creating...' : 'Create Project'}
          </Button>

          {onCancel && (
            <Button
              onClick={onCancel}
              variant="outline"
              className="border-[#ff1c04]/30 text-[#ff1c04] hover:bg-[#ff1c04]/10"
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
