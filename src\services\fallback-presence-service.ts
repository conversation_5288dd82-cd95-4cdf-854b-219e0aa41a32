/**
 * Fallback Presence Service
 * Simple presence tracking without database dependencies
 */

import { supabase } from '@/integrations/supabase/client';

interface UserPresence {
  userId: string;
  userName: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  lastSeen: string;
  location?: string;
  device?: string;
}

export class FallbackPresenceService {
  private static instance: FallbackPresenceService;
  private presenceChannel: any = null;
  private currentPresence: UserPresence | null = null;
  private isActive = false;
  private heartbeatInterval?: NodeJS.Timeout;

  static getInstance(): FallbackPresenceService {
    if (!FallbackPresenceService.instance) {
      FallbackPresenceService.instance = new FallbackPresenceService();
    }
    return FallbackPresenceService.instance;
  }

  /**
   * Initialize presence tracking without database
   */
  async initialize(): Promise<{ success: boolean; message: string }> {
    try {
      console.log('👥 Initializing fallback presence service...');

      // Get current user
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        console.log('ℹ️ No authenticated user, skipping presence');
        return { success: true, message: 'No user - presence skipped' };
      }

      // Get user profile for display name (with fallback)
      let userName = 'Unknown User';
      try {
        const { data: profile } = await supabase
          .from('profiles')
          .select('full_name, email')
          .eq('user_id', user.id)
          .single();

        if (profile) {
          userName = profile.full_name || profile.email || user.email || 'Unknown User';
        }
      } catch (error) {
        console.warn('Could not fetch user profile, using email');
        userName = user.email || 'Unknown User';
      }

      // Set up presence state
      this.currentPresence = {
        userId: user.id,
        userName: userName,
        status: 'online',
        lastSeen: new Date().toISOString(),
        location: 'Web App',
        device: this.getDeviceInfo()
      };

      // Create presence channel using only broadcast (no database)
      this.presenceChannel = supabase.channel('fallback-presence', {
        config: {
          broadcast: { self: true, ack: false },
          presence: { key: user.id }
        }
      });

      // Set up presence event handlers
      this.presenceChannel
        .on('broadcast', { event: 'presence-update' }, (payload: any) => {
          console.log('👥 Presence update received:', payload);
          this.handlePresenceUpdate(payload);
        })
        .on('presence', { event: 'sync' }, () => {
          console.log('👥 Presence sync');
          const presenceState = this.presenceChannel.presenceState();
          this.handlePresenceSync(presenceState);
        })
        .on('presence', { event: 'join' }, ({ key, newPresences }: any) => {
          console.log('👋 User joined:', key);
          this.handlePresenceJoin(key, newPresences);
        })
        .on('presence', { event: 'leave' }, ({ key, leftPresences }: any) => {
          console.log('👋 User left:', key);
          this.handlePresenceLeave(key, leftPresences);
        })
        .subscribe(async (status) => {
          console.log('📡 Fallback presence channel status:', status);
          
          if (status === 'SUBSCRIBED') {
            // Track presence using the presence feature only
            await this.presenceChannel.track(this.currentPresence);
            
            // Also broadcast initial presence
            await this.broadcastPresence();
            
            this.isActive = true;
            this.startHeartbeat();
            console.log('✅ Fallback presence tracking started');
          } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
            console.warn('⚠️ Fallback presence channel failed:', status);
            this.isActive = false;
          }
        });

      // Handle page visibility changes
      document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
          this.updatePresence('away');
        } else {
          this.updatePresence('online');
        }
      });

      // Handle page unload
      window.addEventListener('beforeunload', () => {
        this.updatePresence('offline');
      });

      return { success: true, message: 'Fallback presence initialized' };
    } catch (error: any) {
      console.error('❌ Fallback presence initialization failed:', error);
      return { success: false, message: `Initialization failed: ${error.message}` };
    }
  }

  /**
   * Start heartbeat to maintain presence
   */
  private startHeartbeat(): void {
    // Clear existing heartbeat
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    // Send heartbeat every 30 seconds
    this.heartbeatInterval = setInterval(() => {
      if (this.isActive && this.currentPresence) {
        this.broadcastPresence();
      }
    }, 30000);
  }

  /**
   * Broadcast presence update (no database)
   */
  private async broadcastPresence(): Promise<void> {
    if (!this.presenceChannel || !this.currentPresence) return;

    try {
      this.currentPresence.lastSeen = new Date().toISOString();
      
      await this.presenceChannel.send({
        type: 'broadcast',
        event: 'presence-update',
        payload: this.currentPresence
      });

      console.log('📡 Fallback presence broadcasted');
    } catch (error) {
      console.warn('⚠️ Failed to broadcast fallback presence:', error);
    }
  }

  /**
   * Update user presence status
   */
  async updatePresence(status: 'online' | 'away' | 'busy' | 'offline'): Promise<void> {
    if (!this.currentPresence) return;

    try {
      this.currentPresence.status = status;
      this.currentPresence.lastSeen = new Date().toISOString();

      // Update presence tracking
      if (this.presenceChannel && this.isActive) {
        await this.presenceChannel.track(this.currentPresence);
        await this.broadcastPresence();
      }

      console.log(`👥 Fallback presence updated: ${status}`);
    } catch (error) {
      console.warn('⚠️ Failed to update fallback presence:', error);
    }
  }

  /**
   * Handle presence update broadcast
   */
  private handlePresenceUpdate(payload: any): void {
    console.log('👥 Fallback presence update:', payload);
    this.emitCustomEvent('presence-updated', payload);
  }

  /**
   * Handle presence sync
   */
  private handlePresenceSync(presenceState: any): void {
    const users = Object.keys(presenceState).map(userId => {
      const presence = presenceState[userId][0];
      return presence;
    });

    console.log('👥 Current online users (fallback):', users.length);
    this.emitCustomEvent('presence-sync', { users, count: users.length });
  }

  /**
   * Handle user join
   */
  private handlePresenceJoin(key: string, newPresences: any[]): void {
    const user = newPresences[0];
    console.log('👋 User joined (fallback):', user?.userName || key);
    this.emitCustomEvent('user-joined', { userId: key, user });
  }

  /**
   * Handle user leave
   */
  private handlePresenceLeave(key: string, leftPresences: any[]): void {
    const user = leftPresences[0];
    console.log('👋 User left (fallback):', user?.userName || key);
    this.emitCustomEvent('user-left', { userId: key, user });
  }

  /**
   * Get device information
   */
  private getDeviceInfo(): string {
    const userAgent = navigator.userAgent;
    if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
      return 'Mobile';
    } else if (/Tablet/.test(userAgent)) {
      return 'Tablet';
    } else {
      return 'Desktop';
    }
  }

  /**
   * Emit custom event
   */
  private emitCustomEvent(eventName: string, data: any): void {
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent(`fallback-presence-${eventName}`, {
        detail: data
      }));
    }
  }

  /**
   * Get current presence status
   */
  getCurrentPresence(): UserPresence | null {
    return this.currentPresence;
  }

  /**
   * Get service status
   */
  getStatus(): {
    isActive: boolean;
    hasPresence: boolean;
    channelConnected: boolean;
  } {
    return {
      isActive: this.isActive,
      hasPresence: this.currentPresence !== null,
      channelConnected: this.presenceChannel !== null
    };
  }

  /**
   * Cleanup presence service
   */
  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up fallback presence service...');

    // Clear heartbeat
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = undefined;
    }

    // Update status to offline before disconnecting
    if (this.currentPresence) {
      await this.updatePresence('offline');
    }

    // Remove channel
    if (this.presenceChannel) {
      await supabase.removeChannel(this.presenceChannel);
      this.presenceChannel = null;
    }

    this.isActive = false;
    this.currentPresence = null;
    console.log('✅ Fallback presence service cleanup completed');
  }
}

// Global instance
export const fallbackPresenceService = FallbackPresenceService.getInstance();

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).fallbackPresenceService = fallbackPresenceService;
  (window as any).initFallbackPresence = () => fallbackPresenceService.initialize();
  (window as any).updateFallbackPresence = (status: string) => fallbackPresenceService.updatePresence(status as any);
  (window as any).getFallbackPresenceStatus = () => fallbackPresenceService.getStatus();
  (window as any).getCurrentFallbackPresence = () => fallbackPresenceService.getCurrentPresence();
  
  console.log('👥 Fallback Presence Service loaded. Available commands:');
  console.log('  - initFallbackPresence()');
  console.log('  - updateFallbackPresence(status)');
  console.log('  - getFallbackPresenceStatus()');
  console.log('  - getCurrentFallbackPresence()');
}
