import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { ComprehensiveSystemFixer } from '@/utils/comprehensive-system-fixer';

export const SystemTestPage: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [systemStatus, setSystemStatus] = useState<'unknown' | 'healthy' | 'issues'>('unknown');

  const addResult = (result: string) => {
    setTestResults(prev => [...prev, result]);
  };

  const runSystemTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    try {
      addResult('🧪 Starting comprehensive system tests...');
      
      // Test 1: Authentication System
      addResult('\n🔐 Testing Authentication System...');
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        if (error) {
          addResult(`❌ Auth session error: ${error.message}`);
        } else {
          addResult('✅ Authentication system accessible');
        }
      } catch (error) {
        addResult(`❌ Auth test failed: ${error}`);
      }
      
      // Test 2: Password Validation
      addResult('\n🔒 Testing Password Validation...');
      if (typeof (window as any).validatePassword === 'function') {
        const testPassword = 'TestPass123';
        const validation = (window as any).validatePassword(testPassword);
        addResult(`✅ Password validation working: ${validation.isValid ? 'Valid' : 'Invalid'} (${validation.strength})`);
      } else {
        addResult('❌ Password validation function not found');
      }
      
      // Test 3: Database Connectivity
      addResult('\n🗄️ Testing Database Connectivity...');
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('count')
          .limit(1);
        
        if (error) {
          addResult(`⚠️ Database access issue: ${error.message}`);
        } else {
          addResult('✅ Database connectivity verified');
        }
      } catch (error) {
        addResult(`❌ Database test failed: ${error}`);
      }
      
      // Test 4: Time Logs Table
      addResult('\n🕐 Testing Time Logs System...');
      try {
        const { data, error } = await supabase
          .from('time_logs')
          .select('id')
          .limit(1);
        
        if (error) {
          addResult(`⚠️ Time logs table issue: ${error.message}`);
        } else {
          addResult('✅ Time logs table accessible');
        }
      } catch (error) {
        addResult(`❌ Time logs test failed: ${error}`);
      }
      
      // Test 5: Location Services
      addResult('\n📍 Testing Location Services...');
      if (typeof (window as any).getCurrentLocation === 'function') {
        try {
          const location = await (window as any).getCurrentLocation();
          addResult(`✅ Location services working: ${location.latitude}, ${location.longitude}`);
        } catch (error) {
          addResult(`⚠️ Location services error: ${error}`);
        }
      } else {
        addResult('❌ Location services function not found');
      }
      
      // Test 6: Error Handling
      addResult('\n🛠️ Testing Error Handling...');
      if (typeof (window as any).handleSystemError === 'function') {
        addResult('✅ Global error handler available');
      } else {
        addResult('❌ Global error handler not found');
      }
      
      if (typeof (window as any).getUserFriendlyError === 'function') {
        const testError = new Error('Invalid login credentials');
        const friendlyMessage = (window as any).getUserFriendlyError(testError);
        addResult(`✅ User-friendly error messages: "${friendlyMessage}"`);
      } else {
        addResult('❌ User-friendly error function not found');
      }
      
      // Test 7: System Fixer Status
      addResult('\n🔧 Checking System Fixer Status...');
      try {
        const fixer = ComprehensiveSystemFixer.getInstance();
        const result = await fixer.initializeSystemFixes();
        addResult(`✅ System fixer completed: ${result.success ? 'Success' : 'Failed'}`);
        addResult(`📋 Applied fixes: ${result.results.length}`);
        result.results.forEach(fix => addResult(`   • ${fix}`));
      } catch (error) {
        addResult(`❌ System fixer error: ${error}`);
      }
      
      addResult('\n🎉 System tests completed!');
      
      // Determine overall system status
      const hasErrors = testResults.some(result => result.includes('❌'));
      const hasWarnings = testResults.some(result => result.includes('⚠️'));
      
      if (hasErrors) {
        setSystemStatus('issues');
        toast.error('System Issues Detected', {
          description: 'Some system components have errors. Check the test results.',
          duration: 5000
        });
      } else if (hasWarnings) {
        setSystemStatus('issues');
        toast.warning('System Warnings', {
          description: 'System is mostly functional but has some warnings.',
          duration: 4000
        });
      } else {
        setSystemStatus('healthy');
        toast.success('System Healthy', {
          description: 'All system components are working correctly!',
          duration: 3000
        });
      }
      
    } catch (error) {
      addResult(`💥 Critical test error: ${error}`);
      setSystemStatus('issues');
    } finally {
      setIsRunning(false);
    }
  };

  const testUserRegistration = async () => {
    addResult('\n👤 Testing User Registration Flow...');
    
    // Test email validation
    const testEmails = ['<EMAIL>', 'invalid-email', ''];
    testEmails.forEach(email => {
      const isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
      addResult(`${isValid ? '✅' : '❌'} Email validation for "${email}": ${isValid ? 'Valid' : 'Invalid'}`);
    });
    
    // Test password validation
    const testPasswords = ['weak', 'StrongPass123', 'NoNumbers', 'nonumbers123'];
    testPasswords.forEach(password => {
      if (typeof (window as any).validatePassword === 'function') {
        const validation = (window as any).validatePassword(password);
        addResult(`${validation.isValid ? '✅' : '❌'} Password "${password}": ${validation.strength} (${validation.isValid ? 'Valid' : validation.errors.join(', ')})`);
      }
    });
  };

  const testClockInSystem = async () => {
    addResult('\n🕐 Testing Clock-In System...');
    
    try {
      // Test if user is authenticated
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError || !user) {
        addResult('⚠️ User not authenticated - clock-in requires authentication');
        return;
      }
      
      addResult(`✅ User authenticated: ${user.email}`);
      
      // Test clock-in function availability
      try {
        const { data, error } = await supabase.rpc('clock_in', {
          p_user_id: user.id,
          p_location_data: { latitude: 0, longitude: 0 },
          p_device_info: { userAgent: navigator.userAgent },
          p_notes: 'System test clock-in'
        });
        
        if (error) {
          if (error.message.includes('already has an active time log')) {
            addResult('⚠️ User already clocked in (expected behavior)');
          } else {
            addResult(`❌ Clock-in function error: ${error.message}`);
          }
        } else {
          addResult(`✅ Clock-in function working: Log ID ${data}`);
          
          // Test clock-out
          const { error: clockOutError } = await supabase.rpc('clock_out', {
            p_user_id: user.id,
            p_notes: 'System test clock-out'
          });
          
          if (clockOutError) {
            addResult(`❌ Clock-out function error: ${clockOutError.message}`);
          } else {
            addResult('✅ Clock-out function working');
          }
        }
      } catch (error) {
        addResult(`❌ Clock-in/out functions not available: ${error}`);
      }
      
    } catch (error) {
      addResult(`❌ Clock-in system test failed: ${error}`);
    }
  };

  useEffect(() => {
    // Auto-run tests on component mount
    runSystemTests();
  }, []);

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          System Health Dashboard
        </h1>
        <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${
          systemStatus === 'healthy' ? 'bg-green-100 text-green-800' :
          systemStatus === 'issues' ? 'bg-red-100 text-red-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {systemStatus === 'healthy' && '✅ System Healthy'}
          {systemStatus === 'issues' && '⚠️ Issues Detected'}
          {systemStatus === 'unknown' && '🔍 Checking...'}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <button
          onClick={runSystemTests}
          disabled={isRunning}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isRunning ? '🔄 Running Tests...' : '🧪 Run All Tests'}
        </button>
        
        <button
          onClick={testUserRegistration}
          disabled={isRunning}
          className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          👤 Test Registration
        </button>
        
        <button
          onClick={testClockInSystem}
          disabled={isRunning}
          className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          🕐 Test Clock-In
        </button>
      </div>

      <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto">
        <div className="mb-2 text-gray-400">System Test Console:</div>
        {testResults.length === 0 ? (
          <div className="text-gray-500">No tests run yet...</div>
        ) : (
          testResults.map((result, index) => (
            <div key={index} className="whitespace-pre-wrap">
              {result}
            </div>
          ))
        )}
      </div>

      <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
        <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
          🔧 System Fixes Applied:
        </h3>
        <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
          <li>• Authentication system optimization</li>
          <li>• Password validation with strength checking</li>
          <li>• Clock-in/out system with database functions</li>
          <li>• Error handling improvements</li>
          <li>• User registration flow fixes</li>
          <li>• Database connectivity enhancements</li>
          <li>• User-friendly error messages</li>
          <li>• Location services integration</li>
        </ul>
      </div>
    </div>
  );
};

export default SystemTestPage;