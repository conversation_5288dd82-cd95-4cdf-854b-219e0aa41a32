/**
 * Enhanced Time Tracking Service
 * Robust time tracking with proper error handling and fallbacks
 */

import { supabase } from '@/integrations/supabase/client';
import { enhancedLocationService } from './enhanced-location-service';
import { enhancedDeviceService } from './enhanced-device-service';

export interface TimeLogEntry {
  id?: string;
  user_id: string;
  project_id?: string;
  task_id?: string;
  description?: string;
  clock_in_timestamp?: string;
  clock_out_timestamp?: string;
  start_time?: string;
  end_time?: string;
  duration_minutes?: number;
  break_duration_minutes?: number;
  is_billable?: boolean;
  hourly_rate?: number;
  total_amount?: number;
  status: 'active' | 'paused' | 'completed' | 'cancelled';
  location_data?: any;
  device_info?: any;
  tags?: string[];
  notes?: string;
  metadata?: any;
}

export interface UserPresence {
  user_id: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  last_seen?: string;
  location_data?: any;
  device_info?: any;
  metadata?: any;
}

export class EnhancedTimeTrackingService {
  private static instance: EnhancedTimeTrackingService;
  private currentSession: TimeLogEntry | null = null;

  static getInstance(): EnhancedTimeTrackingService {
    if (!EnhancedTimeTrackingService.instance) {
      EnhancedTimeTrackingService.instance = new EnhancedTimeTrackingService();
    }
    return EnhancedTimeTrackingService.instance;
  }

  /**
   * Clock in with enhanced data collection
   */
  async clockIn(projectId?: string, taskId?: string, description?: string): Promise<{ success: boolean; error?: string; data?: TimeLogEntry }> {
    try {
      console.log('⏰ Starting clock in process...');

      // Get current user
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        return { success: false, error: 'User not authenticated' };
      }

      // Check if user is already clocked in
      const existingSession = await this.getCurrentSession(user.id);
      if (existingSession) {
        return { success: false, error: 'Already clocked in. Please clock out first.' };
      }

      // Gather location and device data
      const [locationData, deviceInfo] = await Promise.allSettled([
        enhancedLocationService.getCurrentLocation(),
        enhancedDeviceService.getDeviceInfo()
      ]);

      const now = new Date().toISOString();

      const timeLogData: TimeLogEntry = {
        user_id: user.id,
        project_id: projectId || null,
        task_id: taskId || null,
        description: description || 'Work session',
        clock_in_timestamp: now,
        start_time: now,
        status: 'active',
        is_billable: true,
        break_duration_minutes: 0,
        location_data: locationData.status === 'fulfilled' ? locationData.value : null,
        device_info: deviceInfo.status === 'fulfilled' ? deviceInfo.value : null,
        metadata: {
          clock_in_method: 'enhanced_service',
          timestamp: now
        }
      };

      // Insert time log entry
      const { data, error } = await supabase
        .from('time_logs')
        .insert(timeLogData)
        .select()
        .single();

      if (error) {
        console.error('❌ Clock in failed:', error);
        return { success: false, error: error.message };
      }

      this.currentSession = data;

      // Update user presence
      await this.updateUserPresence(user.id, 'online');

      console.log('✅ Clock in successful');
      return { success: true, data };
    } catch (error: any) {
      console.error('❌ Clock in error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Clock out with duration calculation
   */
  async clockOut(notes?: string): Promise<{ success: boolean; error?: string; data?: TimeLogEntry }> {
    try {
      console.log('⏰ Starting clock out process...');

      // Get current user
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        return { success: false, error: 'User not authenticated' };
      }

      // Get current session
      const currentSession = await this.getCurrentSession(user.id);
      if (!currentSession) {
        return { success: false, error: 'No active session found' };
      }

      const now = new Date().toISOString();
      const clockInTime = new Date(currentSession.clock_in_timestamp || currentSession.start_time || now);
      const clockOutTime = new Date(now);
      const durationMinutes = Math.round((clockOutTime.getTime() - clockInTime.getTime()) / (1000 * 60));

      // Calculate total amount if hourly rate is set
      let totalAmount = null;
      if (currentSession.hourly_rate && durationMinutes > 0) {
        const hours = durationMinutes / 60;
        totalAmount = hours * currentSession.hourly_rate;
      }

      const updateData = {
        clock_out_timestamp: now,
        end_time: now,
        duration_minutes: durationMinutes,
        total_amount: totalAmount,
        status: 'completed' as const,
        notes: notes || currentSession.notes,
        metadata: {
          ...currentSession.metadata,
          clock_out_method: 'enhanced_service',
          clock_out_timestamp: now
        }
      };

      // Update time log entry
      const { data, error } = await supabase
        .from('time_logs')
        .update(updateData)
        .eq('id', currentSession.id)
        .select()
        .single();

      if (error) {
        console.error('❌ Clock out failed:', error);
        return { success: false, error: error.message };
      }

      this.currentSession = null;

      // Update user presence
      await this.updateUserPresence(user.id, 'away');

      console.log('✅ Clock out successful');
      return { success: true, data };
    } catch (error: any) {
      console.error('❌ Clock out error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get current active session
   */
  async getCurrentSession(userId?: string): Promise<TimeLogEntry | null> {
    try {
      if (!userId) {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return null;
        userId = user.id;
      }

      const { data, error } = await supabase
        .from('time_logs')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      if (error) {
        console.error('❌ Error getting current session:', error);
        return null;
      }

      this.currentSession = data;
      return data;
    } catch (error) {
      console.error('❌ Error getting current session:', error);
      return null;
    }
  }

  /**
   * Update user presence
   */
  async updateUserPresence(userId: string, status: 'online' | 'away' | 'busy' | 'offline'): Promise<void> {
    try {
      const [locationData, deviceInfo] = await Promise.allSettled([
        enhancedLocationService.getCurrentLocation(),
        enhancedDeviceService.getDeviceInfo()
      ]);

      const presenceData: UserPresence = {
        user_id: userId,
        status,
        last_seen: new Date().toISOString(),
        location_data: locationData.status === 'fulfilled' ? locationData.value : null,
        device_info: deviceInfo.status === 'fulfilled' ? deviceInfo.value : null,
        metadata: {
          updated_by: 'enhanced_time_tracking',
          timestamp: new Date().toISOString()
        }
      };

      const { error } = await supabase
        .from('user_presence')
        .upsert(presenceData, { onConflict: 'user_id' });

      if (error) {
        console.warn('⚠️ Failed to update user presence:', error.message);
      } else {
        console.log('✅ User presence updated:', status);
      }
    } catch (error) {
      console.warn('⚠️ Error updating user presence:', error);
    }
  }

  /**
   * Get time logs for a user
   */
  async getTimeLogs(userId?: string, limit = 50): Promise<TimeLogEntry[]> {
    try {
      if (!userId) {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return [];
        userId = user.id;
      }

      const { data, error } = await supabase
        .from('time_logs')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('❌ Error getting time logs:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('❌ Error getting time logs:', error);
      return [];
    }
  }

  /**
   * Get user presence
   */
  async getUserPresence(userId?: string): Promise<UserPresence | null> {
    try {
      if (!userId) {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return null;
        userId = user.id;
      }

      const { data, error } = await supabase
        .from('user_presence')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        console.warn('⚠️ Error getting user presence:', error.message);
        return null;
      }

      return data;
    } catch (error) {
      console.warn('⚠️ Error getting user presence:', error);
      return null;
    }
  }

  /**
   * Check if user is currently clocked in
   */
  async isUserClockedIn(userId?: string): Promise<boolean> {
    const session = await this.getCurrentSession(userId);
    return session !== null;
  }

  /**
   * Get current session data
   */
  getCurrentSessionData(): TimeLogEntry | null {
    return this.currentSession;
  }
}

// Global instance
export const enhancedTimeTrackingService = EnhancedTimeTrackingService.getInstance();

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).enhancedTimeTrackingService = enhancedTimeTrackingService;
  (window as any).clockIn = (projectId?: string, taskId?: string, description?: string) => 
    enhancedTimeTrackingService.clockIn(projectId, taskId, description);
  (window as any).clockOut = (notes?: string) => 
    enhancedTimeTrackingService.clockOut(notes);
  
  console.log('⏰ Enhanced Time Tracking Service loaded. Available commands:');
  console.log('  - clockIn(projectId?, taskId?, description?)');
  console.log('  - clockOut(notes?)');
}
