import { useAuth } from "@/components/auth/AuthProvider";
import { Enhanced<PERSON><PERSON> } from "@/components/charts/EnhancedChart";
import { DashboardSkeleton } from "@/components/dashboard/DashboardSkeleton";
import { EnhancedDashboardStats } from "@/components/dashboard/EnhancedDashboardStats";
import { ErrorState } from "@/components/dashboard/ErrorState";
import { ModernDashboardGrid } from "@/components/dashboard/ModernDashboardGrid";
import { NotificationCenter } from "@/components/notifications/NotificationCenter";
import { CompactTimeCard } from "@/components/time-tracking/CompactTimeCard";
import { useDashboardData } from "@/hooks/useDashboardData";
import {
    Activity,
    AlertTriangle,
    BarChart3,
    CheckSquare,
    Clock,
    DollarSign,
    Star, Target,
    TrendingUp,
    Users,
    Zap
} from "lucide-react";
import { Bar, BarChart, CartesianGrid, Cell, Line, LineChart, Pie, <PERSON><PERSON>, <PERSON>lt<PERSON>, <PERSON>A<PERSON><PERSON>, YAxis } from "recharts";

export const UnifiedDashboard = () => {
  const { userProfile } = useAuth();
  const { data: dashboardData, isLoading, error, refetch } = useDashboardData();

  console.log('🔍 UnifiedDashboard Debug:', {
    userProfile: userProfile?.role,
    dashboardData: !!dashboardData,
    isLoading,
    error: error?.message
  });

  // Show skeleton only for initial load without any data
  if (isLoading && !dashboardData) {
    console.log('⏳ UnifiedDashboard: Showing loading skeleton');
    return <DashboardSkeleton />;
  }

  // If we have an error but no data, show error state
  if (error && !dashboardData) {
    console.log('❌ UnifiedDashboard: Error occurred without data:', error);
    return <ErrorState error={error} onRetry={refetch} title="Failed to load dashboard" />;
  }

  // If we have an error but also have data, just log it and continue
  if (error && dashboardData) {
    console.log('⚠️ UnifiedDashboard: Error occurred but showing cached data:', error);
  }

  // Defensive fallback for dashboardData
  const safeData = dashboardData || {
    stats: {
      completedTasks: 0,
      activeProjects: 0,
      onlineUsers: 0,
      systemHealth: 100,
      pendingApprovals: 0,
      totalRevenue: 0,
    },
    chartData: [],
    pieChartData: [],
    recentActivity: [],
    performanceMetrics: {
      hoursWorked: 0,
      efficiency: 100,
    },
    financialSummary: {},
  };

  const { stats, chartData, pieChartData, recentActivity, performanceMetrics, financialSummary } = safeData;
  const role = userProfile?.role || 'staff';

  // Defensive fallback for EnhancedDashboardStats
  const getStatsForRole = () => {
    const baseStats = [
      {
        title: role === 'staff' ? "Tasks Completed" : "Total Projects",
        value: role === 'staff'
          ? (stats.completedTasks ?? 0).toString()
          : (stats.activeProjects ?? 0).toString(),
        icon: <CheckSquare className="h-8 w-8" />,
        trend: 'up' as const,
        trendValue: "+18%",
        color: 'green' as const
      },
      {
        title: role === 'staff' ? "Hours Logged" : "Team Members",
        value: role === 'staff'
          ? (performanceMetrics.hoursWorked ?? 0).toString()
          : (stats.onlineUsers ?? 0).toString(),
        icon: role === 'staff' ? <Clock className="h-8 w-8" /> : <Users className="h-8 w-8" />,
        trend: 'up' as const,
        trendValue: "+5%",
        color: 'blue' as const
      },
      {
        title: role === 'admin' ? "System Health"
          : role === 'manager' ? "Efficiency"
          : "Efficiency",
        value: role === 'admin'
          ? `${stats.systemHealth ?? 100}%`
          : `${performanceMetrics.efficiency ?? 100}%`,
        icon: <Zap className="h-8 w-8" />,
        trend: 'up' as const,
        trendValue: "+7%",
        color: 'blue' as const
      },
      {
        title: "Pending Items",
        value: (stats.pendingApprovals ?? 0).toString(),
        icon: <AlertTriangle className="h-8 w-8" />,
        trend: 'down' as const,
        trendValue: "-15%",
        color: 'red' as const
      }
    ];

    // Add financial stats for admin and accountant roles
    if (role === 'admin' || role === 'accountant') {
      baseStats.push({
        title: "Total Revenue",
        value: `₦${(stats.totalRevenue ?? 0).toLocaleString()}`,
        icon: <DollarSign className="h-8 w-8" />,
        trend: 'up' as const,
        trendValue: "+12%",
        color: 'green' as const
      });
    }

    // Manager-specific fallback stats
    if (role === 'manager') {
      baseStats.push({
        title: "Projects",
        value: (stats.activeProjects ?? 0).toString(),
        icon: <BarChart3 className="h-8 w-8" />, // Use BarChart3 instead of BarChart
        trend: 'up' as const,
        trendValue: "+10%",
        color: 'purple' as const
      });
      baseStats.push({
        title: "Team Size",
        value: (stats.onlineUsers ?? 0).toString(),
        icon: <Users className="h-8 w-8" />,
        trend: 'up' as const,
        trendValue: "+3%",
        color: 'blue' as const
      });
    }

    return baseStats;
  };

  const roleStats = getStatsForRole();

  // Defensive fallback for chart data
  const generateTimeSeriesData = () => {
    const baseData = [
      { name: 'Mon', value1: 20, value2: 50 },
      { name: 'Tue', value1: 25, value2: 55 },
      { name: 'Wed', value1: 30, value2: 60 },
      { name: 'Thu', value1: 28, value2: 58 },
      { name: 'Fri', value1: 35, value2: 65 },
    ];

    if (role === 'staff') {
      return baseData.map(item => ({
        ...item,
        tasks: item.value1,
        hours: item.value2 / 10,
        productivity: item.value2
      }));
    } else if (role === 'manager') {
      return baseData.map(item => ({
        ...item,
        team: item.value1,
        projects: Math.floor(item.value1 * 0.6),
        efficiency: item.value2
      }));
    } else {
      return baseData.map(item => ({
        ...item,
        users: item.value1,
        projects: Math.floor(item.value1 * 0.8),
        revenue: item.value2 * 1000
      }));
    }
  };

  const timeSeriesData = Array.isArray(chartData) && chartData.length > 0 ? chartData : generateTimeSeriesData();
  const safePieChartData = Array.isArray(pieChartData) && pieChartData.length > 0 ? pieChartData : [
    { name: 'No Data', value: 1 }
  ];
  const safeRecentActivity = Array.isArray(recentActivity) ? recentActivity : [];

  console.log('📊 UnifiedDashboard: State check', {
    userProfile: userProfile?.role,
    isLoading,
    error: error?.message,
    hasDashboardData: !!dashboardData
  });

  console.log('✅ UnifiedDashboard: Rendering dashboard with data');

  return (
    <div className="space-y-8 relative">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-10 right-10 w-64 h-64 bg-gradient-to-br from-primary/5 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-10 left-10 w-48 h-48 bg-gradient-to-tr from-secondary/5 to-transparent rounded-full blur-2xl"></div>
      </div>

      <div className="relative z-10">
        {/* Welcome Header */}
        <div className="glassmorphism rounded-2xl p-8 mb-8" data-aos="fade-down">
          <div className="flex items-center gap-4 mb-4">
            <div className="p-3 rounded-2xl bg-gradient-to-br from-primary to-primary/80 shadow-xl">
              <Star className="h-8 w-8 text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-4xl font-bold modern-heading mb-2">
                {role === 'admin' ? 'Admin Command Center' :
                 role === 'manager' ? 'Manager Dashboard' :
                 role === 'accountant' ? 'Financial Dashboard' :
                 role === 'staff-admin' ? 'Staff Admin Panel' :
                 'Staff Performance Hub'}
              </h1>
              <p className="text-muted-foreground text-lg">
                Welcome back, {userProfile?.full_name || 'User'}! Here's your overview.
              </p>
            </div>

            {/* Notification Center */}
            <div className="flex items-center">
              <NotificationCenter />
            </div>
          </div>
        </div>

        {/* Modern Stats Grid */}
        <ModernDashboardGrid
          cards={[
            {
              id: 'primary-1',
              title: role === 'admin' ? 'Total Users' : role === 'manager' ? 'Team Members' : 'Active Projects',
              value: role === 'admin' ? (stats.totalUsers ?? 0) : role === 'manager' ? (stats.teamMembers ?? 0) : (stats.activeProjects ?? 0),
              change: 12,
              changeType: 'increase',
              icon: Users,
              description: 'Active this month',
              trend: [20, 35, 45, 30, 55, 40, 60],
              color: '#3b82f6'
            },
            {
              id: 'primary-2',
              title: role === 'admin' ? 'Active Projects' : role === 'manager' ? 'Projects' : 'Completed Tasks',
              value: role === 'admin' ? (stats.activeProjects ?? 0) : role === 'manager' ? (stats.activeProjects ?? 0) : (stats.completedTasks ?? 0),
              change: 8,
              changeType: 'increase',
              icon: CheckSquare,
              description: 'This quarter',
              trend: [15, 25, 35, 45, 40, 50, 55],
              color: '#10b981'
            },
            {
              id: 'primary-3',
              title: role === 'admin' ? 'Total Revenue' : role === 'manager' ? 'Budget Used' : 'Hours Logged',
              value: role === 'admin' ? '$125,430' : role === 'manager' ? '68%' : '156h',
              change: role === 'admin' ? 15 : role === 'manager' ? -5 : 22,
              changeType: role === 'admin' ? 'increase' : role === 'manager' ? 'decrease' : 'increase',
              icon: DollarSign,
              description: 'Current period',
              trend: [30, 40, 35, 50, 45, 60, 55],
              color: '#f59e0b'
            },
            {
              id: 'primary-4',
              title: 'Performance',
              value: '94%',
              change: 3,
              changeType: 'increase',
              icon: TrendingUp,
              description: 'Overall efficiency',
              trend: [40, 45, 50, 48, 55, 52, 58],
              color: '#8b5cf6'
            }
          ]}
          loading={isLoading}
        />

        {/* Time Card for staff-admin and accountant */}
        {(role === 'staff-admin' || role === 'accountant') && (
          <div className="mt-8">
            <CompactTimeCard userRole={role} showControls={true} />
          </div>
        )}

        {/* Charts Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8 mt-8">
          <EnhancedChart
            title={role === 'staff' ? "Weekly Performance" : role === 'manager' ? "Team Overview" : "System Metrics"}
            icon={<Activity className="h-5 w-5" />}
            timeFilter
            exportable
            refreshable
          >
            <BarChart data={timeSeriesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              {role === 'staff' ? (
                <>
                  <Bar dataKey="tasks" fill="hsl(var(--primary))" name="Tasks" />
                  <Bar dataKey="hours" fill="hsl(var(--secondary))" name="Hours" />
                </>
              ) : role === 'manager' ? (
                <>
                  <Bar dataKey="team" fill="hsl(var(--primary))" name="Team" />
                  <Bar dataKey="projects" fill="hsl(var(--secondary))" name="Projects" />
                </>
              ) : (
                <>
                  <Bar dataKey="users" fill="hsl(var(--primary))" name="Users" />
                  <Bar dataKey="projects" fill="hsl(var(--secondary))" name="Projects" />
                </>
              )}
            </BarChart>
          </EnhancedChart>

          <EnhancedChart
            title="Status Distribution"
            icon={<Target className="h-5 w-5" />}
            exportable
          >
            <PieChart>
              <Pie
                data={safePieChartData}
                cx="50%"
                cy="50%"
                outerRadius={80}
                fill="hsl(var(--primary))"
                dataKey="value"
              >
                {safePieChartData.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={`hsl(var(--chart-${(index % 5) + 1}))`} 
                  />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </EnhancedChart>
        </div>

        {/* Trends Chart */}
        <div className="grid grid-cols-1 gap-8 mt-8">
          <EnhancedChart
            title="Trends & Analytics"
            icon={<TrendingUp className="h-5 w-5" />}
            timeFilter
            exportable
            refreshable
            fullscreen
          >
            <LineChart data={timeSeriesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              {role === 'staff' ? (
                <>
                  <Line type="monotone" dataKey="tasks" stroke="hsl(var(--primary))" strokeWidth={3} name="Tasks" />
                  <Line type="monotone" dataKey="productivity" stroke="hsl(var(--secondary))" strokeWidth={3} name="Productivity" />
                </>
              ) : role === 'manager' ? (
                <>
                  <Line type="monotone" dataKey="team" stroke="hsl(var(--primary))" strokeWidth={3} name="Team Size" />
                  <Line type="monotone" dataKey="efficiency" stroke="hsl(var(--secondary))" strokeWidth={3} name="Efficiency" />
                </>
              ) : (
                <>
                  <Line type="monotone" dataKey="users" stroke="hsl(var(--primary))" strokeWidth={3} name="Users" />
                  <Line type="monotone" dataKey="projects" stroke="hsl(var(--secondary))" strokeWidth={3} name="Projects" />
                </>
              )}
            </LineChart>
          </EnhancedChart>
        </div>

        {/* Recent Activity */}
        {safeRecentActivity.length > 0 && (
          <div className="glassmorphism rounded-2xl p-6 mt-8">
            <div className="flex items-center gap-2 mb-6">
              <Activity className="h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold">Recent Activity</h3>
            </div>
            <div className="space-y-4">
              {safeRecentActivity.slice(0, 5).map((activity) => (
                <div key={activity.id} className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                  <div className="flex items-center gap-3">
                    <div className={`w-2 h-2 rounded-full ${
                      activity.type === 'error' ? 'bg-destructive' :
                      activity.type === 'warning' ? 'bg-yellow-500' :
                      activity.type === 'success' ? 'bg-green-500' : 'bg-primary'
                    }`} />
                    <div>
                      <p className="font-medium">{activity.action}</p>
                      <p className="text-sm text-muted-foreground">
                        by {activity.user} • {activity.description}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    {new Date(activity.timestamp).toLocaleString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// No change needed here, but ensure your Supabase query for projects is simplified as shown above.
