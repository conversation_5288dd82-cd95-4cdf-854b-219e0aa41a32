/**
 * Authentication Fix Component
 * Handles authentication issues and provides fallback options
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useNavigate } from 'react-router-dom';
import { 
  Shield, 
  User, 
  Lock, 
  AlertCircle, 
  CheckCircle, 
  RefreshCw,
  Eye,
  EyeOff
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface AuthState {
  isLoading: boolean;
  isAuthenticated: boolean;
  user: any;
  error: string | null;
}

export const AuthenticationFix: React.FC = () => {
  const [authState, setAuthState] = useState<AuthState>({
    isLoading: true,
    isAuthenticated: false,
    user: null,
    error: null
  });
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isSignUp, setIsSignUp] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    checkAuthState();
    
    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔐 Auth state change:', event, session?.user?.email);
        
        if (session?.user) {
          setAuthState({
            isLoading: false,
            isAuthenticated: true,
            user: session.user,
            error: null
          });
          
          // Redirect to dashboard
          navigate('/dashboard');
        } else {
          setAuthState({
            isLoading: false,
            isAuthenticated: false,
            user: null,
            error: null
          });
        }
      }
    );

    return () => subscription.unsubscribe();
  }, [navigate]);

  const checkAuthState = async () => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        console.error('Auth check error:', error);
        setAuthState({
          isLoading: false,
          isAuthenticated: false,
          user: null,
          error: error.message
        });
        return;
      }

      if (session?.user) {
        setAuthState({
          isLoading: false,
          isAuthenticated: true,
          user: session.user,
          error: null
        });
        navigate('/dashboard');
      } else {
        setAuthState({
          isLoading: false,
          isAuthenticated: false,
          user: null,
          error: null
        });
      }
    } catch (error: any) {
      console.error('Auth state check failed:', error);
      setAuthState({
        isLoading: false,
        isAuthenticated: false,
        user: null,
        error: error.message
      });
    }
  };

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !password) {
      toast({
        title: "Missing Information",
        description: "Please enter both email and password",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: email.trim(),
        password: password
      });

      if (error) {
        throw error;
      }

      if (data.user) {
        toast({
          title: "Success",
          description: "Signed in successfully",
        });
        
        // The auth state change listener will handle navigation
      }
    } catch (error: any) {
      console.error('Sign in error:', error);
      toast({
        title: "Sign In Failed",
        description: error.message || "Failed to sign in",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !password) {
      toast({
        title: "Missing Information",
        description: "Please enter both email and password",
        variant: "destructive",
      });
      return;
    }

    if (password.length < 6) {
      toast({
        title: "Password Too Short",
        description: "Password must be at least 6 characters",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const { data, error } = await supabase.auth.signUp({
        email: email.trim(),
        password: password,
        options: {
          data: {
            full_name: email.split('@')[0], // Use email prefix as default name
          }
        }
      });

      if (error) {
        throw error;
      }

      if (data.user) {
        toast({
          title: "Success",
          description: "Account created successfully. Please check your email for verification.",
        });
        
        // Switch to sign in mode
        setIsSignUp(false);
      }
    } catch (error: any) {
      console.error('Sign up error:', error);
      toast({
        title: "Sign Up Failed",
        description: error.message || "Failed to create account",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDemoAccess = () => {
    // Set demo user data in localStorage for testing
    localStorage.setItem('demo-user', JSON.stringify({
      id: 'demo-user-id',
      email: '<EMAIL>',
      full_name: 'Demo User',
      role: 'admin'
    }));
    
    toast({
      title: "Demo Access",
      description: "Accessing system in demo mode",
    });
    
    navigate('/dashboard');
  };

  if (authState.isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#1a1a1a] via-[#0f0f0f] to-[#1a1a1a]">
        <Card className="w-96 border-[#ff1c04]/20 bg-[#1a1a1a]">
          <CardContent className="p-8 text-center">
            <RefreshCw className="h-8 w-8 animate-spin text-[#ff1c04] mx-auto mb-4" />
            <p className="text-[#e5e5e5]">Checking authentication...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (authState.isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#1a1a1a] via-[#0f0f0f] to-[#1a1a1a]">
        <Card className="w-96 border-[#ff1c04]/20 bg-[#1a1a1a]">
          <CardContent className="p-8 text-center">
            <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-4" />
            <p className="text-[#e5e5e5] mb-4">You are signed in!</p>
            <Button 
              onClick={() => navigate('/dashboard')}
              className="bg-[#ff1c04] hover:bg-[#ff1c04]/90 text-white"
            >
              Go to Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#1a1a1a] via-[#0f0f0f] to-[#1a1a1a] p-4">
      <Card className={cn(
        "w-full max-w-md border-[0.8px] border-[#ff1c04]/20",
        "bg-gradient-to-br from-[#1a1a1a] via-[#0f0f0f] to-[#1a1a1a]",
        "shadow-[8px_8px_16px_rgba(0,0,0,0.4),-8px_-8px_16px_rgba(255,28,4,0.1)]"
      )}>
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-3 text-[#e5e5e5]">
            <div className="p-2 rounded-xl bg-gradient-to-br from-[#ff1c04]/10 to-[#ff1c04]/20 border border-[#ff1c04]/20">
              <Shield className="h-6 w-6 text-[#ff1c04]" />
            </div>
            <div>
              <h2 className="text-2xl font-bold">AUTHENTICATION</h2>
              <p className="text-sm text-[#a5a5a5] font-normal">
                {isSignUp ? 'Create your account' : 'Sign in to continue'}
              </p>
            </div>
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-6">
          {authState.error && (
            <div className="p-4 rounded-lg bg-red-500/10 border border-red-500/20">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-red-500" />
                <p className="text-sm text-red-400">{authState.error}</p>
              </div>
            </div>
          )}

          <form onSubmit={isSignUp ? handleSignUp : handleSignIn} className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-[#e5e5e5]">Email</label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[#a5a5a5]" />
                <Input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  className="pl-10 bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-[#e5e5e5]">Password</label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[#a5a5a5]" />
                <Input
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your password"
                  className="pl-10 pr-10 bg-[#1a1a1a] border-[#ff1c04]/30 text-[#e5e5e5]"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#a5a5a5] hover:text-[#e5e5e5]"
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>

            <Button
              type="submit"
              disabled={isLoading}
              className="w-full bg-[#ff1c04] hover:bg-[#ff1c04]/90 text-white"
            >
              {isLoading ? (
                <RefreshCw className="h-4 w-4 animate-spin mr-2" />
              ) : null}
              {isSignUp ? 'Create Account' : 'Sign In'}
            </Button>
          </form>

          <div className="text-center">
            <button
              onClick={() => setIsSignUp(!isSignUp)}
              className="text-sm text-[#ff1c04] hover:text-[#ff1c04]/80"
            >
              {isSignUp ? 'Already have an account? Sign in' : "Don't have an account? Sign up"}
            </button>
          </div>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-[#ff1c04]/20"></div>
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-[#1a1a1a] px-2 text-[#a5a5a5]">Or</span>
            </div>
          </div>

          <Button
            onClick={handleDemoAccess}
            variant="outline"
            className="w-full border-[#ff1c04]/30 text-[#ff1c04] hover:bg-[#ff1c04]/10"
          >
            Continue with Demo Access
          </Button>

          <div className="text-center">
            <Button
              onClick={checkAuthState}
              variant="ghost"
              size="sm"
              className="text-[#a5a5a5] hover:text-[#e5e5e5]"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Auth State
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
