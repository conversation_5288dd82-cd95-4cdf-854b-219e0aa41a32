-- QUICK DATABASE FIX - Run this in Supabase SQL Editor
-- This fixes the most critical 400 errors

-- 1. Add missing columns to profiles
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS position TEXT;
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS hire_date DATE;
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS location TEXT;

-- 2. Create user_presence table
CREATE TABLE IF NOT EXISTS public.user_presence (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    status TEXT DEFAULT 'offline',
    last_seen TIMES<PERSON>MP WITH TIME ZONE DEFAULT NOW(),
    location_data JSONB,
    device_info JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- 3. Create time_logs table
CREATE TABLE IF NOT EXISTS public.time_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    clock_in_time TIMESTAMP WITH TIME ZONE NOT NULL,
    clock_out_time TIMESTAMP WITH TIME ZONE,
    total_hours NUMERIC(5,2) DEFAULT 0,
    status TEXT DEFAULT 'clocked_in',
    location_data JSONB,
    device_info JSONB,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Enable RLS
ALTER TABLE public.user_presence ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.time_logs ENABLE ROW LEVEL SECURITY;

-- 5. Create basic policies (drop first if they exist)
DROP POLICY IF EXISTS "Users can manage their own presence" ON public.user_presence;
CREATE POLICY "Users can manage their own presence"
ON public.user_presence FOR ALL TO authenticated
USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can manage their own time logs" ON public.time_logs;
CREATE POLICY "Users can manage their own time logs"
ON public.time_logs FOR ALL TO authenticated
USING (auth.uid() = user_id);

-- 6. Create clock in function
CREATE OR REPLACE FUNCTION public.clock_in(
    p_user_id UUID,
    p_location_data JSONB DEFAULT NULL,
    p_device_info JSONB DEFAULT NULL,
    p_notes TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_time_log_id UUID;
BEGIN
    INSERT INTO public.time_logs (
        user_id, clock_in_time, location_data, device_info, notes, status
    ) VALUES (
        p_user_id, NOW(), p_location_data, p_device_info, p_notes, 'clocked_in'
    ) RETURNING id INTO v_time_log_id;
    
    RETURN v_time_log_id;
END;
$$;

-- 7. Create clock out function
CREATE OR REPLACE FUNCTION public.clock_out(
    p_user_id UUID,
    p_notes TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_time_log_id UUID;
BEGIN
    UPDATE public.time_logs 
    SET clock_out_time = NOW(), notes = COALESCE(p_notes, notes), status = 'clocked_out'
    WHERE user_id = p_user_id AND clock_out_time IS NULL
    RETURNING id INTO v_time_log_id;
    
    RETURN v_time_log_id;
END;
$$;

-- 8. Grant permissions
GRANT ALL ON public.user_presence TO authenticated;
GRANT ALL ON public.time_logs TO authenticated;
GRANT EXECUTE ON FUNCTION public.clock_in TO authenticated;
GRANT EXECUTE ON FUNCTION public.clock_out TO authenticated;

-- 9. Create missing tables for other 400 errors
CREATE TABLE IF NOT EXISTS public.memos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT,
    content TEXT,
    created_by UUID REFERENCES auth.users(id),
    department_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.procurement_requests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT,
    requested_by UUID REFERENCES auth.users(id),
    status TEXT DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS and create basic policies
ALTER TABLE public.memos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.procurement_requests ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view memos" ON public.memos;
CREATE POLICY "Users can view memos" ON public.memos FOR ALL TO authenticated USING (true);

DROP POLICY IF EXISTS "Users can view procurement" ON public.procurement_requests;
CREATE POLICY "Users can view procurement" ON public.procurement_requests FOR ALL TO authenticated USING (true);

GRANT ALL ON public.memos TO authenticated;
GRANT ALL ON public.procurement_requests TO authenticated;
