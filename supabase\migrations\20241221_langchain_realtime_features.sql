-- LangChain and Real-time Collaboration Features Migration
-- This migration adds all necessary tables and functions for enhanced LangChain and real-time features

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- <PERSON><PERSON><PERSON><PERSON> Conversations Table
CREATE TABLE IF NOT EXISTS langchain_conversations (
    id TEXT PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT,
    messages JSONB DEFAULT '[]'::jsonb,
    context JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- LangChain Documents Table (Vector Store)
CREATE TABLE IF NOT EXISTS langchain_documents (
    id TEXT PRIMARY KEY,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    embedding vector(1536), -- OpenAI text-embedding-3-small dimensions
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON><PERSON><PERSON> Document Metadata Table
CREATE TABLE IF NOT EXISTS langchain_document_metadata (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    chunk_count INTEGER DEFAULT 0,
    processing_time INTEGER DEFAULT 0,
    total_tokens INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Presence Table
CREATE TABLE IF NOT EXISTS user_presence (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    status TEXT CHECK (status IN ('online', 'away', 'busy', 'offline')) DEFAULT 'offline',
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    current_page TEXT,
    is_typing BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}'::jsonb,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Collaborative Sessions Table
CREATE TABLE IF NOT EXISTS collaborative_sessions (
    id TEXT PRIMARY KEY,
    type TEXT CHECK (type IN ('document', 'project', 'task', 'meeting')) NOT NULL,
    resource_id TEXT NOT NULL,
    participants JSONB DEFAULT '[]'::jsonb,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Document Comments Table
CREATE TABLE IF NOT EXISTS document_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id TEXT NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES document_comments(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    position INTEGER NOT NULL,
    resolved BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Real-time Notifications Table
CREATE TABLE IF NOT EXISTS realtime_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    type TEXT NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    data JSONB DEFAULT '{}'::jsonb,
    read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_langchain_conversations_user_id ON langchain_conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_langchain_conversations_updated_at ON langchain_conversations(updated_at);

CREATE INDEX IF NOT EXISTS idx_langchain_documents_embedding ON langchain_documents USING ivfflat (embedding vector_cosine_ops);
CREATE INDEX IF NOT EXISTS idx_langchain_documents_metadata ON langchain_documents USING gin(metadata);

CREATE INDEX IF NOT EXISTS idx_user_presence_status ON user_presence(status);
CREATE INDEX IF NOT EXISTS idx_user_presence_last_seen ON user_presence(last_seen);

CREATE INDEX IF NOT EXISTS idx_collaborative_sessions_type ON collaborative_sessions(type);
CREATE INDEX IF NOT EXISTS idx_collaborative_sessions_resource_id ON collaborative_sessions(resource_id);
CREATE INDEX IF NOT EXISTS idx_collaborative_sessions_last_activity ON collaborative_sessions(last_activity);

CREATE INDEX IF NOT EXISTS idx_document_comments_document_id ON document_comments(document_id);
CREATE INDEX IF NOT EXISTS idx_document_comments_user_id ON document_comments(user_id);
CREATE INDEX IF NOT EXISTS idx_document_comments_parent_id ON document_comments(parent_id);

CREATE INDEX IF NOT EXISTS idx_realtime_notifications_user_id ON realtime_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_realtime_notifications_read ON realtime_notifications(read);

-- Vector similarity search function
CREATE OR REPLACE FUNCTION match_documents(
    query_embedding vector(1536),
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 5
)
RETURNS TABLE (
    id text,
    content text,
    metadata jsonb,
    similarity float
)
LANGUAGE sql STABLE
AS $$
    SELECT
        langchain_documents.id,
        langchain_documents.content,
        langchain_documents.metadata,
        1 - (langchain_documents.embedding <=> query_embedding) AS similarity
    FROM langchain_documents
    WHERE 1 - (langchain_documents.embedding <=> query_embedding) > match_threshold
    ORDER BY similarity DESC
    LIMIT match_count;
$$;

-- Function to update user presence
CREATE OR REPLACE FUNCTION update_user_presence(
    p_user_id UUID,
    p_status TEXT DEFAULT NULL,
    p_current_page TEXT DEFAULT NULL,
    p_is_typing BOOLEAN DEFAULT NULL
)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    INSERT INTO user_presence (user_id, status, current_page, is_typing, last_seen, updated_at)
    VALUES (p_user_id, COALESCE(p_status, 'online'), p_current_page, COALESCE(p_is_typing, FALSE), NOW(), NOW())
    ON CONFLICT (user_id)
    DO UPDATE SET
        status = COALESCE(p_status, user_presence.status),
        current_page = COALESCE(p_current_page, user_presence.current_page),
        is_typing = COALESCE(p_is_typing, user_presence.is_typing),
        last_seen = NOW(),
        updated_at = NOW();
END;
$$;

-- Function to clean up old presence data
CREATE OR REPLACE FUNCTION cleanup_old_presence()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    -- Mark users as offline if they haven't been seen in 5 minutes
    UPDATE user_presence
    SET status = 'offline', is_typing = FALSE, updated_at = NOW()
    WHERE last_seen < NOW() - INTERVAL '5 minutes'
    AND status != 'offline';
    
    -- Delete very old presence records (older than 24 hours)
    DELETE FROM user_presence
    WHERE last_seen < NOW() - INTERVAL '24 hours';
END;
$$;

-- Function to clean up old collaborative sessions
CREATE OR REPLACE FUNCTION cleanup_old_sessions()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    -- Delete sessions with no activity in the last hour
    DELETE FROM collaborative_sessions
    WHERE last_activity < NOW() - INTERVAL '1 hour';
END;
$$;

-- Function to clean up old conversations
CREATE OR REPLACE FUNCTION cleanup_old_conversations()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    -- Delete conversations older than 30 days
    DELETE FROM langchain_conversations
    WHERE updated_at < NOW() - INTERVAL '30 days';
END;
$$;

-- Triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_langchain_conversations_updated_at
    BEFORE UPDATE ON langchain_conversations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_langchain_document_metadata_updated_at
    BEFORE UPDATE ON langchain_document_metadata
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_presence_updated_at
    BEFORE UPDATE ON user_presence
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_document_comments_updated_at
    BEFORE UPDATE ON document_comments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- RLS Policies

-- LangChain Conversations
ALTER TABLE langchain_conversations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own conversations"
ON langchain_conversations
FOR ALL
TO authenticated
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- LangChain Documents (accessible to all authenticated users for RAG)
ALTER TABLE langchain_documents ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Authenticated users can read documents"
ON langchain_documents
FOR SELECT
TO authenticated
USING (true);

CREATE POLICY "Authenticated users can insert documents"
ON langchain_documents
FOR INSERT
TO authenticated
WITH CHECK (true);

-- LangChain Document Metadata
ALTER TABLE langchain_document_metadata ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Authenticated users can read document metadata"
ON langchain_document_metadata
FOR SELECT
TO authenticated
USING (true);

CREATE POLICY "Authenticated users can insert document metadata"
ON langchain_document_metadata
FOR INSERT
TO authenticated
WITH CHECK (true);

-- User Presence
ALTER TABLE user_presence ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own presence"
ON user_presence
FOR ALL
TO authenticated
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view all presence data"
ON user_presence
FOR SELECT
TO authenticated
USING (true);

-- Collaborative Sessions
ALTER TABLE collaborative_sessions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Authenticated users can manage sessions"
ON collaborative_sessions
FOR ALL
TO authenticated
USING (true)
WITH CHECK (true);

-- Document Comments
ALTER TABLE document_comments ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own comments"
ON document_comments
FOR ALL
TO authenticated
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can read all comments"
ON document_comments
FOR SELECT
TO authenticated
USING (true);

-- Real-time Notifications
ALTER TABLE realtime_notifications ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own notifications"
ON realtime_notifications
FOR ALL
TO authenticated
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Create a scheduled job to clean up old data (if pg_cron is available)
-- This would typically be set up separately in production
-- SELECT cron.schedule('cleanup-presence', '*/5 * * * *', 'SELECT cleanup_old_presence();');
-- SELECT cron.schedule('cleanup-sessions', '0 * * * *', 'SELECT cleanup_old_sessions();');
-- SELECT cron.schedule('cleanup-conversations', '0 2 * * *', 'SELECT cleanup_old_conversations();');
