import React, { useEffect, useState } from 'react';
import { ComprehensiveSystemFixer } from '../utils/comprehensive-system-fixer';
import { toast } from 'sonner';

interface SystemInitializerProps {
  children: React.ReactNode;
}

export const SystemInitializer: React.FC<SystemInitializerProps> = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeSystem = async () => {
      try {
        console.log('🚀 Initializing system fixes...');
        
        const fixer = ComprehensiveSystemFixer.getInstance();
        const result = await fixer.initializeSystemFixes();
        
        if (result.success) {
          console.log('✅ System initialization completed successfully');
          console.log('📋 Fix results:', result.results);
          
          toast.success('System Ready', {
            description: `System initialized successfully with ${result.results.length} fixes applied.`,
            duration: 3000
          });
          
          setIsInitialized(true);
        } else {
          console.error('❌ System initialization failed');
          setError('System initialization failed. Some features may not work correctly.');
          
          toast.error('System Warning', {
            description: 'Some system fixes failed. The app may have limited functionality.',
            duration: 5000
          });
        }
      } catch (error) {
        console.error('💥 Critical system initialization error:', error);
        setError('Critical system error occurred during initialization.');
        
        toast.error('Critical Error', {
          description: 'System initialization failed. Please refresh the page.',
          duration: 10000
        });
      } finally {
        setIsLoading(false);
      }
    };

    // Run initialization after a short delay to ensure all dependencies are loaded
    const timer = setTimeout(initializeSystem, 1000);
    
    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-gray-800">Initializing System</h2>
            <p className="text-gray-600">Setting up authentication, database, and security features...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error && !isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-pink-100">
        <div className="text-center space-y-4 max-w-md mx-auto p-6">
          <div className="text-red-600 text-6xl mb-4">⚠️</div>
          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-red-800">System Error</h2>
            <p className="text-red-600">{error}</p>
            <button 
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Refresh Page
            </button>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default SystemInitializer;