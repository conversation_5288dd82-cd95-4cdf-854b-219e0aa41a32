import { supabase } from '@/integrations/supabase/client';

export class TimeLogsSchemaFixer {
  async checkCurrentSchema(): Promise<{ success: boolean; message: string; details: string[] }> {
    const details: string[] = [];
    
    try {
      details.push('Checking time_logs table schema...');
      
      // Check if table exists
      const { data: tableExists, error: tableError } = await supabase
        .rpc('exec_sql', {
          sql: `
            SELECT EXISTS (
              SELECT 1 FROM information_schema.tables 
              WHERE table_schema = 'public' 
              AND table_name = 'time_logs'
            ) as table_exists;
          `
        });
      
      if (tableError) {
        details.push(`Error checking table existence: ${tableError.message}`);
        return {
          success: false,
          message: 'Failed to check table existence',
          details
        };
      }
      
      if (!tableExists || !tableExists[0]?.table_exists) {
        details.push('❌ time_logs table does not exist');
        return {
          success: false,
          message: 'time_logs table does not exist',
          details
        };
      }
      
      details.push('✓ time_logs table exists');
      
      // Check current columns
      const { data: columns, error: columnsError } = await supabase
        .rpc('exec_sql', {
          sql: `
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'time_logs'
            ORDER BY ordinal_position;
          `
        });
      
      if (columnsError) {
        details.push(`Error checking columns: ${columnsError.message}`);
        return {
          success: false,
          message: 'Failed to check table columns',
          details
        };
      }
      
      details.push('Current columns:');
      const columnNames = columns?.map(col => col.column_name) || [];
      columnNames.forEach(name => details.push(`  - ${name}`));
      
      // Check for expected columns
      const hasClockIn = columnNames.includes('clock_in');
      const hasClockOut = columnNames.includes('clock_out');
      const hasClockInTime = columnNames.includes('clock_in_time');
      const hasClockOutTime = columnNames.includes('clock_out_time');
      
      details.push('');
      details.push('Column analysis:');
      details.push(`  - clock_in: ${hasClockIn ? '✓' : '❌'}`);
      details.push(`  - clock_out: ${hasClockOut ? '✓' : '❌'}`);
      details.push(`  - clock_in_time: ${hasClockInTime ? '✓' : '❌'}`);
      details.push(`  - clock_out_time: ${hasClockOutTime ? '✓' : '❌'}`);
      
      if (hasClockIn && hasClockOut) {
        return {
          success: true,
          message: 'time_logs table has correct column names',
          details
        };
      } else if (hasClockInTime && !hasClockIn) {
        return {
          success: false,
          message: 'time_logs table needs column renaming (clock_in_time -> clock_in)',
          details
        };
      } else {
        return {
          success: false,
          message: 'time_logs table has missing or incorrect columns',
          details
        };
      }
      
    } catch (error) {
      details.push(`Schema check error: ${error}`);
      return {
        success: false,
        message: `Schema check failed: ${error}`,
        details
      };
    }
  }

  async fixTimeLogsSchema(): Promise<{ success: boolean; message: string; details: string[] }> {
    const details: string[] = [];
    
    try {
      details.push('Starting time_logs schema fix...');
      
      // First check current schema
      const schemaCheck = await this.checkCurrentSchema();
      details.push(...schemaCheck.details);
      
      if (schemaCheck.success) {
        details.push('✓ Schema is already correct');
        return {
          success: true,
          message: 'time_logs schema is already correct',
          details
        };
      }
      
      details.push('');
      details.push('Applying schema fixes...');
      
      // Fix the schema
      const { error: fixError } = await supabase.rpc('exec_sql', {
        sql: `
          -- Fix time_logs table schema
          DO $$
          BEGIN
            -- Check if table exists
            IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'time_logs' AND table_schema = 'public') THEN
              
              -- Add clock_out column if missing
              IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'time_logs' AND column_name = 'clock_out') THEN
                -- Check if clock_out_time exists and rename it
                IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'time_logs' AND column_name = 'clock_out_time') THEN
                  ALTER TABLE public.time_logs RENAME COLUMN clock_out_time TO clock_out;
                  RAISE NOTICE 'Renamed clock_out_time to clock_out';
                ELSE
                  -- Add new clock_out column
                  ALTER TABLE public.time_logs ADD COLUMN clock_out TIMESTAMP WITH TIME ZONE;
                  RAISE NOTICE 'Added clock_out column';
                END IF;
              END IF;
              
              -- Add clock_in column if missing
              IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'time_logs' AND column_name = 'clock_in') THEN
                -- Check if clock_in_time exists and rename it
                IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'time_logs' AND column_name = 'clock_in_time') THEN
                  ALTER TABLE public.time_logs RENAME COLUMN clock_in_time TO clock_in;
                  RAISE NOTICE 'Renamed clock_in_time to clock_in';
                ELSE
                  -- Add new clock_in column
                  ALTER TABLE public.time_logs ADD COLUMN clock_in TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW();
                  RAISE NOTICE 'Added clock_in column';
                END IF;
              END IF;
              
              -- Ensure user_id column exists and has proper reference
              IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'time_logs' AND column_name = 'user_id') THEN
                ALTER TABLE public.time_logs ADD COLUMN user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;
                RAISE NOTICE 'Added user_id column';
              END IF;
              
              -- Add other commonly needed columns if missing
              IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'time_logs' AND column_name = 'location_data') THEN
                ALTER TABLE public.time_logs ADD COLUMN location_data JSONB;
                RAISE NOTICE 'Added location_data column';
              END IF;
              
              IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'time_logs' AND column_name = 'device_info') THEN
                ALTER TABLE public.time_logs ADD COLUMN device_info JSONB;
                RAISE NOTICE 'Added device_info column';
              END IF;
              
              IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'time_logs' AND column_name = 'notes') THEN
                ALTER TABLE public.time_logs ADD COLUMN notes TEXT;
                RAISE NOTICE 'Added notes column';
              END IF;
              
              IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'time_logs' AND column_name = 'status') THEN
                ALTER TABLE public.time_logs ADD COLUMN status TEXT DEFAULT 'clocked_in';
                RAISE NOTICE 'Added status column';
              END IF;
              
              -- Update indexes
              DROP INDEX IF EXISTS idx_time_logs_clock_in_time;
              CREATE INDEX IF NOT EXISTS idx_time_logs_clock_in ON public.time_logs(clock_in);
              CREATE INDEX IF NOT EXISTS idx_time_logs_user_id ON public.time_logs(user_id);
              CREATE INDEX IF NOT EXISTS idx_time_logs_status ON public.time_logs(status);
              
              -- Enable RLS if not already enabled
              ALTER TABLE public.time_logs ENABLE ROW LEVEL SECURITY;
              
              -- Create policies if they don't exist
              DROP POLICY IF EXISTS "Users can view their own time logs" ON public.time_logs;
              DROP POLICY IF EXISTS "Users can manage their own time logs" ON public.time_logs;
              
              CREATE POLICY "Users can view their own time logs" 
              ON public.time_logs 
              FOR SELECT 
              TO authenticated 
              USING (user_id = auth.uid());
              
              CREATE POLICY "Users can manage their own time logs" 
              ON public.time_logs 
              FOR ALL 
              TO authenticated 
              USING (user_id = auth.uid())
              WITH CHECK (user_id = auth.uid());
              
              RAISE NOTICE 'time_logs schema fix completed successfully';
              
            ELSE
              -- Create table if it doesn't exist
              CREATE TABLE public.time_logs (
                id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
                clock_in TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                clock_out TIMESTAMP WITH TIME ZONE,
                location_data JSONB,
                device_info JSONB,
                notes TEXT,
                status TEXT DEFAULT 'clocked_in',
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
              );
              
              -- Enable RLS
              ALTER TABLE public.time_logs ENABLE ROW LEVEL SECURITY;
              
              -- Create policies
              CREATE POLICY "Users can view their own time logs" 
              ON public.time_logs 
              FOR SELECT 
              TO authenticated 
              USING (user_id = auth.uid());
              
              CREATE POLICY "Users can manage their own time logs" 
              ON public.time_logs 
              FOR ALL 
              TO authenticated 
              USING (user_id = auth.uid())
              WITH CHECK (user_id = auth.uid());
              
              -- Create indexes
              CREATE INDEX idx_time_logs_user_id ON public.time_logs(user_id);
              CREATE INDEX idx_time_logs_clock_in ON public.time_logs(clock_in);
              CREATE INDEX idx_time_logs_status ON public.time_logs(status);
              
              RAISE NOTICE 'time_logs table created successfully';
            END IF;
          END
          $$;
        `
      });
      
      if (fixError) {
        details.push(`❌ Schema fix failed: ${fixError.message}`);
        return {
          success: false,
          message: `Schema fix failed: ${fixError.message}`,
          details
        };
      }
      
      details.push('✓ Schema fix completed');
      
      // Verify the fix
      const verifyCheck = await this.checkCurrentSchema();
      details.push('');
      details.push('Verification:');
      details.push(...verifyCheck.details);
      
      return {
        success: verifyCheck.success,
        message: verifyCheck.success ? 'time_logs schema fixed successfully' : 'Schema fix verification failed',
        details
      };
      
    } catch (error) {
      details.push(`❌ Schema fix error: ${error}`);
      return {
        success: false,
        message: `Schema fix failed: ${error}`,
        details
      };
    }
  }

  async testTimeLogsQueries(): Promise<{ success: boolean; message: string; details: string[] }> {
    const details: string[] = [];
    
    try {
      details.push('Testing time_logs queries...');
      
      // Test basic select
      const { data: selectTest, error: selectError } = await supabase
        .from('time_logs')
        .select('id, user_id, clock_in, clock_out')
        .limit(1);
      
      if (selectError) {
        details.push(`❌ Select test failed: ${selectError.message}`);
        return {
          success: false,
          message: 'Query test failed',
          details
        };
      }
      
      details.push('✓ Basic select query works');
      
      // Test the specific query that was failing
      const { data: sessionTest, error: sessionError } = await supabase
        .from('time_logs')
        .select('*')
        .eq('user_id', '00000000-0000-0000-0000-000000000000') // dummy UUID
        .is('clock_out', null)
        .order('clock_in', { ascending: false })
        .limit(1);
      
      if (sessionError) {
        details.push(`❌ Session query test failed: ${sessionError.message}`);
        return {
          success: false,
          message: 'Session query test failed',
          details
        };
      }
      
      details.push('✓ Session query (clock_out is null) works');
      
      return {
        success: true,
        message: 'All time_logs queries working correctly',
        details
      };
      
    } catch (error) {
      details.push(`❌ Query test error: ${error}`);
      return {
        success: false,
        message: `Query test failed: ${error}`,
        details
      };
    }
  }
}

export const timeLogsSchemaFixer = new TimeLogsSchemaFixer();