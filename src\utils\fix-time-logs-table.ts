/**
 * Fix Time Logs Table
 * Utility to create and fix the time_logs table that's causing 400 errors and clock in failures
 */

import { supabase } from '@/integrations/supabase/client';

export interface TimeLogsFixResult {
  success: boolean;
  message: string;
  details?: string[];
}

export class TimeLogsTableFixer {
  private static instance: TimeLogsTableFixer;
  private isFixed = false;

  static getInstance(): TimeLogsTableFixer {
    if (!TimeLogsTableFixer.instance) {
      TimeLogsTableFixer.instance = new TimeLogsTableFixer();
    }
    return TimeLogsTableFixer.instance;
  }

  /**
   * Check if time_logs table exists and is accessible
   */
  async checkTimeLogsTable(): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('time_logs')
        .select('id')
        .limit(1);

      if (error) {
        console.log('❌ time_logs table check failed:', error.message);
        return false;
      }

      console.log('✅ time_logs table exists and is accessible');
      return true;
    } catch (error) {
      console.log('❌ time_logs table check error:', error);
      return false;
    }
  }

  /**
   * Test clock in functionality
   */
  async testClockInFunction(): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🧪 Testing clock in function...');

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return { success: false, error: 'No authenticated user' };
      }

      // Test the clock_in RPC function
      const { data, error } = await supabase.rpc('clock_in', {
        p_user_id: user.id,
        p_location_data: { test: true },
        p_device_info: { browser: 'test' },
        p_notes: 'Test clock in'
      });

      if (error) {
        // If error is about existing active log, that's actually good - function exists
        if (error.message.includes('already has an active time log')) {
          console.log('✅ Clock in function exists (user already clocked in)');
          return { success: true };
        }
        console.error('❌ Clock in function error:', error.message);
        return { success: false, error: error.message };
      }

      console.log('✅ Clock in function works, returned ID:', data);
      return { success: true };

    } catch (error: any) {
      console.error('❌ Test clock in failed:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create time_logs table using RPC function
   */
  async createTimeLogsTable(): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🔧 Creating time_logs table...');

      // Create the table using RPC
      const { error } = await supabase.rpc('exec_sql', {
        sql: `
          -- Create time_logs table
          CREATE TABLE IF NOT EXISTS public.time_logs (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
            clock_in_time TIMESTAMP WITH TIME ZONE NOT NULL,
            clock_out_time TIMESTAMP WITH TIME ZONE,
            break_start_time TIMESTAMP WITH TIME ZONE,
            break_end_time TIMESTAMP WITH TIME ZONE,
            total_hours NUMERIC(5,2) DEFAULT 0,
            break_hours NUMERIC(5,2) DEFAULT 0,
            overtime_hours NUMERIC(5,2) DEFAULT 0,
            status TEXT DEFAULT 'clocked_in' CHECK (status IN ('clocked_in', 'on_break', 'clocked_out', 'overtime')),
            location_data JSONB,
            device_info JSONB,
            ip_address INET,
            notes TEXT,
            approved_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
            approved_at TIMESTAMP WITH TIME ZONE,
            is_approved BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );

          -- Enable RLS
          ALTER TABLE public.time_logs ENABLE ROW LEVEL SECURITY;

          -- Create basic policies
          DROP POLICY IF EXISTS "Users can view their own time logs" ON public.time_logs;
          DROP POLICY IF EXISTS "Users can manage their own time logs" ON public.time_logs;

          CREATE POLICY "Users can view their own time logs" 
          ON public.time_logs 
          FOR SELECT 
          TO authenticated 
          USING (user_id = auth.uid());

          CREATE POLICY "Users can manage their own time logs" 
          ON public.time_logs 
          FOR ALL 
          TO authenticated 
          USING (user_id = auth.uid())
          WITH CHECK (user_id = auth.uid());

          -- Create indexes
          CREATE INDEX IF NOT EXISTS idx_time_logs_user_id ON public.time_logs(user_id);
          CREATE INDEX IF NOT EXISTS idx_time_logs_clock_in_time ON public.time_logs(clock_in_time);
          CREATE INDEX IF NOT EXISTS idx_time_logs_status ON public.time_logs(status);
        `
      });

      if (error) {
        console.error('❌ Failed to create time_logs table:', error.message);
        return { success: false, error: error.message };
      }

      console.log('✅ time_logs table created successfully');
      return { success: true };

    } catch (error: any) {
      console.error('❌ Error creating time_logs table:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create clock in/out functions
   */
  async createClockFunctions(): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🔧 Creating clock in/out functions...');

      const { error } = await supabase.rpc('exec_sql', {
        sql: `
          -- Create function to clock in
          CREATE OR REPLACE FUNCTION public.clock_in(
              p_user_id UUID,
              p_location_data JSONB DEFAULT NULL,
              p_device_info JSONB DEFAULT NULL,
              p_ip_address INET DEFAULT NULL,
              p_notes TEXT DEFAULT NULL
          )
          RETURNS UUID
          LANGUAGE plpgsql
          SECURITY DEFINER
          AS $$
          DECLARE
              v_time_log_id UUID;
              v_active_log_exists BOOLEAN;
          BEGIN
              -- Check if user already has an active time log
              SELECT EXISTS(
                  SELECT 1 FROM public.time_logs 
                  WHERE user_id = p_user_id 
                  AND clock_out_time IS NULL
              ) INTO v_active_log_exists;
              
              IF v_active_log_exists THEN
                  RAISE EXCEPTION 'User already has an active time log. Please clock out first.';
              END IF;
              
              -- Create new time log
              INSERT INTO public.time_logs (
                  user_id,
                  clock_in_time,
                  location_data,
                  device_info,
                  ip_address,
                  notes,
                  status
              ) VALUES (
                  p_user_id,
                  NOW(),
                  p_location_data,
                  p_device_info,
                  p_ip_address,
                  p_notes,
                  'clocked_in'
              ) RETURNING id INTO v_time_log_id;
              
              RETURN v_time_log_id;
          END;
          $$;

          -- Create function to clock out
          CREATE OR REPLACE FUNCTION public.clock_out(
              p_user_id UUID,
              p_notes TEXT DEFAULT NULL
          )
          RETURNS UUID
          LANGUAGE plpgsql
          SECURITY DEFINER
          AS $$
          DECLARE
              v_time_log_id UUID;
          BEGIN
              -- Update the active time log
              UPDATE public.time_logs 
              SET 
                  clock_out_time = NOW(),
                  notes = COALESCE(p_notes, notes),
                  status = 'clocked_out',
                  updated_at = NOW()
              WHERE user_id = p_user_id 
              AND clock_out_time IS NULL
              RETURNING id INTO v_time_log_id;
              
              IF v_time_log_id IS NULL THEN
                  RAISE EXCEPTION 'No active time log found for user.';
              END IF;
              
              RETURN v_time_log_id;
          END;
          $$;

          -- Grant permissions
          GRANT EXECUTE ON FUNCTION public.clock_in TO authenticated;
          GRANT EXECUTE ON FUNCTION public.clock_out TO authenticated;
        `
      });

      if (error) {
        console.error('❌ Failed to create clock functions:', error.message);
        return { success: false, error: error.message };
      }

      console.log('✅ Clock functions created successfully');
      return { success: true };

    } catch (error: any) {
      console.error('❌ Error creating clock functions:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Fix all time_logs-related issues
   */
  async fixAllIssues(): Promise<{ success: boolean; results: string[] }> {
    const results: string[] = [];

    try {
      console.log('🔧 Starting time_logs table fixes...');

      // Step 1: Check if table exists
      const tableExists = await this.checkTimeLogsTable();

      if (!tableExists) {
        results.push('time_logs table does not exist');

        // Step 2: Create table
        const createResult = await this.createTimeLogsTable();
        if (createResult.success) {
          results.push('time_logs table created successfully');
        } else {
          results.push(`Failed to create time_logs table: ${createResult.error}`);
          return { success: false, results };
        }

        // Step 3: Create functions
        const functionsResult = await this.createClockFunctions();
        if (functionsResult.success) {
          results.push('Clock in/out functions created successfully');
        } else {
          results.push(`Failed to create functions: ${functionsResult.error}`);
        }
      } else {
        results.push('time_logs table already exists');
      }

      // Step 4: Test clock in function
      const testResult = await this.testClockInFunction();
      if (testResult.success) {
        results.push('Clock in function works correctly');
      } else {
        results.push(`Clock in function test failed: ${testResult.error}`);
      }

      console.log('✅ time_logs table fixes completed');
      this.isFixed = true;
      return { success: true, results };

    } catch (error: any) {
      console.error('❌ Error during time_logs fixes:', error.message);
      results.push(`Error during fixes: ${error.message}`);
      return { success: false, results };
    }
  }

  /**
   * Get the current fix status
   */
  isTableFixed(): boolean {
    return this.isFixed;
  }

  /**
   * Reset the fix status (for testing)
   */
  resetFixStatus(): void {
    this.isFixed = false;
  }
}

// Export singleton instance
export const timeLogsTableFixer = TimeLogsTableFixer.getInstance();

// Export utility functions
export const fixTimeLogsTable = () => timeLogsTableFixer.fixAllIssues();
export const checkTimeLogsTable = () => timeLogsTableFixer.checkTimeLogsTable();
