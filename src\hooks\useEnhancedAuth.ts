/**
 * Enhanced Authentication Hook
 * Fixes authentication state and profile synchronization issues
 */

import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import type { User } from '@supabase/supabase-js';

export interface UserProfile {
  id: string;
  user_id: string;
  full_name: string;
  email: string;
  role: string;
  account_type: string;
  status: string;
  department_id?: string;
  avatar_url?: string;
  phone?: string;
  position?: string;
  hire_date?: string;
  last_login?: string;
  created_at?: string;
  updated_at?: string;
}

export interface AuthState {
  user: User | null;
  userProfile: UserProfile | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  initialized: boolean;
  error: string | null;
}

export const useEnhancedAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    userProfile: null,
    isAuthenticated: false,
    isLoading: true,
    initialized: false,
    error: null
  });

  useEffect(() => {
    let mounted = true;

    const initializeAuth = async () => {
      try {
        console.log('🔐 Initializing enhanced authentication...');

        // Get current session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {
          console.error('❌ Session error:', sessionError);
          if (mounted) {
            setAuthState({
              user: null,
              userProfile: null,
              isAuthenticated: false,
              isLoading: false,
              initialized: true,
              error: sessionError.message
            });
          }
          return;
        }

        if (!session?.user) {
          console.log('ℹ️ No active session');
          if (mounted) {
            setAuthState({
              user: null,
              userProfile: null,
              isAuthenticated: false,
              isLoading: false,
              initialized: true,
              error: null
            });
          }
          return;
        }

        // Get or create user profile
        const profile = await getOrCreateUserProfile(session.user);
        
        if (mounted) {
          setAuthState({
            user: session.user,
            userProfile: profile,
            isAuthenticated: true,
            isLoading: false,
            initialized: true,
            error: null
          });
        }

        console.log('✅ Enhanced authentication initialized');
      } catch (error: any) {
        console.error('❌ Auth initialization failed:', error);
        if (mounted) {
          setAuthState({
            user: null,
            userProfile: null,
            isAuthenticated: false,
            isLoading: false,
            initialized: true,
            error: error.message
          });
        }
      }
    };

    // Initialize auth
    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔐 Auth state changed:', event);

        if (!mounted) return;

        if (event === 'SIGNED_OUT' || !session?.user) {
          setAuthState({
            user: null,
            userProfile: null,
            isAuthenticated: false,
            isLoading: false,
            initialized: true,
            error: null
          });
          return;
        }

        if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
          try {
            const profile = await getOrCreateUserProfile(session.user);
            
            setAuthState({
              user: session.user,
              userProfile: profile,
              isAuthenticated: true,
              isLoading: false,
              initialized: true,
              error: null
            });
          } catch (error: any) {
            console.error('❌ Profile sync failed:', error);
            setAuthState(prev => ({
              ...prev,
              error: error.message,
              isLoading: false
            }));
          }
        }
      }
    );

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, []);

  return authState;
};

/**
 * Get or create user profile with proper data synchronization
 */
async function getOrCreateUserProfile(user: User): Promise<UserProfile | null> {
  try {
    console.log('👤 Getting/creating profile for user:', user.id);

    // First, try to get existing profile
    const { data: existingProfile, error: fetchError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .maybeSingle();

    if (fetchError && !fetchError.message.includes('No rows')) {
      console.error('❌ Profile fetch error:', fetchError);
      throw fetchError;
    }

    const userMetadata = user.user_metadata || {};
    
    // Prepare profile data with correct role from metadata
    const profileData: Partial<UserProfile> = {
      id: user.id,
      user_id: user.id,
      full_name: userMetadata.full_name || user.email?.split('@')[0] || 'Unknown User',
      email: user.email || '',
      role: userMetadata.role || 'staff',
      account_type: userMetadata.account_type || userMetadata.role || 'staff',
      status: 'active',
      department_id: userMetadata.department_id || null,
      phone: userMetadata.phone || null,
      position: userMetadata.position || null,
      last_login: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // If profile exists but has inconsistent data, update it
    if (existingProfile) {
      const needsUpdate = 
        existingProfile.user_id !== user.id ||
        existingProfile.role !== profileData.role ||
        existingProfile.account_type !== profileData.account_type ||
        existingProfile.email !== profileData.email;

      if (needsUpdate) {
        console.log('🔄 Updating profile with correct data...');
        
        const { data: updatedProfile, error: updateError } = await supabase
          .from('profiles')
          .update(profileData)
          .eq('id', user.id)
          .select()
          .single();

        if (updateError) {
          console.error('❌ Profile update failed:', updateError);
          throw updateError;
        }

        console.log('✅ Profile updated successfully');
        return updatedProfile;
      } else {
        // Update last login
        await supabase
          .from('profiles')
          .update({ last_login: new Date().toISOString() })
          .eq('id', user.id);

        return existingProfile;
      }
    } else {
      // Create new profile
      console.log('➕ Creating new profile...');
      
      const { data: newProfile, error: createError } = await supabase
        .from('profiles')
        .insert(profileData)
        .select()
        .single();

      if (createError) {
        console.error('❌ Profile creation failed:', createError);
        throw createError;
      }

      console.log('✅ Profile created successfully');
      return newProfile;
    }
  } catch (error: any) {
    console.error('❌ Profile operation failed:', error);
    throw error;
  }
}

/**
 * Sign out user
 */
export const signOut = async (): Promise<{ success: boolean; error?: string }> => {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) {
      return { success: false, error: error.message };
    }
    return { success: true };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
};

/**
 * Refresh user profile
 */
export const refreshProfile = async (): Promise<{ success: boolean; error?: string }> => {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error || !user) {
      return { success: false, error: 'User not authenticated' };
    }

    await getOrCreateUserProfile(user);
    return { success: true };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
};
