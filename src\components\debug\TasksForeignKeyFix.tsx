import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, XCircle, Info } from 'lucide-react';
import { TasksForeignKeyFixer } from '@/utils/fix-tasks-foreign-keys';

interface FixResult {
  success: boolean;
  results: string[];
}

interface ConstraintInfo {
  constraint_name: string;
  table_name: string;
  column_name: string;
  foreign_table_name: string;
  foreign_column_name: string;
}

export const TasksForeignKeyFix: React.FC = () => {
  const [isFixing, setIsFixing] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [fixResult, setFixResult] = useState<FixResult | null>(null);
  const [constraints, setConstraints] = useState<ConstraintInfo[]>([]);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  const handleCheckConstraints = async () => {
    setIsChecking(true);
    try {
      const result = await TasksForeignKeyFixer.checkCurrentConstraints();
      if (result.success) {
        setConstraints(result.constraints);
        setLastChecked(new Date());
      }
    } catch (error) {
      console.error('Error checking constraints:', error);
    } finally {
      setIsChecking(false);
    }
  };

  const handleFixForeignKeys = async () => {
    setIsFixing(true);
    setFixResult(null);
    
    try {
      const result = await TasksForeignKeyFixer.fixTasksForeignKeys();
      setFixResult(result);
      
      // Refresh constraints after fix
      if (result.success) {
        await handleCheckConstraints();
      }
    } catch (error: any) {
      setFixResult({
        success: false,
        results: [`Error: ${error.message}`]
      });
    } finally {
      setIsFixing(false);
    }
  };

  const getConstraintStatus = (constraintName: string) => {
    const constraint = constraints.find(c => c.constraint_name === constraintName);
    if (!constraint) return { status: 'missing', color: 'text-red-600' };
    
    const isCorrect = constraint.foreign_table_name === 'profiles';
    return {
      status: isCorrect ? 'correct' : 'incorrect',
      color: isCorrect ? 'text-green-600' : 'text-orange-600',
      details: `${constraint.foreign_table_name}.${constraint.foreign_column_name}`
    };
  };

  const assignedToStatus = getConstraintStatus('tasks_assigned_to_id_fkey');
  const createdByStatus = getConstraintStatus('tasks_created_by_fkey');

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Info className="h-5 w-5" />
          Tasks Foreign Key Fixer
        </CardTitle>
        <CardDescription>
          Fix foreign key constraints for tasks table to reference profiles instead of auth.users
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Status */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Current Foreign Key Status</h3>
            <Button 
              onClick={handleCheckConstraints} 
              disabled={isChecking}
              variant="outline"
              size="sm"
            >
              {isChecking ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Checking...
                </>
              ) : (
                'Check Status'
              )}
            </Button>
          </div>

          {lastChecked && (
            <p className="text-sm text-muted-foreground">
              Last checked: {lastChecked.toLocaleString()}
            </p>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center justify-between">
                <span className="font-medium">tasks_assigned_to_id_fkey</span>
                <span className={`text-sm ${assignedToStatus.color}`}>
                  {assignedToStatus.status}
                </span>
              </div>
              {assignedToStatus.details && (
                <p className="text-sm text-muted-foreground mt-1">
                  References: {assignedToStatus.details}
                </p>
              )}
            </div>

            <div className="p-4 border rounded-lg">
              <div className="flex items-center justify-between">
                <span className="font-medium">tasks_created_by_fkey</span>
                <span className={`text-sm ${createdByStatus.color}`}>
                  {createdByStatus.status}
                </span>
              </div>
              {createdByStatus.details && (
                <p className="text-sm text-muted-foreground mt-1">
                  References: {createdByStatus.details}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Fix Action */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Fix Foreign Keys</h3>
          <p className="text-sm text-muted-foreground">
            This will drop the existing foreign key constraints that reference auth.users 
            and recreate them to reference the profiles table instead.
          </p>
          
          <Button 
            onClick={handleFixForeignKeys} 
            disabled={isFixing}
            className="w-full"
          >
            {isFixing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Fixing Foreign Keys...
              </>
            ) : (
              'Fix Foreign Key Constraints'
            )}
          </Button>
        </div>

        {/* Results */}
        {fixResult && (
          <div className="space-y-4">
            <Alert className={fixResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
              <div className="flex items-center gap-2">
                {fixResult.success ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
                <AlertDescription className={fixResult.success ? 'text-green-800' : 'text-red-800'}>
                  {fixResult.success ? 'Foreign key fixes completed successfully!' : 'Foreign key fixes failed!'}
                </AlertDescription>
              </div>
            </Alert>

            {fixResult.results.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium">Fix Results:</h4>
                <div className="bg-gray-50 p-4 rounded-lg max-h-60 overflow-y-auto">
                  {fixResult.results.map((result, index) => (
                    <div key={index} className="text-sm font-mono mb-1">
                      {result}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* All Constraints Details */}
        {constraints.length > 0 && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">All Task Foreign Key Constraints</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="space-y-2">
                {constraints.map((constraint, index) => (
                  <div key={index} className="text-sm font-mono">
                    <span className="font-semibold">{constraint.constraint_name}</span>: 
                    {constraint.table_name}.{constraint.column_name} → 
                    {constraint.foreign_table_name}.{constraint.foreign_column_name}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TasksForeignKeyFix;