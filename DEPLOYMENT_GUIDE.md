# 🚀 Enhanced Features Deployment Guide

## Prerequisites
- Supabase project: `dvflgnqwbsjityrowatf`
- OpenAI API key
- Admin access to Supabase dashboard

---

## 1. 📊 Database Migration

### Option A: Via Supabase Dashboard (Recommended)
1. Go to [Supabase Dashboard](https://supabase.com/dashboard/project/dvflgnqwbsjityrowatf)
2. Navigate to **SQL Editor**
3. Copy and paste the migration from: `supabase/migrations/20241221_langchain_realtime_features.sql`
4. Click **Run** to execute the migration

### Option B: Via CLI (if logged in)
```bash
npx supabase login
npx supabase link --project-ref dvflgnqwbsjityrowatf
npx supabase db push
```

### Migration Summary
The migration will create:
- ✅ 7 new tables for LangChain and real-time features
- ✅ Vector search capabilities with PostgreSQL vector extension
- ✅ RLS policies for security
- ✅ Optimized indexes for performance
- ✅ Utility functions for cleanup and maintenance

---

## 2. 🔧 Deploy Edge Functions

### Deploy via Supabase Dashboard
1. Go to **Edge Functions** in your Supabase dashboard
2. Create 3 new functions:

#### Function 1: `generate-embeddings`
- **Name**: `generate-embeddings`
- **Code**: Copy from `supabase/functions/generate-embeddings/index.ts`

#### Function 2: `analyze-agent-query`
- **Name**: `analyze-agent-query`
- **Code**: Copy from `supabase/functions/analyze-agent-query/index.ts`

#### Function 3: `generate-agent-response`
- **Name**: `generate-agent-response`
- **Code**: Copy from `supabase/functions/generate-agent-response/index.ts`

### Deploy via CLI (if logged in)
```bash
npx supabase functions deploy generate-embeddings
npx supabase functions deploy analyze-agent-query
npx supabase functions deploy generate-agent-response
```

---

## 3. 🔑 Configure API Keys

### Add OpenAI API Key to Database
Run this SQL in your Supabase SQL Editor:

```sql
-- Insert OpenAI API key
INSERT INTO api_keys (provider, api_key, is_active, created_at) 
VALUES (
  'openai', 
  'your-actual-openai-api-key-here', 
  true, 
  NOW()
) ON CONFLICT (provider) DO UPDATE SET 
  api_key = EXCLUDED.api_key,
  is_active = EXCLUDED.is_active,
  updated_at = NOW();
```

**⚠️ Important**: Replace `'your-actual-openai-api-key-here'` with your real OpenAI API key.

---

## 4. 🎯 Add Enhanced AI Route

### Update App Routing
The enhanced AI page is already created at `src/pages/ai/EnhancedAIPage.tsx`. 

To access it, add this route to your routing configuration:

```tsx
// In your routing file (likely in UnifiedDashboardLayout.tsx)
import EnhancedAIPage from '@/pages/ai/EnhancedAIPage';

// Add this route
<Route path="/ai/enhanced" element={<EnhancedAIPage />} />
```

---

## 5. ✅ Verification Steps

### Test Database Migration
Run this query in SQL Editor to verify tables were created:
```sql
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE '%langchain%' 
OR table_name LIKE '%collaborative%' 
OR table_name LIKE '%presence%';
```

Expected results:
- `langchain_conversations`
- `langchain_documents`
- `langchain_document_metadata`
- `user_presence`
- `collaborative_sessions`
- `document_comments`
- `realtime_notifications`

### Test Edge Functions
1. Go to **Edge Functions** in Supabase dashboard
2. Test each function with sample data
3. Check logs for any errors

### Test API Key Configuration
Run this query to verify API key was added:
```sql
SELECT provider, is_active, created_at 
FROM api_keys 
WHERE provider = 'openai';
```

---

## 6. 🎨 Access the Demo

### Navigate to Enhanced AI Page
1. Start your development server: `npm run dev`
2. Navigate to: `http://localhost:8084/ai/enhanced`
3. You should see the enhanced AI and collaboration features

### Features to Test
- ✅ **AI Assistant**: Ask complex questions, see RAG responses
- ✅ **Real-time Collaboration**: Edit documents with multiple users
- ✅ **Presence Indicators**: See who's online and typing
- ✅ **Comments**: Add comments to documents
- ✅ **Vector Search**: Search through knowledge base

---

## 7. 🔧 Troubleshooting

### Common Issues

#### Migration Fails
- **Issue**: Vector extension not enabled
- **Solution**: Run `CREATE EXTENSION IF NOT EXISTS vector;` first

#### Edge Functions Don't Deploy
- **Issue**: Missing dependencies
- **Solution**: Ensure all imports are correct in function files

#### API Key Not Working
- **Issue**: Invalid or missing OpenAI API key
- **Solution**: Verify key is correct and has sufficient credits

#### Features Not Loading
- **Issue**: LangChainRealtimeProvider not initialized
- **Solution**: Ensure App.tsx includes the provider wrapper

### Debug Commands
```sql
-- Check if vector extension is enabled
SELECT * FROM pg_extension WHERE extname = 'vector';

-- Check API keys
SELECT provider, is_active FROM api_keys;

-- Check table creation
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' AND table_name LIKE '%langchain%';
```

---

## 8. 🎯 Next Steps After Deployment

### Immediate Actions
1. **Test all features** on the demo page
2. **Add sample documents** to the knowledge base
3. **Invite team members** to test collaboration
4. **Monitor performance** and usage

### Optional Enhancements
1. **Custom AI Agents**: Build domain-specific tools
2. **Mobile Optimization**: Adapt for mobile devices
3. **Analytics Dashboard**: Track usage and performance
4. **Integration Expansion**: Connect more external services

---

## 🎉 Success Indicators

When deployment is successful, you should see:
- ✅ All 7 new tables in your database
- ✅ 3 new edge functions deployed and working
- ✅ OpenAI API key configured and active
- ✅ Enhanced AI page accessible at `/ai/enhanced`
- ✅ Real-time collaboration features working
- ✅ AI assistant responding with RAG capabilities

---

## 📞 Support

If you encounter issues:
1. Check the browser console for errors
2. Review Supabase function logs
3. Verify all environment variables are set
4. Ensure database migration completed successfully

The enhanced features transform CTNL AI Workboard into a world-class enterprise platform with advanced AI and collaboration capabilities!
