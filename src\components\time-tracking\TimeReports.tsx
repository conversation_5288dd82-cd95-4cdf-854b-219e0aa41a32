/**
 * Time Reports Component
 * Displays time tracking analytics and reports
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { BarChart3, TrendingUp, Clock, Calendar, Download, PieChart } from 'lucide-react';

export const TimeReports: React.FC = () => {
  const [reportPeriod, setReportPeriod] = useState('month');
  const [reportType, setReportType] = useState('summary');

  // Mock data for reports
  const weeklyData = [
    { day: 'Mon', hours: 8.5 },
    { day: 'Tue', hours: 7.8 },
    { day: 'Wed', hours: 8.2 },
    { day: 'Thu', hours: 8.0 },
    { day: 'Fri', hours: 7.5 },
    { day: 'Sat', hours: 0 },
    { day: 'Sun', hours: 0 }
  ];

  const projectBreakdown = [
    { project: 'Website Redesign', hours: 32.5, percentage: 45 },
    { project: 'Mobile App', hours: 24.0, percentage: 33 },
    { project: 'Database Migration', hours: 12.5, percentage: 17 },
    { project: 'System Maintenance', hours: 3.5, percentage: 5 }
  ];

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Time Reports & Analytics</span>
          </CardTitle>
          <CardDescription>
            Analyze your time tracking patterns and productivity
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <Select value={reportPeriod} onValueChange={setReportPeriod}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
                <SelectItem value="quarter">This Quarter</SelectItem>
                <SelectItem value="year">This Year</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={reportType} onValueChange={setReportType}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Report Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="summary">Summary</SelectItem>
                <SelectItem value="detailed">Detailed</SelectItem>
                <SelectItem value="project">By Project</SelectItem>
                <SelectItem value="productivity">Productivity</SelectItem>
              </SelectContent>
            </Select>
            
            <Button variant="outline" className="flex items-center space-x-2">
              <Download className="h-4 w-4" />
              <span>Export Report</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Total Hours</p>
                <p className="text-2xl font-bold">168.5h</p>
                <p className="text-xs text-green-600 flex items-center">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +12% vs last month
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Working Days</p>
                <p className="text-2xl font-bold">22</p>
                <p className="text-xs text-gray-500">out of 23 days</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-4 w-4 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Avg Daily</p>
                <p className="text-2xl font-bold">7.7h</p>
                <p className="text-xs text-blue-600">Target: 8h</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <PieChart className="h-4 w-4 text-orange-600" />
              <div>
                <p className="text-sm text-gray-600">Efficiency</p>
                <p className="text-2xl font-bold">94%</p>
                <p className="text-xs text-green-600">Excellent</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Weekly Hours Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Weekly Hours Breakdown</CardTitle>
            <CardDescription>
              Daily hours worked this week
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {weeklyData.map((day) => (
                <div key={day.day} className="flex items-center space-x-3">
                  <div className="w-12 text-sm font-medium">{day.day}</div>
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${(day.hours / 10) * 100}%` }}
                    ></div>
                  </div>
                  <div className="w-12 text-sm text-right">{day.hours}h</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Project Time Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Project Time Distribution</CardTitle>
            <CardDescription>
              Time spent on different projects
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {projectBreakdown.map((project, index) => (
                <div key={project.project} className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="font-medium">{project.project}</span>
                    <span>{project.hours}h ({project.percentage}%)</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        index === 0 ? 'bg-blue-600' :
                        index === 1 ? 'bg-green-600' :
                        index === 2 ? 'bg-purple-600' : 'bg-orange-600'
                      }`}
                      style={{ width: `${project.percentage}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Productivity Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Productivity Insights</CardTitle>
          <CardDescription>
            Analysis of your work patterns and recommendations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="font-semibold text-green-700">Strengths</h3>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Consistent daily hours (7-8.5h range)</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>High completion rate (94%)</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Good work-life balance</span>
                </li>
              </ul>
            </div>
            
            <div className="space-y-4">
              <h3 className="font-semibold text-blue-700">Recommendations</h3>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span>Consider shorter breaks for better focus</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span>Peak productivity: 9-11 AM</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span>Allocate more time to high-priority projects</span>
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Monthly Summary</CardTitle>
          <CardDescription>
            Detailed breakdown of time tracking metrics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-3">
              <h4 className="font-medium">Time Distribution</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Productive Work</span>
                  <span className="font-medium">142.5h (85%)</span>
                </div>
                <div className="flex justify-between">
                  <span>Meetings</span>
                  <span className="font-medium">18.0h (11%)</span>
                </div>
                <div className="flex justify-between">
                  <span>Administrative</span>
                  <span className="font-medium">8.0h (4%)</span>
                </div>
              </div>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-medium">Break Patterns</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Average Break</span>
                  <span className="font-medium">35 minutes</span>
                </div>
                <div className="flex justify-between">
                  <span>Lunch Break</span>
                  <span className="font-medium">45 minutes</span>
                </div>
                <div className="flex justify-between">
                  <span>Short Breaks</span>
                  <span className="font-medium">3-4 per day</span>
                </div>
              </div>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-medium">Attendance</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>On Time</span>
                  <span className="font-medium">20 days (91%)</span>
                </div>
                <div className="flex justify-between">
                  <span>Late Arrival</span>
                  <span className="font-medium">2 days (9%)</span>
                </div>
                <div className="flex justify-between">
                  <span>Early Departure</span>
                  <span className="font-medium">1 day (4%)</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
