/**
 * Safe Type Utilities
 * Provides safe alternatives to TypeScript type assertions that cause parsing errors
 */

// Type assertion helpers to avoid parsing errors
export const safeTypeAssertion = {
  /**
   * Safe keyof assertion for object properties
   */
  asKeyof: <T extends Record<string, any>>(obj: T, key: string): keyof T => {
    return key as keyof T;
  },
  
  /**
   * Safe any type assertion
   */
  asAny: (obj: any): any => {
    return obj;
  },
  
  /**
   * Safe window object assertion
   */
  asWindow: (): any => {
    return typeof window !== 'undefined' ? window : {};
  },
  
  /**
   * Safe HTML element assertion
   */
  asElement: (element: any): HTMLElement => {
    return element;
  },

  /**
   * Safe form element assertion
   */
  asFormElement: (element: any): HTMLFormElement => {
    return element;
  },

  /**
   * Safe input element assertion
   */
  asInputElement: (element: any): HTMLInputElement => {
    return element;
  }
};

// Safe object property access
export const safeAccess = {
  /**
   * Get object property safely
   */
  getProperty: <T extends Record<string, any>>(obj: T, key: string, defaultValue?: any) => {
    return obj && obj[key] !== undefined ? obj[key] : defaultValue;
  },
  
  /**
   * Set object property safely
   */
  setProperty: <T extends Record<string, any>>(obj: T, key: string, value: any) => {
    if (obj) {
      obj[key] = value;
    }
    return obj;
  },

  /**
   * Check if property exists
   */
  hasProperty: <T extends Record<string, any>>(obj: T, key: string): boolean => {
    return obj && key in obj;
  },

  /**
   * Get nested property safely
   */
  getNestedProperty: (obj: any, path: string, defaultValue?: any) => {
    const keys = path.split('.');
    let current = obj;
    
    for (const key of keys) {
      if (current && current[key] !== undefined) {
        current = current[key];
      } else {
        return defaultValue;
      }
    }
    
    return current;
  }
};

// Safe window object access
export const safeWindow = {
  /**
   * Get window property safely
   */
  get: (property: string, defaultValue?: any) => {
    if (typeof window === 'undefined') return defaultValue;
    return safeAccess.getProperty(window, property, defaultValue);
  },
  
  /**
   * Set window property safely
   */
  set: (property: string, value: any) => {
    if (typeof window !== 'undefined') {
      safeAccess.setProperty(window, property, value);
    }
  },
  
  /**
   * Check if window exists
   */
  exists: () => typeof window !== 'undefined',
  
  /**
   * Add event listener safely
   */
  addEventListener: (event: string, handler: EventListener, options?: AddEventListenerOptions) => {
    if (typeof window !== 'undefined') {
      window.addEventListener(event, handler, options);
    }
  },
  
  /**
   * Remove event listener safely
   */
  removeEventListener: (event: string, handler: EventListener, options?: EventListenerOptions) => {
    if (typeof window !== 'undefined') {
      window.removeEventListener(event, handler, options);
    }
  },

  /**
   * Get window location safely
   */
  getLocation: () => {
    if (typeof window !== 'undefined' && window.location) {
      return window.location;
    }
    return {
      href: '',
      pathname: '',
      search: '',
      hash: '',
      reload: () => {},
      assign: () => {},
      replace: () => {}
    };
  }
};

// Safe DOM access
export const safeDom = {
  /**
   * Get element by ID safely
   */
  getElementById: (id: string): HTMLElement | null => {
    if (typeof document === 'undefined') return null;
    return document.getElementById(id);
  },

  /**
   * Query selector safely
   */
  querySelector: (selector: string): Element | null => {
    if (typeof document === 'undefined') return null;
    try {
      return document.querySelector(selector);
    } catch (error) {
      console.warn('Invalid selector:', selector);
      return null;
    }
  },

  /**
   * Query selector all safely
   */
  querySelectorAll: (selector: string): NodeListOf<Element> | [] => {
    if (typeof document === 'undefined') return [] as any;
    try {
      return document.querySelectorAll(selector);
    } catch (error) {
      console.warn('Invalid selector:', selector);
      return [] as any;
    }
  },

  /**
   * Add event listener to document safely
   */
  addEventListener: (event: string, handler: EventListener, options?: AddEventListenerOptions) => {
    if (typeof document !== 'undefined') {
      document.addEventListener(event, handler, options);
    }
  },

  /**
   * Remove event listener from document safely
   */
  removeEventListener: (event: string, handler: EventListener, options?: EventListenerOptions) => {
    if (typeof document !== 'undefined') {
      document.removeEventListener(event, handler, options);
    }
  }
};

// Safe type checking utilities
export const safeTypeCheck = {
  /**
   * Check if value is string
   */
  isString: (value: any): value is string => {
    return typeof value === 'string';
  },

  /**
   * Check if value is number
   */
  isNumber: (value: any): value is number => {
    return typeof value === 'number' && !isNaN(value);
  },

  /**
   * Check if value is boolean
   */
  isBoolean: (value: any): value is boolean => {
    return typeof value === 'boolean';
  },

  /**
   * Check if value is object
   */
  isObject: (value: any): value is object => {
    return value !== null && typeof value === 'object' && !Array.isArray(value);
  },

  /**
   * Check if value is array
   */
  isArray: (value: any): value is any[] => {
    return Array.isArray(value);
  },

  /**
   * Check if value is function
   */
  isFunction: (value: any): value is Function => {
    return typeof value === 'function';
  },

  /**
   * Check if value is null or undefined
   */
  isNullOrUndefined: (value: any): value is null | undefined => {
    return value === null || value === undefined;
  },

  /**
   * Check if value exists (not null or undefined)
   */
  exists: (value: any): boolean => {
    return !safeTypeCheck.isNullOrUndefined(value);
  }
};

// Safe error handling utilities
export const safeError = {
  /**
   * Get error message safely
   */
  getMessage: (error: any): string => {
    if (safeTypeCheck.isString(error)) return error;
    if (error && safeTypeCheck.isString(error.message)) return error.message;
    if (error && safeTypeCheck.isString(error.toString)) return error.toString();
    return 'Unknown error occurred';
  },

  /**
   * Get error stack safely
   */
  getStack: (error: any): string => {
    if (error && safeTypeCheck.isString(error.stack)) return error.stack;
    return '';
  },

  /**
   * Check if error is specific type
   */
  isErrorType: (error: any, type: string): boolean => {
    if (!error) return false;
    if (error.name === type) return true;
    if (error.constructor && error.constructor.name === type) return true;
    return false;
  }
};

// Safe async utilities
export const safeAsync = {
  /**
   * Execute async function with error handling
   */
  execute: async <T>(fn: () => Promise<T>, defaultValue?: T): Promise<T | undefined> => {
    try {
      return await fn();
    } catch (error) {
      console.error('Async execution failed:', safeError.getMessage(error));
      return defaultValue;
    }
  },

  /**
   * Execute with timeout
   */
  executeWithTimeout: async <T>(
    fn: () => Promise<T>, 
    timeoutMs: number, 
    defaultValue?: T
  ): Promise<T | undefined> => {
    try {
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Operation timed out')), timeoutMs);
      });

      return await Promise.race([fn(), timeoutPromise]);
    } catch (error) {
      console.error('Async execution with timeout failed:', safeError.getMessage(error));
      return defaultValue;
    }
  }
};

// Export all utilities as a single object for convenience
export const SafeUtils = {
  typeAssertion: safeTypeAssertion,
  access: safeAccess,
  window: safeWindow,
  dom: safeDom,
  typeCheck: safeTypeCheck,
  error: safeError,
  async: safeAsync
};

// Make available globally for debugging
if (typeof window !== 'undefined') {
  safeWindow.set('SafeUtils', SafeUtils);
  
  console.log('🛡️ Safe Type Utilities loaded. Available as window.SafeUtils');
  console.log('  - SafeUtils.typeAssertion');
  console.log('  - SafeUtils.access');
  console.log('  - SafeUtils.window');
  console.log('  - SafeUtils.dom');
  console.log('  - SafeUtils.typeCheck');
  console.log('  - SafeUtils.error');
  console.log('  - SafeUtils.async');
}
