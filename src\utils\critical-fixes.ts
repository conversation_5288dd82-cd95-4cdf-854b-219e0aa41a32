/**
 * Critical Fixes for Production Issues
 * Addresses authentication, database, and WebSocket issues
 */

import { supabase } from '@/integrations/supabase/client';

export class CriticalFixes {
  /**
   * Fix user profile data inconsistencies
   */
  static async fixUserProfile(userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🔧 Fixing user profile for:', userId);

      // Get current user session
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        return { success: false, error: 'User not authenticated' };
      }

      // Get user metadata from auth
      const userMetadata = user.user_metadata || {};
      
      // Update profile with correct data
      const profileData = {
        user_id: user.id,
        full_name: userMetadata.full_name || user.email?.split('@')[0] || 'Unknown User',
        email: user.email,
        role: userMetadata.role || 'staff',
        account_type: userMetadata.account_type || userMetadata.role || 'staff',
        department_id: userMetadata.department_id || null,
        phone: userMetadata.phone || null,
        position: userMetadata.position || null,
        status: 'active',
        updated_at: new Date().toISOString()
      };

      const { error: updateError } = await supabase
        .from('profiles')
        .upsert(profileData, { onConflict: 'id' });

      if (updateError) {
        console.error('❌ Profile update failed:', updateError);
        return { success: false, error: updateError.message };
      }

      console.log('✅ Profile updated successfully');
      return { success: true };
    } catch (error: any) {
      console.error('❌ Profile fix failed:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create missing database tables and relationships
   */
  static async createMissingTables(): Promise<{ success: boolean; errors: string[] }> {
    const errors: string[] = [];
    console.log('🗄️ Creating missing database tables...');

    try {
      // Create reports table
      const { error: reportsError } = await supabase.rpc('exec_sql', {
        sql: `
          -- Create reports table
          CREATE TABLE IF NOT EXISTS public.reports (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            title TEXT NOT NULL,
            description TEXT,
            content TEXT,
            report_type TEXT DEFAULT 'general' CHECK (report_type IN ('general', 'financial', 'project', 'staff', 'performance')),
            status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'submitted', 'approved', 'rejected')),
            submitted_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
            approved_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
            department_id UUID REFERENCES public.departments(id) ON DELETE SET NULL,
            project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
            data JSONB DEFAULT '{}',
            attachments TEXT[],
            due_date DATE,
            submitted_at TIMESTAMP WITH TIME ZONE,
            approved_at TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );

          -- Enable RLS
          ALTER TABLE public.reports ENABLE ROW LEVEL SECURITY;

          -- Create policies
          CREATE POLICY "Users can view reports they submitted or are assigned to" ON public.reports
            FOR SELECT USING (
              auth.uid() = submitted_by OR 
              auth.uid() = approved_by OR
              EXISTS (
                SELECT 1 FROM public.profiles 
                WHERE id = auth.uid() AND role IN ('admin', 'manager')
              )
            );

          CREATE POLICY "Users can insert their own reports" ON public.reports
            FOR INSERT WITH CHECK (auth.uid() = submitted_by);

          CREATE POLICY "Users can update their own reports" ON public.reports
            FOR UPDATE USING (
              auth.uid() = submitted_by OR 
              auth.uid() = approved_by OR
              EXISTS (
                SELECT 1 FROM public.profiles 
                WHERE id = auth.uid() AND role IN ('admin', 'manager')
              )
            );

          -- Create indexes
          CREATE INDEX IF NOT EXISTS idx_reports_submitted_by ON public.reports(submitted_by);
          CREATE INDEX IF NOT EXISTS idx_reports_approved_by ON public.reports(approved_by);
          CREATE INDEX IF NOT EXISTS idx_reports_status ON public.reports(status);
          CREATE INDEX IF NOT EXISTS idx_reports_type ON public.reports(report_type);
          CREATE INDEX IF NOT EXISTS idx_reports_created_at ON public.reports(created_at);
        `
      });

      if (reportsError) {
        errors.push(`Reports table: ${reportsError.message}`);
      } else {
        console.log('✅ Reports table created');
      }

      // Create activity_logs table
      const { error: activityError } = await supabase.rpc('exec_sql', {
        sql: `
          -- Create activity_logs table
          CREATE TABLE IF NOT EXISTS public.activity_logs (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
            action TEXT NOT NULL,
            entity_type TEXT,
            entity_id UUID,
            description TEXT,
            metadata JSONB DEFAULT '{}',
            ip_address INET,
            user_agent TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );

          -- Enable RLS
          ALTER TABLE public.activity_logs ENABLE ROW LEVEL SECURITY;

          -- Create policies
          CREATE POLICY "Users can view their own activity logs" ON public.activity_logs
            FOR SELECT USING (
              auth.uid() = user_id OR
              EXISTS (
                SELECT 1 FROM public.profiles 
                WHERE id = auth.uid() AND role IN ('admin', 'manager')
              )
            );

          CREATE POLICY "System can insert activity logs" ON public.activity_logs
            FOR INSERT WITH CHECK (true);

          -- Create indexes
          CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON public.activity_logs(user_id);
          CREATE INDEX IF NOT EXISTS idx_activity_logs_action ON public.activity_logs(action);
          CREATE INDEX IF NOT EXISTS idx_activity_logs_entity ON public.activity_logs(entity_type, entity_id);
          CREATE INDEX IF NOT EXISTS idx_activity_logs_created_at ON public.activity_logs(created_at);
        `
      });

      if (activityError) {
        errors.push(`Activity logs table: ${activityError.message}`);
      } else {
        console.log('✅ Activity logs table created');
      }

      // Fix foreign key relationships
      const { error: fkError } = await supabase.rpc('exec_sql', {
        sql: `
          -- Add missing foreign key constraints if they don't exist
          DO $$ 
          BEGIN
            -- Fix projects table relationships
            IF NOT EXISTS (
              SELECT 1 FROM information_schema.table_constraints 
              WHERE constraint_name = 'projects_manager_id_fkey'
            ) THEN
              ALTER TABLE public.projects 
              ADD CONSTRAINT projects_manager_id_fkey 
              FOREIGN KEY (manager_id) REFERENCES auth.users(id) ON DELETE SET NULL;
            END IF;

            -- Fix tasks table relationships
            IF NOT EXISTS (
              SELECT 1 FROM information_schema.table_constraints 
              WHERE constraint_name = 'tasks_assigned_to_id_fkey'
            ) THEN
              ALTER TABLE public.tasks 
              ADD CONSTRAINT tasks_assigned_to_id_fkey 
              FOREIGN KEY (assigned_to_id) REFERENCES auth.users(id) ON DELETE SET NULL;
            END IF;

            IF NOT EXISTS (
              SELECT 1 FROM information_schema.table_constraints 
              WHERE constraint_name = 'tasks_created_by_fkey'
            ) THEN
              ALTER TABLE public.tasks 
              ADD CONSTRAINT tasks_created_by_fkey 
              FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE SET NULL;
            END IF;
          END $$;
        `
      });

      if (fkError) {
        errors.push(`Foreign key constraints: ${fkError.message}`);
      } else {
        console.log('✅ Foreign key relationships fixed');
      }

    } catch (error: any) {
      errors.push(`Table creation failed: ${error.message}`);
    }

    return {
      success: errors.length === 0,
      errors
    };
  }

  /**
   * Fix authentication state issues
   */
  static async fixAuthenticationState(): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🔐 Fixing authentication state...');

      // Get current session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        console.error('❌ Session error:', sessionError);
        return { success: false, error: sessionError.message };
      }

      if (!session?.user) {
        console.log('ℹ️ No active session found');
        return { success: true };
      }

      // Fix user profile
      const profileResult = await this.fixUserProfile(session.user.id);
      if (!profileResult.success) {
        return profileResult;
      }

      console.log('✅ Authentication state fixed');
      return { success: true };
    } catch (error: any) {
      console.error('❌ Auth fix failed:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Fix WebSocket connection issues
   */
  static async fixWebSocketConnections(): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🔌 Fixing WebSocket connections...');

      // Remove all existing channels
      const channels = supabase.getChannels();
      for (const channel of channels) {
        await supabase.removeChannel(channel);
      }

      // Wait a bit before reconnecting
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Reinitialize WebSocket manager
      if (typeof (window as any).webSocketManager !== 'undefined') {
        await (window as any).webSocketManager.disconnect();
        await (window as any).webSocketManager.initialize();
      }

      console.log('✅ WebSocket connections fixed');
      return { success: true };
    } catch (error: any) {
      console.error('❌ WebSocket fix failed:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Run all critical fixes
   */
  static async runAllFixes(): Promise<{ success: boolean; errors: string[] }> {
    console.log('🔧 Running all critical fixes...');
    const allErrors: string[] = [];

    // Fix authentication state
    const authResult = await this.fixAuthenticationState();
    if (!authResult.success && authResult.error) {
      allErrors.push(`Auth fix: ${authResult.error}`);
    }

    // Create missing tables
    const tablesResult = await this.createMissingTables();
    allErrors.push(...tablesResult.errors);

    // Fix WebSocket connections
    const wsResult = await this.fixWebSocketConnections();
    if (!wsResult.success && wsResult.error) {
      allErrors.push(`WebSocket fix: ${wsResult.error}`);
    }

    const success = allErrors.length === 0;
    
    if (success) {
      console.log('✅ All critical fixes completed successfully');
    } else {
      console.error('❌ Some fixes failed:', allErrors);
    }

    return {
      success,
      errors: allErrors
    };
  }
}

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).CriticalFixes = CriticalFixes;
  (window as any).runCriticalFixes = () => CriticalFixes.runAllFixes();
  (window as any).fixUserProfile = () => CriticalFixes.fixAuthenticationState();
  
  console.log('🔧 Critical Fixes loaded. Available commands:');
  console.log('  - runCriticalFixes()');
  console.log('  - fixUserProfile()');
}
