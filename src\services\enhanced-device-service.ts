/**
 * Enhanced Device Service
 * Robust device information service with fallbacks
 */

export interface DeviceInfo {
  userAgent: string;
  platform: string;
  language: string;
  timezone: string;
  screenResolution: string;
  connectionType?: string;
  batteryLevel?: number;
  isOnline: boolean;
  deviceType: 'mobile' | 'tablet' | 'desktop';
  browser: string;
  os: string;
  ipAddress?: string;
}

export interface NetworkInfo {
  effectiveType?: string;
  downlink?: number;
  rtt?: number;
  saveData?: boolean;
}

export class EnhancedDeviceService {
  private static instance: EnhancedDeviceService;
  private deviceInfo: DeviceInfo | null = null;
  private ipAddress: string | null = null;

  static getInstance(): EnhancedDeviceService {
    if (!EnhancedDeviceService.instance) {
      EnhancedDeviceService.instance = new EnhancedDeviceService();
    }
    return EnhancedDeviceService.instance;
  }

  /**
   * Get comprehensive device information
   */
  async getDeviceInfo(): Promise<DeviceInfo> {
    if (this.deviceInfo) {
      return this.deviceInfo;
    }

    console.log('📱 Gathering device information...');

    const nav = navigator as any;
    const userAgent = navigator.userAgent;

    // Get IP address asynchronously
    if (!this.ipAddress) {
      this.getIPAddress().then(ip => {
        this.ipAddress = ip;
        if (this.deviceInfo) {
          this.deviceInfo.ipAddress = ip;
        }
      }).catch(() => {
        this.ipAddress = 'unknown';
      });
    }

    this.deviceInfo = {
      userAgent,
      platform: navigator.platform,
      language: navigator.language,
      timezone: this.getTimezone(),
      screenResolution: `${screen.width}x${screen.height}`,
      connectionType: this.getConnectionType(),
      batteryLevel: await this.getBatteryLevel(),
      isOnline: navigator.onLine,
      deviceType: this.getDeviceType(userAgent),
      browser: this.getBrowser(userAgent),
      os: this.getOS(userAgent),
      ipAddress: this.ipAddress || undefined
    };

    console.log('✅ Device information gathered:', this.deviceInfo);
    return this.deviceInfo;
  }

  /**
   * Get IP address with multiple fallbacks
   */
  async getIPAddress(): Promise<string> {
    if (this.ipAddress && this.ipAddress !== 'unknown') {
      return this.ipAddress;
    }

    const services = [
      {
        name: 'cloudflare',
        url: 'https://*******/cdn-cgi/trace',
        parser: (text: string) => {
          const match = text.match(/ip=([^\n]+)/);
          return match ? match[1] : null;
        }
      },
      {
        name: 'ipify',
        url: 'https://api.ipify.org?format=json',
        parser: (text: string) => {
          try {
            return JSON.parse(text).ip;
          } catch {
            return null;
          }
        }
      },
      {
        name: 'ipapi',
        url: 'https://ipapi.co/ip/',
        parser: (text: string) => text.trim()
      },
      {
        name: 'httpbin',
        url: 'https://httpbin.org/ip',
        parser: (text: string) => {
          try {
            return JSON.parse(text).origin;
          } catch {
            return null;
          }
        }
      }
    ];

    for (const service of services) {
      try {
        console.log(`🌐 Trying ${service.name} for IP address...`);
        
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 3000);

        const response = await fetch(service.url, {
          signal: controller.signal,
          mode: 'cors',
          cache: 'no-cache'
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          console.warn(`${service.name} returned ${response.status}`);
          continue;
        }

        const text = await response.text();
        const ip = service.parser(text);
        
        if (ip && this.isValidIP(ip)) {
          console.log(`✅ IP address from ${service.name}:`, ip);
          this.ipAddress = ip;
          return ip;
        }
      } catch (error: any) {
        console.warn(`❌ ${service.name} failed:`, error.message);
        continue;
      }
    }

    console.warn('⚠️ All IP services failed, using fallback');
    this.ipAddress = 'unknown';
    return 'unknown';
  }

  /**
   * Validate IP address format
   */
  private isValidIP(ip: string): boolean {
    const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
    const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
    return ipv4Regex.test(ip) || ipv6Regex.test(ip);
  }

  /**
   * Get timezone information
   */
  private getTimezone(): string {
    try {
      return Intl.DateTimeFormat().resolvedOptions().timeZone;
    } catch {
      return 'UTC';
    }
  }

  /**
   * Get connection type
   */
  private getConnectionType(): string {
    const nav = navigator as any;
    if (nav.connection) {
      return nav.connection.effectiveType || nav.connection.type || 'unknown';
    }
    return 'unknown';
  }

  /**
   * Get battery level
   */
  private async getBatteryLevel(): Promise<number | undefined> {
    try {
      const nav = navigator as any;
      if (nav.getBattery) {
        const battery = await nav.getBattery();
        return Math.round(battery.level * 100);
      }
    } catch (error) {
      console.warn('Battery API not available');
    }
    return undefined;
  }

  /**
   * Detect device type
   */
  private getDeviceType(userAgent: string): 'mobile' | 'tablet' | 'desktop' {
    const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
    const tabletRegex = /iPad|Android(?=.*\bMobile\b)(?=.*\bSafari\b)|Android(?=.*\bTablet\b)/i;
    
    if (tabletRegex.test(userAgent)) {
      return 'tablet';
    } else if (mobileRegex.test(userAgent)) {
      return 'mobile';
    } else {
      return 'desktop';
    }
  }

  /**
   * Detect browser
   */
  private getBrowser(userAgent: string): string {
    if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
      return 'Chrome';
    } else if (userAgent.includes('Firefox')) {
      return 'Firefox';
    } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
      return 'Safari';
    } else if (userAgent.includes('Edg')) {
      return 'Edge';
    } else if (userAgent.includes('Opera') || userAgent.includes('OPR')) {
      return 'Opera';
    } else {
      return 'Unknown';
    }
  }

  /**
   * Detect operating system
   */
  private getOS(userAgent: string): string {
    if (userAgent.includes('Windows')) {
      return 'Windows';
    } else if (userAgent.includes('Mac OS')) {
      return 'macOS';
    } else if (userAgent.includes('Linux')) {
      return 'Linux';
    } else if (userAgent.includes('Android')) {
      return 'Android';
    } else if (userAgent.includes('iOS') || userAgent.includes('iPhone') || userAgent.includes('iPad')) {
      return 'iOS';
    } else {
      return 'Unknown';
    }
  }

  /**
   * Get network information
   */
  getNetworkInfo(): NetworkInfo {
    const nav = navigator as any;
    if (nav.connection) {
      return {
        effectiveType: nav.connection.effectiveType,
        downlink: nav.connection.downlink,
        rtt: nav.connection.rtt,
        saveData: nav.connection.saveData
      };
    }
    return {};
  }

  /**
   * Check if device is mobile
   */
  isMobile(): boolean {
    return this.getDeviceType(navigator.userAgent) === 'mobile';
  }

  /**
   * Check if device is tablet
   */
  isTablet(): boolean {
    return this.getDeviceType(navigator.userAgent) === 'tablet';
  }

  /**
   * Check if device is desktop
   */
  isDesktop(): boolean {
    return this.getDeviceType(navigator.userAgent) === 'desktop';
  }

  /**
   * Get device capabilities
   */
  getCapabilities(): {
    geolocation: boolean;
    camera: boolean;
    microphone: boolean;
    notifications: boolean;
    serviceWorker: boolean;
    webGL: boolean;
    localStorage: boolean;
    sessionStorage: boolean;
  } {
    return {
      geolocation: 'geolocation' in navigator,
      camera: 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices,
      microphone: 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices,
      notifications: 'Notification' in window,
      serviceWorker: 'serviceWorker' in navigator,
      webGL: this.hasWebGL(),
      localStorage: this.hasLocalStorage(),
      sessionStorage: this.hasSessionStorage()
    };
  }

  /**
   * Check WebGL support
   */
  private hasWebGL(): boolean {
    try {
      const canvas = document.createElement('canvas');
      return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
    } catch {
      return false;
    }
  }

  /**
   * Check localStorage support
   */
  private hasLocalStorage(): boolean {
    try {
      const test = 'test';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Check sessionStorage support
   */
  private hasSessionStorage(): boolean {
    try {
      const test = 'test';
      sessionStorage.setItem(test, test);
      sessionStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Refresh device information
   */
  async refresh(): Promise<DeviceInfo> {
    this.deviceInfo = null;
    this.ipAddress = null;
    return await this.getDeviceInfo();
  }
}

// Global instance
export const enhancedDeviceService = EnhancedDeviceService.getInstance();

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).enhancedDeviceService = enhancedDeviceService;
  (window as any).getDeviceInfo = () => enhancedDeviceService.getDeviceInfo();
  (window as any).getNetworkInfo = () => enhancedDeviceService.getNetworkInfo();
  
  console.log('📱 Enhanced Device Service loaded. Available commands:');
  console.log('  - getDeviceInfo()');
  console.log('  - getNetworkInfo()');
}
