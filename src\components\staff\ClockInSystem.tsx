
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Clock, MapPin, Calendar, AlertCircle } from "lucide-react";
import { format } from "date-fns";

interface TimeLog {
  id: string;
  user_id: string;
  clock_in: string;
  clock_out: string | null;
  location_address: string;
  latitude: number;
  longitude: number;
  total_hours: number | null;
  created_at: string;
}

interface LocationData {
  latitude: number;
  longitude: number;
  address: string;
}

export const ClockInSystem = () => {
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [location, setLocation] = useState<LocationData | null>(null);
  const [isGettingLocation, setIsGettingLocation] = useState(false);

  // Fetch today's time logs for the user
  const { data: todayLogs, isLoading } = useQuery({
    queryKey: ['time-logs', userProfile?.id],
    queryFn: async () => {
      if (!userProfile?.id) return [];
      
      const today = new Date().toISOString().split('T')[0];
      const { data, error } = await supabase
        .from('time_logs')
        .select('*')
        .eq('user_id', userProfile.id)
        .gte('clock_in', `${today}T00:00:00`)
        .lt('clock_in', `${today}T23:59:59`)
        .order('clock_in', { ascending: false });

      if (error) throw error;
      return data as TimeLog[];
    },
    enabled: !!userProfile?.id,
  });

  const currentSession = todayLogs?.find(log => !log.clock_out);
  const isCurrentlyClockedIn = !!currentSession;

  // Get current location
  const getCurrentLocation = async (): Promise<LocationData> => {
    setIsGettingLocation(true);
    try {
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        });
      });

      const { latitude, longitude } = position.coords;
      
      // Simple address format for now - can be enhanced with actual geocoding service
      const address = `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;

      const locationData: LocationData = { latitude, longitude, address };
      setLocation(locationData);
      return locationData;
    } catch (error) {
      toast({
        title: "Location Error",
        description: "Unable to get your location. Please enable location services.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsGettingLocation(false);
    }
  };

  // Clock in mutation
  const clockInMutation = useMutation({
    mutationFn: async (locationData: LocationData) => {
      const { data, error } = await supabase
        .from('time_logs')
        .insert([{
          user_id: userProfile?.id,
          clock_in: new Date().toISOString(),
          latitude: locationData.latitude,
          longitude: locationData.longitude,
          location_address: locationData.address,
          clock_in_method: 'manual',
          activity_type: 'work',
          status: 'active'
        }])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      toast({
        title: "Clocked In",
        description: "You have successfully clocked in.",
      });
      queryClient.invalidateQueries({ queryKey: ['time-logs'] });
    },
    onError: (error) => {
      toast({
        title: "Clock In Failed",
        description: "Failed to clock in. Please try again.",
        variant: "destructive",
      });
      console.error('Clock in error:', error);
    },
  });

  // Clock out mutation
  const clockOutMutation = useMutation({
    mutationFn: async () => {
      if (!currentSession) throw new Error('No active session');
      
      const clockOutTime = new Date();
      const clockInTime = new Date(currentSession.clock_in);
      const totalHours = (clockOutTime.getTime() - clockInTime.getTime()) / (1000 * 60 * 60);

      const { data, error } = await supabase
        .from('time_logs')
        .update({
          clock_out: clockOutTime.toISOString(),
          total_hours: Math.round(totalHours * 100) / 100,
        })
        .eq('id', currentSession.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      toast({
        title: "Clocked Out",
        description: "You have successfully clocked out.",
      });
      queryClient.invalidateQueries({ queryKey: ['time-logs'] });
    },
    onError: (error) => {
      toast({
        title: "Clock Out Failed",
        description: "Failed to clock out. Please try again.",
        variant: "destructive",
      });
      console.error('Clock out error:', error);
    },
  });

  const handleClockIn = async () => {
    try {
      const locationData = location || await getCurrentLocation();
      clockInMutation.mutate(locationData);
    } catch (error) {
      // Error already handled in getCurrentLocation
    }
  };

  const handleClockOut = () => {
    clockOutMutation.mutate();
  };

  useEffect(() => {
    getCurrentLocation();
  }, []);

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Time Clock
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Status */}
          <div className="text-center space-y-2">
            <Badge variant={isCurrentlyClockedIn ? "default" : "secondary"} className="text-sm">
              {isCurrentlyClockedIn ? "CLOCKED IN" : "CLOCKED OUT"}
            </Badge>
            
            {isCurrentlyClockedIn && currentSession && (
              <div className="text-sm text-muted-foreground">
                Since: {format(new Date(currentSession.clock_in), 'MMM dd, yyyy HH:mm')}
              </div>
            )}
          </div>

          {/* Location Info */}
          {location && (
            <div className="flex items-start gap-2 p-3 bg-muted rounded-md">
              <MapPin className="h-4 w-4 mt-0.5" />
              <div className="text-sm">
                <div className="font-medium">Current Location:</div>
                <div className="text-muted-foreground">{location.address}</div>
              </div>
            </div>
          )}

          {!location && (
            <div className="flex items-center gap-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-md">
              <AlertCircle className="h-4 w-4 text-yellow-600" />
              <div className="text-sm text-yellow-700 dark:text-yellow-300">
                Location required for time tracking
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-2">
            {!isCurrentlyClockedIn ? (
              <Button 
                onClick={handleClockIn}
                disabled={clockInMutation.isPending || isGettingLocation || !location}
                className="w-full"
                size="lg"
              >
                {clockInMutation.isPending ? "Clocking In..." : 
                 isGettingLocation ? "Getting Location..." : 
                 "Clock In"}
              </Button>
            ) : (
              <Button 
                onClick={handleClockOut}
                disabled={clockOutMutation.isPending}
                variant="destructive"
                className="w-full"
                size="lg"
              >
                {clockOutMutation.isPending ? "Clocking Out..." : "Clock Out"}
              </Button>
            )}

            {!location && (
              <Button 
                onClick={getCurrentLocation}
                disabled={isGettingLocation}
                variant="outline"
                className="w-full"
              >
                {isGettingLocation ? "Getting Location..." : "Get Location"}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Today's Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Today's Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          {todayLogs && todayLogs.length > 0 ? (
            <div className="space-y-3">
              {todayLogs.map((log) => (
                <div key={log.id} className="flex items-center justify-between p-3 border rounded-md">
                  <div>
                    <div className="font-medium">
                      {format(new Date(log.clock_in), 'HH:mm')} - {log.clock_out ? format(new Date(log.clock_out), 'HH:mm') : 'Active'}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {log.location_address}
                    </div>
                  </div>
                  <div className="text-right">
                    {log.total_hours ? (
                      <Badge variant="outline">
                        {log.total_hours.toFixed(2)}h
                      </Badge>
                    ) : (
                      <Badge>Active</Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              No activity recorded today
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
