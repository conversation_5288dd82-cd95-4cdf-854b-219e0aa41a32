/**
 * Enhanced Realtime Service
 * Comprehensive real-time functionality using Supabase best practices
 */

import { supabaseRealtimeManager } from '@/utils/supabase-realtime-manager';
import { supabaseDatabaseService } from '@/utils/supabase-database-service';
import { supabase } from '@/integrations/supabase/client';

interface RealtimeSubscription {
  id: string;
  table: string;
  callback: (payload: any) => void;
  unsubscribe: () => void;
}

interface PresenceState {
  userId: string;
  userName: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  lastSeen: string;
  location?: string;
  device?: string;
}

export class EnhancedRealtimeService {
  private static instance: EnhancedRealtimeService;
  private subscriptions: Map<string, RealtimeSubscription> = new Map();
  private presenceChannel: any = null;
  private currentPresence: PresenceState | null = null;
  private isInitialized = false;

  static getInstance(): EnhancedRealtimeService {
    if (!EnhancedRealtimeService.instance) {
      EnhancedRealtimeService.instance = new EnhancedRealtimeService();
    }
    return EnhancedRealtimeService.instance;
  }

  /**
   * Initialize the enhanced realtime service
   */
  async initialize(): Promise<{ success: boolean; message: string }> {
    if (this.isInitialized) {
      return { success: true, message: 'Already initialized' };
    }

    try {
      console.log('🚀 Initializing Enhanced Realtime Service...');

      // Initialize the Supabase realtime manager first
      const realtimeResult = await supabaseRealtimeManager.initialize();
      if (!realtimeResult.success) {
        console.warn('⚠️ Supabase realtime manager failed:', realtimeResult.message);
        return { success: false, message: realtimeResult.message };
      }

      // Initialize presence tracking
      await this.initializePresence();

      // Set up database change listeners
      await this.setupDatabaseListeners();

      // Set up custom event listeners
      this.setupCustomEventListeners();

      this.isInitialized = true;
      console.log('✅ Enhanced Realtime Service initialized successfully');

      return { success: true, message: 'Enhanced realtime service initialized' };
    } catch (error: any) {
      console.error('❌ Enhanced realtime service initialization failed:', error);
      return { success: false, message: `Initialization failed: ${error.message}` };
    }
  }

  /**
   * Initialize presence tracking
   */
  private async initializePresence(): Promise<void> {
    try {
      console.log('👥 Initializing presence tracking...');

      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.log('ℹ️ No authenticated user, skipping presence');
        return;
      }

      // Get user profile
      const profileResult = await supabaseDatabaseService.getUserProfile(user.id);
      if (!profileResult.data) {
        console.warn('⚠️ Could not get user profile for presence');
        return;
      }

      const profile = profileResult.data;

      // Set up presence state
      this.currentPresence = {
        userId: user.id,
        userName: profile.full_name || profile.email || 'Unknown User',
        status: 'online',
        lastSeen: new Date().toISOString(),
        location: 'Web App',
        device: this.getDeviceInfo()
      };

      // Create presence channel
      this.presenceChannel = supabase.channel('user-presence', {
        config: {
          presence: { key: user.id }
        }
      });

      // Set up presence event handlers
      this.presenceChannel
        .on('presence', { event: 'sync' }, () => {
          console.log('👥 Presence sync');
          const presenceState = this.presenceChannel.presenceState();
          this.handlePresenceSync(presenceState);
        })
        .on('presence', { event: 'join' }, ({ key, newPresences }: any) => {
          console.log('👋 User joined:', key, newPresences);
          this.handlePresenceJoin(key, newPresences);
        })
        .on('presence', { event: 'leave' }, ({ key, leftPresences }: any) => {
          console.log('👋 User left:', key, leftPresences);
          this.handlePresenceLeave(key, leftPresences);
        })
        .subscribe(async (status) => {
          if (status === 'SUBSCRIBED') {
            // Track presence
            await this.presenceChannel.track(this.currentPresence);
            console.log('✅ Presence tracking started');
          }
        });

      // Update presence periodically
      setInterval(() => {
        this.updatePresence();
      }, 30000); // Every 30 seconds

      // Handle page visibility changes
      document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
          this.updatePresence('away');
        } else {
          this.updatePresence('online');
        }
      });

      // Handle page unload
      window.addEventListener('beforeunload', () => {
        this.updatePresence('offline');
      });

    } catch (error) {
      console.error('❌ Presence initialization failed:', error);
    }
  }

  /**
   * Set up database change listeners
   */
  private async setupDatabaseListeners(): Promise<void> {
    console.log('🗄️ Setting up database change listeners...');

    // Listen to projects changes
    this.subscribeToTable('projects', (payload) => {
      console.log('📋 Project changed:', payload);
      this.emitCustomEvent('project-changed', payload);
      
      // Clear projects cache
      supabaseDatabaseService.clearCache();
    });

    // Listen to tasks changes
    this.subscribeToTable('tasks', (payload) => {
      console.log('📝 Task changed:', payload);
      this.emitCustomEvent('task-changed', payload);
      
      // Clear tasks cache
      supabaseDatabaseService.clearCache();
    });

    // Listen to profile changes
    this.subscribeToTable('profiles', (payload) => {
      console.log('👤 Profile changed:', payload);
      this.emitCustomEvent('profile-changed', payload);
      
      // Clear profile cache if it's the current user
      const { data: { user } } = await supabase.auth.getUser();
      if (user && payload.new?.id === user.id) {
        supabaseDatabaseService.clearCache();
      }
    });
  }

  /**
   * Set up custom event listeners
   */
  private setupCustomEventListeners(): void {
    console.log('🎧 Setting up custom event listeners...');

    // Listen for realtime broadcast events
    window.addEventListener('realtime-broadcast-notifications', (event: any) => {
      console.log('📢 Notification broadcast:', event.detail);
      this.handleNotificationBroadcast(event.detail);
    });

    window.addEventListener('realtime-broadcast-system-updates', (event: any) => {
      console.log('🔄 System update broadcast:', event.detail);
      this.handleSystemUpdateBroadcast(event.detail);
    });
  }

  /**
   * Subscribe to table changes
   */
  subscribeToTable(table: string, callback: (payload: any) => void): string {
    const subscriptionId = `${table}_${Date.now()}`;
    
    const unsubscribe = supabaseDatabaseService.subscribeToTable(
      table,
      callback
    );

    const subscription: RealtimeSubscription = {
      id: subscriptionId,
      table,
      callback,
      unsubscribe
    };

    this.subscriptions.set(subscriptionId, subscription);
    console.log(`📡 Subscribed to ${table} changes`);

    return subscriptionId;
  }

  /**
   * Unsubscribe from table changes
   */
  unsubscribeFromTable(subscriptionId: string): void {
    const subscription = this.subscriptions.get(subscriptionId);
    if (subscription) {
      subscription.unsubscribe();
      this.subscriptions.delete(subscriptionId);
      console.log(`🔌 Unsubscribed from ${subscription.table} changes`);
    }
  }

  /**
   * Update user presence
   */
  async updatePresence(status?: 'online' | 'away' | 'busy' | 'offline'): Promise<void> {
    if (!this.presenceChannel || !this.currentPresence) return;

    try {
      this.currentPresence.status = status || 'online';
      this.currentPresence.lastSeen = new Date().toISOString();

      await this.presenceChannel.track(this.currentPresence);
      console.log(`👥 Presence updated: ${this.currentPresence.status}`);
    } catch (error) {
      console.warn('⚠️ Failed to update presence:', error);
    }
  }

  /**
   * Send broadcast message
   */
  async sendBroadcast(channel: string, event: string, payload: any): Promise<void> {
    try {
      const channelInstance = supabase.channel(channel);
      await channelInstance.send({
        type: 'broadcast',
        event,
        payload
      });
      console.log(`📢 Broadcast sent to ${channel}:`, event, payload);
    } catch (error) {
      console.error('❌ Failed to send broadcast:', error);
    }
  }

  /**
   * Handle presence sync
   */
  private handlePresenceSync(presenceState: any): void {
    const users = Object.keys(presenceState).map(userId => {
      const presence = presenceState[userId][0];
      return presence;
    });

    console.log('👥 Current online users:', users.length);
    this.emitCustomEvent('presence-sync', { users, count: users.length });
  }

  /**
   * Handle presence join
   */
  private handlePresenceJoin(key: string, newPresences: any[]): void {
    const user = newPresences[0];
    console.log('👋 User joined:', user?.userName);
    this.emitCustomEvent('user-joined', { userId: key, user });
  }

  /**
   * Handle presence leave
   */
  private handlePresenceLeave(key: string, leftPresences: any[]): void {
    const user = leftPresences[0];
    console.log('👋 User left:', user?.userName);
    this.emitCustomEvent('user-left', { userId: key, user });
  }

  /**
   * Handle notification broadcast
   */
  private handleNotificationBroadcast(payload: any): void {
    // Show notification to user
    this.emitCustomEvent('notification-received', payload);
  }

  /**
   * Handle system update broadcast
   */
  private handleSystemUpdateBroadcast(payload: any): void {
    // Handle system updates
    this.emitCustomEvent('system-update-received', payload);
  }

  /**
   * Emit custom event
   */
  private emitCustomEvent(eventName: string, data: any): void {
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent(`enhanced-realtime-${eventName}`, {
        detail: data
      }));
    }
  }

  /**
   * Get device information
   */
  private getDeviceInfo(): string {
    const userAgent = navigator.userAgent;
    if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
      return 'Mobile';
    } else if (/Tablet/.test(userAgent)) {
      return 'Tablet';
    } else {
      return 'Desktop';
    }
  }

  /**
   * Get service status
   */
  getStatus(): {
    isInitialized: boolean;
    subscriptionCount: number;
    presenceActive: boolean;
    realtimeStatus: any;
  } {
    return {
      isInitialized: this.isInitialized,
      subscriptionCount: this.subscriptions.size,
      presenceActive: this.presenceChannel !== null,
      realtimeStatus: supabaseRealtimeManager.getStatus()
    };
  }

  /**
   * Cleanup all connections
   */
  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up Enhanced Realtime Service...');

    // Unsubscribe from all table subscriptions
    for (const [id, subscription] of this.subscriptions) {
      subscription.unsubscribe();
    }
    this.subscriptions.clear();

    // Clean up presence
    if (this.presenceChannel) {
      await supabase.removeChannel(this.presenceChannel);
      this.presenceChannel = null;
    }

    // Clean up realtime manager
    await supabaseRealtimeManager.cleanup();

    this.isInitialized = false;
    console.log('✅ Enhanced Realtime Service cleanup completed');
  }
}

// Global instance
export const enhancedRealtimeService = EnhancedRealtimeService.getInstance();

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).enhancedRealtimeService = enhancedRealtimeService;
  (window as any).initEnhancedRealtime = () => enhancedRealtimeService.initialize();
  (window as any).getEnhancedRealtimeStatus = () => enhancedRealtimeService.getStatus();
  (window as any).updatePresence = (status?: string) => enhancedRealtimeService.updatePresence(status as any);
  
  console.log('🚀 Enhanced Realtime Service loaded. Available commands:');
  console.log('  - initEnhancedRealtime()');
  console.log('  - getEnhancedRealtimeStatus()');
  console.log('  - updatePresence(status)');
}
