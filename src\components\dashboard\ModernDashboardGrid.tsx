/**
 * Modern Dashboard Grid Component
 * Contemporary card arrangement with improved UX
 */

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  TrendingUp,
  TrendingDown,
  Activity,
  BarChart3,
  Target,
  Zap,
  ArrowUpRight
} from 'lucide-react';

interface DashboardCard {
  id: string;
  title: string;
  value: string | number;
  change?: number;
  changeType?: 'increase' | 'decrease' | 'neutral';
  icon: React.ComponentType<any>;
  description?: string;
  trend?: number[];
  color: string;
  size?: 'small' | 'medium' | 'large';
}

interface ModernDashboardGridProps {
  cards: DashboardCard[];
  loading?: boolean;
}

export const ModernDashboardGrid = ({ cards, loading = false }: ModernDashboardGridProps) => {
  const getChangeColor = (changeType?: string) => {
    switch (changeType) {
      case 'increase': return 'text-green-600 bg-green-50';
      case 'decrease': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getChangeIcon = (changeType?: string) => {
    switch (changeType) {
      case 'increase': return TrendingUp;
      case 'decrease': return TrendingDown;
      default: return Activity;
    }
  };

  const getCardSize = (size?: string) => {
    switch (size) {
      case 'large': return 'md:col-span-2 lg:col-span-2';
      case 'medium': return 'md:col-span-1 lg:col-span-1';
      case 'small': return 'col-span-1';
      default: return 'col-span-1';
    }
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {Array.from({ length: 8 }).map((_, index) => (
          <Card key={index} className="animate-pulse">
            <CardHeader className="space-y-0 pb-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-full"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Primary Stats - Hero Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {cards.slice(0, 4).map((card, index) => {
          const ChangeIcon = getChangeIcon(card.changeType);
          
          return (
            <Card
              key={card.id}
              className="group relative overflow-hidden border-[0.8px] border-[#ff1c04]/20 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-[#1a1a1a] to-[#0f0f0f]"
              style={{
                background: `linear-gradient(135deg, #1a1a1a 0%, #0f0f0f 100%)`
              }}
            >
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-5">
                <div className="absolute inset-0" style={{
                  backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
                }}></div>
              </div>

              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {card.title}
                </CardTitle>
                <div 
                  className="p-2 rounded-lg text-white group-hover:scale-110 transition-transform"
                  style={{ backgroundColor: card.color }}
                >
                  <card.icon className="h-4 w-4" />
                </div>
              </CardHeader>
              
              <CardContent className="relative z-10">
                <div className="flex items-baseline justify-between">
                  <div className="text-3xl font-bold text-[#e5e5e5]">
                    {typeof card.value === 'number' ? card.value.toLocaleString() : card.value}
                  </div>
                  {card.change !== undefined && (
                    <Badge 
                      variant="secondary" 
                      className={`${getChangeColor(card.changeType)} border-0 text-xs font-medium`}
                    >
                      <ChangeIcon className="h-3 w-3 mr-1" />
                      {Math.abs(card.change)}%
                    </Badge>
                  )}
                </div>
                
                {card.description && (
                  <p className="text-xs text-muted-foreground mt-2 line-clamp-2">
                    {card.description}
                  </p>
                )}

                {/* Mini Trend Line */}
                {card.trend && (
                  <div className="mt-3 h-8 flex items-end space-x-1">
                    {card.trend.map((value, i) => (
                      <div
                        key={i}
                        className="flex-1 rounded-t"
                        style={{
                          height: `${(value / Math.max(...card.trend!)) * 100}%`,
                          backgroundColor: card.color,
                          opacity: 0.6
                        }}
                      />
                    ))}
                  </div>
                )}
              </CardContent>

              {/* Hover Action */}
              <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <ArrowUpRight className="h-4 w-4" />
                </Button>
              </div>
            </Card>
          );
        })}
      </div>

      {/* Secondary Stats - Compact Grid */}
      {cards.length > 4 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {cards.slice(4).map((card, index) => (
            <Card 
              key={card.id}
              className="group hover:shadow-md transition-all duration-200 border-l-4"
              style={{ borderLeftColor: card.color }}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <card.icon 
                    className="h-5 w-5" 
                    style={{ color: card.color }}
                  />
                  {card.change !== undefined && (
                    <Badge variant="outline" className="text-xs">
                      {card.change > 0 ? '+' : ''}{card.change}%
                    </Badge>
                  )}
                </div>
                
                <div className="space-y-1">
                  <div className="text-xl font-bold">
                    {typeof card.value === 'number' ? card.value.toLocaleString() : card.value}
                  </div>
                  <div className="text-xs text-muted-foreground font-medium">
                    {card.title}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Action Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="group cursor-pointer hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-200">
          <CardContent className="p-6 text-center">
            <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
              <BarChart3 className="h-6 w-6 text-white" />
            </div>
            <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">View Analytics</h3>
            <p className="text-sm text-blue-700 dark:text-blue-300">Detailed insights and reports</p>
          </CardContent>
        </Card>

        <Card className="group cursor-pointer hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-green-200">
          <CardContent className="p-6 text-center">
            <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
              <Target className="h-6 w-6 text-white" />
            </div>
            <h3 className="font-semibold text-green-900 dark:text-green-100 mb-2">Set Goals</h3>
            <p className="text-sm text-green-700 dark:text-green-300">Create and track objectives</p>
          </CardContent>
        </Card>

        <Card className="group cursor-pointer hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 border-purple-200">
          <CardContent className="p-6 text-center">
            <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
              <Zap className="h-6 w-6 text-white" />
            </div>
            <h3 className="font-semibold text-purple-900 dark:text-purple-100 mb-2">Quick Actions</h3>
            <p className="text-sm text-purple-700 dark:text-purple-300">Automate common tasks</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
