import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface AgentQueryRequest {
  query: string
  userContext: any
  availableTools: string[]
}

interface AgentAction {
  tool: string
  parameters: Record<string, any>
  reasoning: string
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { query, userContext, availableTools }: AgentQueryRequest = await req.json()

    if (!query || !availableTools) {
      return new Response(
        JSON.stringify({ error: 'Query and available tools are required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get OpenAI API key
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    const { data: apiKeys, error: keyError } = await supabase
      .from('api_keys')
      .select('api_key')
      .eq('provider', 'openai')
      .eq('is_active', true)
      .single()

    if (keyError || !apiKeys?.api_key) {
      // Fallback to simple keyword analysis
      return new Response(
        JSON.stringify(analyzeQueryFallback(query, availableTools)),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Build prompt for tool analysis
    const prompt = buildToolAnalysisPrompt(query, userContext, availableTools)

    // Call OpenAI to analyze query
    const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKeys.api_key}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'You are an AI agent analyzer. Determine which tools are needed for a user query and provide structured output.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 1000,
      }),
    })

    if (!openaiResponse.ok) {
      console.error('OpenAI API error:', await openaiResponse.text())
      return new Response(
        JSON.stringify(analyzeQueryFallback(query, availableTools)),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const aiResponse = await openaiResponse.json()
    const content = aiResponse.choices[0]?.message?.content

    if (!content) {
      return new Response(
        JSON.stringify(analyzeQueryFallback(query, availableTools)),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Parse AI response
    try {
      const analysis = JSON.parse(content)
      return new Response(
        JSON.stringify(analysis),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    } catch (parseError) {
      console.error('Failed to parse AI response:', parseError)
      return new Response(
        JSON.stringify(analyzeQueryFallback(query, availableTools)),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

  } catch (error) {
    console.error('Error in analyze-agent-query function:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

function buildToolAnalysisPrompt(query: string, userContext: any, availableTools: string[]): string {
  return `Analyze the following user query and determine which tools are needed to fulfill the request.

User Query: "${query}"

User Context:
- User ID: ${userContext?.userId || 'unknown'}
- Role: ${userContext?.userProfile?.role || 'unknown'}
- Department: ${userContext?.userProfile?.department?.name || 'unknown'}

Available Tools:
${availableTools.map(tool => `- ${tool}`).join('\n')}

Tool Descriptions:
- database_query: Query database tables for information
- create_task: Create new tasks for users or projects
- search_documents: Search through documents and knowledge base
- get_user_info: Get information about a specific user
- send_notification: Send notifications to users
- get_project_status: Get project status and progress

Respond with a JSON object in this exact format:
{
  "needsTools": boolean,
  "suggestedActions": [
    {
      "tool": "tool_name",
      "parameters": {
        "param1": "value1",
        "param2": "value2"
      },
      "reasoning": "Why this tool is needed"
    }
  ]
}

Guidelines:
1. Only suggest tools that are actually needed
2. Extract specific parameters from the query when possible
3. If the query is just conversational, set needsTools to false
4. Provide clear reasoning for each tool selection
5. Consider the user's role and context when suggesting actions`
}

function analyzeQueryFallback(query: string, availableTools: string[]): any {
  const lowerQuery = query.toLowerCase()
  const actions: AgentAction[] = []

  // Simple keyword-based analysis
  if (lowerQuery.includes('create') && lowerQuery.includes('task')) {
    actions.push({
      tool: 'create_task',
      parameters: {},
      reasoning: 'User wants to create a task'
    })
  }

  if (lowerQuery.includes('search') || lowerQuery.includes('find') || lowerQuery.includes('look for')) {
    actions.push({
      tool: 'search_documents',
      parameters: { query: query },
      reasoning: 'User wants to search for information'
    })
  }

  if (lowerQuery.includes('user') || lowerQuery.includes('profile') || lowerQuery.includes('employee')) {
    actions.push({
      tool: 'database_query',
      parameters: { table: 'profiles', limit: 10 },
      reasoning: 'User asking about users or profiles'
    })
  }

  if (lowerQuery.includes('project') && (lowerQuery.includes('status') || lowerQuery.includes('progress'))) {
    actions.push({
      tool: 'get_project_status',
      parameters: {},
      reasoning: 'User asking about project status'
    })
  }

  if (lowerQuery.includes('notify') || lowerQuery.includes('send') || lowerQuery.includes('message')) {
    actions.push({
      tool: 'send_notification',
      parameters: {},
      reasoning: 'User wants to send a notification'
    })
  }

  return {
    needsTools: actions.length > 0,
    suggestedActions: actions
  }
}
