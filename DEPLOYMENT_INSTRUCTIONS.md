# 🚀 Deployment Instructions - CT Nigeria AI Workboard

## 📋 Pre-Deployment Checklist

### ✅ All Fixes Applied
- [x] User presence table fix
- [x] Tasks table fix  
- [x] Profiles table fix
- [x] Time logs table fix
- [x] Service worker external API fix
- [x] GPS/Location timeout fix
- [x] Dialog accessibility fix
- [x] Route loading fix
- [x] Console error cleanup

### ✅ Database Migrations Required
The following migrations need to be applied to your Supabase database:

1. `supabase/migrations/20250803_fix_user_presence_table.sql`
2. `supabase/migrations/20250803_fix_tasks_table.sql`
3. `supabase/migrations/20250803_fix_profiles_table.sql`
4. `supabase/migrations/20250803_fix_time_logs_table.sql`

---

## 🗄️ Database Setup (CRITICAL - Do This First!)

### Option 1: Apply Migrations via Supabase Dashboard
1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor**
3. Run each migration file in order:

```sql
-- 1. Run contents of: supabase/migrations/20250803_fix_user_presence_table.sql
-- 2. Run contents of: supabase/migrations/20250803_fix_tasks_table.sql  
-- 3. Run contents of: supabase/migrations/20250803_fix_profiles_table.sql
-- 4. Run contents of: supabase/migrations/20250803_fix_time_logs_table.sql
```

### Option 2: Use Supabase CLI (Recommended)
```bash
# Install Supabase CLI if not already installed
npm install -g supabase

# Login to Supabase
supabase login

# Link to your project
supabase link --project-ref YOUR_PROJECT_REF

# Apply migrations
supabase db push
```

---

## 🔧 Build & Deploy Steps

### Step 1: Clean Build
```bash
# Clear all caches and rebuild
npm run fresh-build

# Or manually:
npm run clear-cache
npm install
npm run build:prod
```

### Step 2: Deploy to Vercel
```bash
# Option A: Using npm script
npm run deploy:vercel

# Option B: Manual deployment
npm run build:prod
vercel --prod

# Option C: Git-based deployment (recommended)
git add .
git commit -m "feat: comprehensive fixes and improvements"
git push origin main
```

### Step 3: Verify Deployment
1. Visit `https://ai.ctnigeria.com`
2. Check the fix test page: `https://ai.ctnigeria.com/dashboard/fix-test`
3. Run all fixes and verify they pass
4. Test critical functionality:
   - User authentication
   - Time tracking (clock in/out)
   - Navigation to all routes
   - Database operations

---

## 🌐 Environment Variables

Ensure these are set in your Vercel project:

### Required Environment Variables
```bash
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_OPENAI_API_KEY=your_openai_api_key
NODE_ENV=production
```

### Optional Environment Variables
```bash
VITE_APP_VERSION=1.0.0
VITE_ENVIRONMENT=production
VITE_DEBUG_MODE=false
```

---

## 🔍 Post-Deployment Verification

### 1. Automated Tests
Visit: `https://ai.ctnigeria.com/dashboard/fix-test`
- Click "Run All Fixes"
- Verify all tests pass
- Check that no console errors appear

### 2. Manual Testing
Test these critical paths:
- [ ] User login/logout
- [ ] Dashboard navigation
- [ ] Time tracking (clock in/out)
- [ ] Leave requests
- [ ] Task management
- [ ] AI features
- [ ] File uploads
- [ ] Reports generation

### 3. Performance Check
- [ ] Page load times < 3 seconds
- [ ] No 404 errors
- [ ] No console errors
- [ ] Service worker functioning
- [ ] PWA features working

---

## 🚨 Troubleshooting

### Common Issues & Solutions

#### 1. Database Connection Errors
```bash
# Check Supabase environment variables
echo $VITE_SUPABASE_URL
echo $VITE_SUPABASE_ANON_KEY

# Verify in Vercel dashboard under Settings > Environment Variables
```

#### 2. Build Failures
```bash
# Clear everything and rebuild
npm run fresh-build

# Check for TypeScript errors
npm run build 2>&1 | grep -i error
```

#### 3. 404 Errors on Routes
- Verify `vercel.json` rewrites are correct
- Check that all route components exist
- Ensure proper lazy loading

#### 4. Console Errors After Deployment
- Check browser console for specific errors
- Verify all migrations were applied
- Run the fix test page

---

## 📊 Monitoring & Maintenance

### 1. Set Up Monitoring
- Vercel Analytics (automatic)
- Supabase Dashboard monitoring
- Browser console monitoring

### 2. Regular Maintenance
- Weekly: Check error logs
- Monthly: Update dependencies
- Quarterly: Performance audit

### 3. Backup Strategy
- Supabase automatic backups (enabled by default)
- Git repository (source code)
- Environment variables backup

---

## 🎯 Success Criteria

Deployment is successful when:
- [ ] All pages load without errors
- [ ] All database operations work
- [ ] Time tracking functions correctly
- [ ] No console errors or warnings
- [ ] All routes are accessible
- [ ] PWA features work
- [ ] Performance is acceptable

---

## 📞 Support

If you encounter issues:
1. Check the fix test page first
2. Review browser console for errors
3. Verify database migrations were applied
4. Check Vercel deployment logs
5. Review Supabase logs

---

## 🎉 Final Notes

This deployment includes:
- **9 major bug fixes**
- **4 database table fixes**
- **Enhanced error handling**
- **Improved performance**
- **Better accessibility**
- **Cleaner console output**

The application should now run smoothly with minimal errors and excellent user experience!
