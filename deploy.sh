#!/bin/bash

# 🚀 CT Nigeria AI Workboard - Automated Deployment Script
# This script handles the complete deployment process with all fixes

set -e  # Exit on any error

echo "🚀 Starting CT Nigeria AI Workboard Deployment..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root."
    exit 1
fi

# Check if required tools are installed
print_status "Checking required tools..."

if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install Node.js and npm."
    exit 1
fi

if ! command -v git &> /dev/null; then
    print_error "git is not installed. Please install git."
    exit 1
fi

print_success "All required tools are available"

# Step 1: Clean and prepare
print_status "Step 1: Cleaning previous builds..."
npm run clear-cache || {
    print_warning "Cache clearing failed, continuing anyway..."
}

# Step 2: Install dependencies
print_status "Step 2: Installing dependencies..."
npm ci

# Step 3: Run production build
print_status "Step 3: Building for production..."
npm run build:prod

if [ $? -eq 0 ]; then
    print_success "Build completed successfully"
else
    print_error "Build failed. Please check the errors above."
    exit 1
fi

# Step 4: Check if Vercel CLI is available
print_status "Step 4: Checking deployment options..."

if command -v vercel &> /dev/null; then
    print_status "Vercel CLI found. Deploying to Vercel..."
    
    # Deploy to Vercel
    vercel --prod
    
    if [ $? -eq 0 ]; then
        print_success "Deployment to Vercel completed successfully!"
        print_status "Your app should be available at: https://ai.ctnigeria.com"
    else
        print_error "Vercel deployment failed."
        exit 1
    fi
else
    print_warning "Vercel CLI not found. Please install it with: npm install -g vercel"
    print_status "Alternative: Push to git and let Vercel auto-deploy"
    
    # Check if we're in a git repository
    if [ -d ".git" ]; then
        print_status "Git repository detected. Preparing for git-based deployment..."
        
        # Add all changes
        git add .
        
        # Commit with timestamp
        TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
        git commit -m "feat: comprehensive fixes and deployment - $TIMESTAMP" || {
            print_warning "No changes to commit or commit failed"
        }
        
        # Push to main branch
        print_status "Pushing to main branch..."
        git push origin main
        
        if [ $? -eq 0 ]; then
            print_success "Code pushed to git successfully!"
            print_status "Vercel should auto-deploy from git. Check your Vercel dashboard."
        else
            print_error "Git push failed."
            exit 1
        fi
    else
        print_error "No git repository found and Vercel CLI not available."
        print_status "Please either:"
        print_status "1. Install Vercel CLI: npm install -g vercel"
        print_status "2. Initialize git repository: git init"
        exit 1
    fi
fi

# Step 5: Post-deployment verification
print_status "Step 5: Post-deployment verification..."
print_status "Please verify the following manually:"
echo ""
echo "✅ Database Migrations Applied:"
echo "   - user_presence table"
echo "   - tasks table" 
echo "   - profiles table"
echo "   - time_logs table"
echo ""
echo "✅ Test the application:"
echo "   - Visit: https://ai.ctnigeria.com"
echo "   - Test page: https://ai.ctnigeria.com/dashboard/fix-test"
echo "   - Run all fixes and verify they pass"
echo ""
echo "✅ Critical functionality:"
echo "   - User authentication"
echo "   - Time tracking (clock in/out)"
echo "   - Navigation to all routes"
echo "   - Database operations"
echo ""

print_success "Deployment script completed!"
print_status "Next steps:"
echo "1. Apply database migrations (see DEPLOYMENT_INSTRUCTIONS.md)"
echo "2. Test the application thoroughly"
echo "3. Monitor for any issues"

echo ""
echo "=================================================="
echo "🎉 Deployment Complete!"
echo "=================================================="
