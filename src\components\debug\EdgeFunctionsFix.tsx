/**
 * Edge Functions Fix Component
 * Provides UI for testing and debugging Supabase Edge Functions
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { ScrollArea } from '../ui/scroll-area';
import { Loader2, CheckCircle, XCircle, Play, RefreshCw, FileText } from 'lucide-react';
import { edgeFunctionsFixer, EdgeFunctionStatus, EdgeFunctionTestResult } from '../../utils/edge-functions-fixer';

interface TestResults {
  healthCheck: EdgeFunctionStatus[];
  functionTests: EdgeFunctionTestResult[];
  summary: {
    totalFunctions: number;
    healthyFunctions: number;
    errorFunctions: number;
    successfulTests: number;
    failedTests: number;
  };
}

export function EdgeFunctionsFix() {
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<TestResults | null>(null);
  const [diagnosticReport, setDiagnosticReport] = useState<string>('');
  const [activeTest, setActiveTest] = useState<string>('');

  const handleHealthCheck = async () => {
    setIsLoading(true);
    setActiveTest('health-check');
    try {
      const results = await edgeFunctionsFixer.checkAllFunctions();
      setTestResults(prev => prev ? { ...prev, healthCheck: results } : null);
    } catch (error) {
      console.error('Health check failed:', error);
    } finally {
      setIsLoading(false);
      setActiveTest('');
    }
  };

  const handleComprehensiveTest = async () => {
    setIsLoading(true);
    setActiveTest('comprehensive');
    try {
      const results = await edgeFunctionsFixer.runComprehensiveTests();
      setTestResults(results);
    } catch (error) {
      console.error('Comprehensive test failed:', error);
    } finally {
      setIsLoading(false);
      setActiveTest('');
    }
  };

  const handleGenerateReport = async () => {
    setIsLoading(true);
    setActiveTest('report');
    try {
      const report = await edgeFunctionsFixer.generateReport();
      setDiagnosticReport(report);
    } catch (error) {
      console.error('Report generation failed:', error);
    } finally {
      setIsLoading(false);
      setActiveTest('');
    }
  };

  const handleTestSpecificFunction = async (functionName: string) => {
    setIsLoading(true);
    setActiveTest(functionName);
    try {
      let result: EdgeFunctionTestResult;
      
      switch (functionName) {
        case 'ai-agent-executor':
          result = await edgeFunctionsFixer.testAIAgentExecutor();
          break;
        case 'ai-file-analyzer':
          result = await edgeFunctionsFixer.testAIFileAnalyzer();
          break;
        case 'analyze-document':
          result = await edgeFunctionsFixer.testAnalyzeDocument();
          break;
        default:
          throw new Error(`Unknown function: ${functionName}`);
      }

      // Update the specific test result
      setTestResults(prev => {
        if (!prev) return null;
        const updatedTests = prev.functionTests.map(test => 
          test.function === functionName ? result : test
        );
        return { ...prev, functionTests: updatedTests };
      });
    } catch (error) {
      console.error(`Test for ${functionName} failed:`, error);
    } finally {
      setIsLoading(false);
      setActiveTest('');
    }
  };

  const getStatusIcon = (status: string, success?: boolean) => {
    if (status === 'healthy' || success === true) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
    if (status === 'error' || success === false) {
      return <XCircle className="h-4 w-4 text-red-500" />;
    }
    return <RefreshCw className="h-4 w-4 text-yellow-500" />;
  };

  const getStatusBadge = (status: string, success?: boolean) => {
    if (status === 'healthy' || success === true) {
      return <Badge variant="default" className="bg-green-100 text-green-800">Healthy</Badge>;
    }
    if (status === 'error' || success === false) {
      return <Badge variant="destructive">Error</Badge>;
    }
    return <Badge variant="secondary">Unknown</Badge>;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Play className="h-5 w-5" />
          Edge Functions Diagnostics
        </CardTitle>
        <CardDescription>
          Test and debug Supabase Edge Functions: ai-agent-executor, ai-file-analyzer, and analyze-document
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Control Buttons */}
        <div className="flex flex-wrap gap-2">
          <Button
            onClick={handleHealthCheck}
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            {isLoading && activeTest === 'health-check' ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Health Check
          </Button>
          <Button
            onClick={handleComprehensiveTest}
            disabled={isLoading}
            size="sm"
          >
            {isLoading && activeTest === 'comprehensive' ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Play className="h-4 w-4 mr-2" />
            )}
            Run All Tests
          </Button>
          <Button
            onClick={handleGenerateReport}
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            {isLoading && activeTest === 'report' ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <FileText className="h-4 w-4 mr-2" />
            )}
            Generate Report
          </Button>
        </div>

        {/* Results */}
        {testResults && (
          <Tabs defaultValue="summary" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="summary">Summary</TabsTrigger>
              <TabsTrigger value="health">Health Check</TabsTrigger>
              <TabsTrigger value="tests">Function Tests</TabsTrigger>
              <TabsTrigger value="report">Report</TabsTrigger>
            </TabsList>

            <TabsContent value="summary" className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold">{testResults.summary.totalFunctions}</div>
                    <div className="text-sm text-muted-foreground">Total Functions</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">{testResults.summary.healthyFunctions}</div>
                    <div className="text-sm text-muted-foreground">Healthy</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-red-600">{testResults.summary.errorFunctions}</div>
                    <div className="text-sm text-muted-foreground">Errors</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">{testResults.summary.successfulTests}</div>
                    <div className="text-sm text-muted-foreground">Passed Tests</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-red-600">{testResults.summary.failedTests}</div>
                    <div className="text-sm text-muted-foreground">Failed Tests</div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="health" className="space-y-4">
              {testResults.healthCheck.map((func) => (
                <Card key={func.name}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(func.status)}
                        <div>
                          <div className="font-medium">{func.name}</div>
                          <div className="text-sm text-muted-foreground">
                            Last checked: {new Date(func.lastChecked).toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {func.responseTime && (
                          <Badge variant="outline">{func.responseTime}ms</Badge>
                        )}
                        {getStatusBadge(func.status)}
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleTestSpecificFunction(func.name)}
                          disabled={isLoading}
                        >
                          {isLoading && activeTest === func.name ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            'Test'
                          )}
                        </Button>
                      </div>
                    </div>
                    {func.error && (
                      <Alert className="mt-3">
                        <XCircle className="h-4 w-4" />
                        <AlertDescription>{func.error}</AlertDescription>
                      </Alert>
                    )}
                  </CardContent>
                </Card>
              ))}
            </TabsContent>

            <TabsContent value="tests" className="space-y-4">
              {testResults.functionTests.map((test) => (
                <Card key={test.function}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        {getStatusIcon('', test.success)}
                        <div>
                          <div className="font-medium">{test.function}</div>
                          <div className="text-sm text-muted-foreground">
                            Response time: {test.responseTime}ms
                          </div>
                        </div>
                      </div>
                      {getStatusBadge('', test.success)}
                    </div>
                    {test.error && (
                      <Alert className="mb-3">
                        <XCircle className="h-4 w-4" />
                        <AlertDescription>{test.error}</AlertDescription>
                      </Alert>
                    )}
                    {test.response && (
                      <ScrollArea className="h-32 w-full rounded border p-3">
                        <pre className="text-xs">
                          {JSON.stringify(test.response, null, 2)}
                        </pre>
                      </ScrollArea>
                    )}
                  </CardContent>
                </Card>
              ))}
            </TabsContent>

            <TabsContent value="report">
              {diagnosticReport ? (
                <ScrollArea className="h-96 w-full rounded border p-4">
                  <pre className="text-sm whitespace-pre-wrap">{diagnosticReport}</pre>
                </ScrollArea>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  Click "Generate Report" to create a detailed diagnostic report
                </div>
              )}
            </TabsContent>
          </Tabs>
        )}

        {!testResults && (
          <div className="text-center py-8 text-muted-foreground">
            Click "Run All Tests" to start diagnosing Edge Functions
          </div>
        )}
      </CardContent>
    </Card>
  );
}