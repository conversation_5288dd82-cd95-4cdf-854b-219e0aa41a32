import react from '@vitejs/plugin-react-swc';
import path from 'path';
import { defineConfig } from 'vite';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
    dedupe: ['react', 'react-dom'],
  },
  server: {
    port: 8083,
    host: true,
  },
  build: {
    target: 'es2020',
    sourcemap: false, // Disable source maps in production to prevent 403 errors
    outDir: 'dist',
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        // Fix initialization order issues by using simpler chunking strategy
        manualChunks: {
          // Core React dependencies - load first
          'react-vendor': ['react', 'react-dom', 'react-router-dom'],
          // UI components - load after React
          'ui-vendor': ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu', '@radix-ui/react-select'],
          // Chart libraries
          'chart-vendor': ['recharts', 'd3-scale', 'd3-shape'],
          // Supabase
          'supabase-vendor': ['@supabase/supabase-js', '@supabase/auth-helpers-react'],
          // PDF libraries
          'pdf-vendor': ['jspdf', 'jspdf-autotable', 'pdf-lib'],
          // Utilities
          'utils-vendor': ['lodash', 'date-fns', 'uuid'],
        },
        // Ensure proper chunk loading order
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId ? chunkInfo.facadeModuleId.split('/').pop() : 'chunk';
          return `assets/[name]-[hash].js`;
        },
      },
    },
  },
  define: {
    global: 'globalThis',
  },
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@supabase/supabase-js',
      'recharts',
      'jspdf',
    ],
    exclude: [
      'p-queue',
    ],
  },

});
