// Service Worker for CT Nigeria Ltd AI Management Platform
// Version 1.0.0

const CACHE_NAME = 'ct-nigeria-v1.0.0';
const STATIC_CACHE = 'ct-nigeria-static-v1.0.0';
const DYNAMIC_CACHE = 'ct-nigeria-dynamic-v1.0.0';

// Assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/favicon.ico',
  '/icon.png',
  '/logo.png',
  '/manifest.json'
];

// API endpoints to cache
const API_CACHE_PATTERNS = [
  /\/api\/profiles/,
  /\/api\/projects/,
  /\/api\/tasks/,
  /\/api\/reports/,
  /\/api\/notifications/
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('Service Worker: Static assets cached');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('Service Worker: Failed to cache static assets', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('Service Worker: Deleting old cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - serve from cache with network fallback
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Skip chrome-extension and other non-http requests
  if (!url.protocol.startsWith('http')) {
    return;
  }

  // Handle different types of requests
  if (isStaticAsset(request)) {
    event.respondWith(cacheFirst(request, STATIC_CACHE));
  } else if (isAPIRequest(request)) {
    event.respondWith(networkFirst(request, DYNAMIC_CACHE));
  } else {
    event.respondWith(staleWhileRevalidate(request, DYNAMIC_CACHE));
  }
});

// Cache strategies
async function cacheFirst(request, cacheName) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    // Allow external API requests to pass through without caching
    const url = new URL(request.url);
    const externalDomains = [
      'api.bigdatacloud.net',
      'api.ipify.org',
      'ipapi.co',
      'httpbin.org',
      'nominatim.openstreetmap.org',
      '*******',
      'cloudflare.com',
      'cloudflare-dns.com',
      'cdn.cloudflare.com'
    ];

    if (externalDomains.some(domain => url.hostname.includes(domain))) {
      console.log('Service Worker: Bypassing cache for external API:', url.hostname);
      // Return fetch directly without any caching or service worker intervention
      try {
        return await fetch(request, {
          mode: 'cors',
          credentials: 'omit',
          cache: 'no-cache'
        });
      } catch (fetchError) {
        console.warn('External API fetch failed:', fetchError.message);
        // Return a proper error response instead of throwing
        return new Response(JSON.stringify({
          error: 'External API unavailable',
          service: url.hostname,
          offline: true
        }), {
          status: 503,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.warn('Cache first strategy failed:', error.message);

    // Return cached response if available, otherwise return offline response
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    return new Response(JSON.stringify({
      error: 'Network unavailable',
      offline: true
    }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

async function networkFirst(request, cacheName) {
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.log('Network failed, trying cache:', error);
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    return new Response('Offline', { status: 503 });
  }
}

async function staleWhileRevalidate(request, cacheName) {
  try {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);

    // Allow external API requests to pass through without caching
    const url = new URL(request.url);
    const externalDomains = [
      'api.bigdatacloud.net',
      'api.ipify.org',
      'ipapi.co',
      'httpbin.org',
      'nominatim.openstreetmap.org',
      '*******',
      'cloudflare.com',
      'cloudflare-dns.com',
      'cdn.cloudflare.com'
    ];

    if (externalDomains.some(domain => url.hostname.includes(domain))) {
      console.log('Service Worker: Bypassing cache for external API:', url.hostname);
      // Return fetch directly without any caching or service worker intervention
      try {
        return await fetch(request, {
          mode: 'cors',
          credentials: 'omit',
          cache: 'no-cache'
        });
      } catch (fetchError) {
        console.warn('External API fetch failed:', fetchError.message);
        // Return a proper error response instead of throwing
        return new Response(JSON.stringify({
          error: 'External API unavailable',
          service: url.hostname,
          offline: true
        }), {
          status: 503,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

    const fetchPromise = fetch(request).then((networkResponse) => {
      if (networkResponse.ok) {
        cache.put(request, networkResponse.clone());
      }
      return networkResponse;
    }).catch((error) => {
      console.warn('Network request failed:', error.message);
      return cachedResponse || new Response(JSON.stringify({
        error: 'Network unavailable',
        offline: true
      }), {
        status: 503,
        headers: { 'Content-Type': 'application/json' }
      });
    });

    return cachedResponse || fetchPromise;
  } catch (error) {
    console.warn('Stale while revalidate failed:', error.message);
    return new Response(JSON.stringify({
      error: 'Service unavailable',
      offline: true
    }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Helper functions
function isStaticAsset(request) {
  const url = new URL(request.url);
  return url.pathname.match(/\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/);
}

function isAPIRequest(request) {
  const url = new URL(request.url);
  return API_CACHE_PATTERNS.some(pattern => pattern.test(url.pathname));
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

async function doBackgroundSync() {
  console.log('Service Worker: Background sync triggered');
  // Implement background sync logic here
}

// Push notifications
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json();
    const options = {
      body: data.body,
      icon: '/icon.png',
      badge: '/icon.png',
      data: data.data || {}
    };
    
    event.waitUntil(
      self.registration.showNotification(data.title, options)
    );
  }
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  event.waitUntil(
    clients.openWindow(event.notification.data.url || '/')
  );
});

console.log('Service Worker: Loaded successfully');
