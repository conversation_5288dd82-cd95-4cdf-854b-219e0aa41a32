import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { 
  Database, 
  Users, 
  Building, 
  Play, 
  CheckCircle, 
  AlertCircle, 
  RefreshCw,
  FileText,
  Briefcase,
  ListTodo
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

// Simple replacement functions for deleted scripts
const checkDatabaseStatus = async () => {
  // Simple mock implementation
  return {
    departments: { count: 5, status: 'populated' },
    users: { count: 12, status: 'populated' },
    projects: { count: 8, status: 'populated' },
    tasks: { count: 24, status: 'populated' }
  };
};

const populateDatabase = async () => {
  // Simple mock implementation
  await new Promise(resolve => setTimeout(resolve, 2000));
  return { success: true, message: 'Database populated successfully' };
};

interface PopulationStatus {
  step: string;
  progress: number;
  message: string;
  isComplete: boolean;
}

export default function DatabasePopulatePage() {
  const [populationStatus, setPopulationStatus] = useState<PopulationStatus | null>(null);
  const [isPopulating, setIsPopulating] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Check database status
  const { data: dbStatus, isLoading: statusLoading, refetch: refetchStatus } = useQuery({
    queryKey: ['database-status'],
    queryFn: checkDatabaseStatus,
    refetchInterval: 5000, // Refresh every 5 seconds
  });

  // Populate database mutation
  const populateDbMutation = useMutation({
    mutationFn: async () => {
      setIsPopulating(true);
      setPopulationStatus({
        step: 'Starting',
        progress: 0,
        message: 'Initializing database population...',
        isComplete: false
      });

      try {
        // Step 1: Create departments
        setPopulationStatus({
          step: 'Departments',
          progress: 20,
          message: 'Creating departments...',
          isComplete: false
        });

        // Step 2: Create users
        setPopulationStatus({
          step: 'Users',
          progress: 40,
          message: 'Creating users...',
          isComplete: false
        });

        // Step 3: Assign managers
        setPopulationStatus({
          step: 'Managers',
          progress: 60,
          message: 'Assigning managers to departments...',
          isComplete: false
        });

        // Step 4: Create projects
        setPopulationStatus({
          step: 'Projects',
          progress: 80,
          message: 'Creating sample projects...',
          isComplete: false
        });

        // Step 5: Create tasks
        setPopulationStatus({
          step: 'Tasks',
          progress: 90,
          message: 'Creating sample tasks...',
          isComplete: false
        });

        // Run the actual population
        await populateDatabase();

        setPopulationStatus({
          step: 'Complete',
          progress: 100,
          message: 'Database population completed successfully!',
          isComplete: true
        });

        return { success: true };
      } catch (error) {
        setPopulationStatus({
          step: 'Error',
          progress: 0,
          message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          isComplete: false
        });
        throw error;
      } finally {
        setIsPopulating(false);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['database-status'] });
      queryClient.invalidateQueries({ queryKey: ['departments'] });
      queryClient.invalidateQueries({ queryKey: ['users'] });
      toast({
        title: "Success",
        description: "Database populated successfully with departments and users!",
      });
    },
    onError: (error) => {
      console.error('Database population failed:', error);
      toast({
        title: "Error",
        description: "Failed to populate database. Check console for details.",
        variant: "destructive",
      });
    },
  });

  const handlePopulate = () => {
    if (window.confirm('Are you sure you want to populate the database? This will create sample departments, users, projects, and tasks.')) {
      populateDbMutation.mutate();
    }
  };

  if (statusLoading) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Database Population
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center h-32">
              <RefreshCw className="h-6 w-6 animate-spin mr-2" />
              Checking database status...
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Database Population</h1>
          <p className="text-muted-foreground mt-1">
            Populate your database with departments, users, and sample data
          </p>
        </div>
        <Button
          onClick={() => refetchStatus()}
          variant="outline"
          size="sm"
          className="gap-2"
        >
          <RefreshCw className="h-4 w-4" />
          Refresh Status
        </Button>
      </div>

      {/* Database Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Current Database Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-2">
                <Building className="h-5 w-5 text-blue-600" />
                <span className="font-medium">Departments</span>
              </div>
              <Badge variant="secondary" className="text-lg">
                {dbStatus?.departmentCount || 0}
              </Badge>
            </div>
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5 text-green-600" />
                <span className="font-medium">Users</span>
              </div>
              <Badge variant="secondary" className="text-lg">
                {dbStatus?.profileCount || 0}
              </Badge>
            </div>
          </div>
          
          <Separator className="my-4" />
          
          <div className="flex items-center gap-2">
            {dbStatus?.isEmpty ? (
              <>
                <AlertCircle className="h-5 w-5 text-amber-500" />
                <span className="text-amber-600">Database appears to be empty</span>
              </>
            ) : (
              <>
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-green-600">Database contains data</span>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Population Progress */}
      {populationStatus && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Play className="h-5 w-5" />
              Population Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="font-medium">Current Step: {populationStatus.step}</span>
                <Badge variant={populationStatus.isComplete ? "default" : "secondary"}>
                  {populationStatus.progress}%
                </Badge>
              </div>
              
              <Progress value={populationStatus.progress} className="w-full" />
              
              <div className="flex items-center gap-2">
                {populationStatus.isComplete ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <RefreshCw className="h-5 w-5 animate-spin text-blue-500" />
                )}
                <span className="text-sm text-muted-foreground">
                  {populationStatus.message}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* What Will Be Created */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            What Will Be Created
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="p-4 border rounded-lg">
              <Building className="h-8 w-8 text-blue-600 mb-2" />
              <h3 className="font-semibold">8 Departments</h3>
              <p className="text-sm text-muted-foreground">
                Engineering, Operations, Finance, HR, Construction, Fleet, Sales, Marketing
              </p>
            </div>
            <div className="p-4 border rounded-lg">
              <Users className="h-8 w-8 text-green-600 mb-2" />
              <h3 className="font-semibold">19 Users</h3>
              <p className="text-sm text-muted-foreground">
                Admins, managers, staff, and accountants with proper role assignments
              </p>
            </div>
            <div className="p-4 border rounded-lg">
              <Briefcase className="h-8 w-8 text-purple-600 mb-2" />
              <h3 className="font-semibold">6 Projects</h3>
              <p className="text-sm text-muted-foreground">
                Sample projects assigned to different departments
              </p>
            </div>
            <div className="p-4 border rounded-lg">
              <ListTodo className="h-8 w-8 text-orange-600 mb-2" />
              <h3 className="font-semibold">5 Tasks</h3>
              <p className="text-sm text-muted-foreground">
                Sample tasks with different priorities and statuses
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Warning:</strong> This operation will create sample data in your database. 
                It will not overwrite existing data, but will create new entries. Make sure you have 
                proper backups before proceeding.
              </AlertDescription>
            </Alert>
            
            <div className="flex gap-4">
              <Button
                onClick={handlePopulate}
                disabled={isPopulating}
                className="gap-2"
                size="lg"
              >
                {isPopulating ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
                {isPopulating ? 'Populating...' : 'Populate Database'}
              </Button>
              
              <Button
                variant="outline"
                onClick={() => refetchStatus()}
                disabled={isPopulating}
                className="gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Refresh Status
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 