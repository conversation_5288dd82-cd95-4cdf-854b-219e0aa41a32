/**
 * Edge Functions Fixer Utility
 * Diagnoses and fixes issues with Supabase Edge Functions
 */

import { supabase } from '@/integrations/supabase/client';

export interface EdgeFunctionStatus {
  name: string;
  status: 'healthy' | 'error' | 'unknown';
  error?: string;
  lastChecked: string;
  responseTime?: number;
}

export interface EdgeFunctionTestResult {
  function: string;
  success: boolean;
  error?: string;
  response?: any;
  responseTime: number;
}

export class EdgeFunctionsFixer {
  private readonly functions = [
    'ai-agent-executor',
    'ai-file-analyzer',
    'analyze-document'
  ];

  /**
   * Check the health status of all Edge Functions
   */
  async checkAllFunctions(): Promise<EdgeFunctionStatus[]> {
    const results: EdgeFunctionStatus[] = [];

    for (const functionName of this.functions) {
      const startTime = Date.now();
      try {
        const { data, error } = await supabase.functions.invoke(functionName, {
          body: { healthCheck: true }
        });

        const responseTime = Date.now() - startTime;

        if (error) {
          results.push({
            name: functionName,
            status: 'error',
            error: error.message,
            lastChecked: new Date().toISOString(),
            responseTime
          });
        } else {
          results.push({
            name: functionName,
            status: 'healthy',
            lastChecked: new Date().toISOString(),
            responseTime
          });
        }
      } catch (error) {
        const responseTime = Date.now() - startTime;
        results.push({
          name: functionName,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
          lastChecked: new Date().toISOString(),
          responseTime
        });
      }
    }

    return results;
  }

  /**
   * Test the ai-agent-executor function
   */
  async testAIAgentExecutor(): Promise<EdgeFunctionTestResult> {
    const startTime = Date.now();
    try {
      const { data, error } = await supabase.functions.invoke('ai-agent-executor', {
        body: {
          input: 'Hello, can you help me with task management?'
        }
      });

      const responseTime = Date.now() - startTime;

      if (error) {
        return {
          function: 'ai-agent-executor',
          success: false,
          error: error.message,
          responseTime
        };
      }

      return {
        function: 'ai-agent-executor',
        success: true,
        response: data,
        responseTime
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      return {
        function: 'ai-agent-executor',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        responseTime
      };
    }
  }

  /**
   * Test the ai-file-analyzer function
   */
  async testAIFileAnalyzer(): Promise<EdgeFunctionTestResult> {
    const startTime = Date.now();
    try {
      const { data, error } = await supabase.functions.invoke('ai-file-analyzer', {
        body: {
          fileName: 'test-document.txt',
          fileType: 'text/plain',
          content: 'This is a test document for analyzing file content and structure.',
          userId: 'test-user-id'
        }
      });

      const responseTime = Date.now() - startTime;

      if (error) {
        return {
          function: 'ai-file-analyzer',
          success: false,
          error: error.message,
          responseTime
        };
      }

      return {
        function: 'ai-file-analyzer',
        success: true,
        response: data,
        responseTime
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      return {
        function: 'ai-file-analyzer',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        responseTime
      };
    }
  }

  /**
   * Test the analyze-document function
   */
  async testAnalyzeDocument(): Promise<EdgeFunctionTestResult> {
    const startTime = Date.now();
    try {
      const { data, error } = await supabase.functions.invoke('analyze-document', {
        body: {
          content: 'This is a sample document content for testing the document analysis functionality. It contains various information that should be analyzed for insights and recommendations.',
          fileName: 'test-analysis.txt'
        }
      });

      const responseTime = Date.now() - startTime;

      if (error) {
        return {
          function: 'analyze-document',
          success: false,
          error: error.message,
          responseTime
        };
      }

      return {
        function: 'analyze-document',
        success: true,
        response: data,
        responseTime
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      return {
        function: 'analyze-document',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        responseTime
      };
    }
  }

  /**
   * Run comprehensive tests on all Edge Functions
   */
  async runComprehensiveTests(): Promise<{
    healthCheck: EdgeFunctionStatus[];
    functionTests: EdgeFunctionTestResult[];
    summary: {
      totalFunctions: number;
      healthyFunctions: number;
      errorFunctions: number;
      successfulTests: number;
      failedTests: number;
    };
  }> {
    console.log('Running comprehensive Edge Functions tests...');

    // Check health status
    const healthCheck = await this.checkAllFunctions();

    // Run function-specific tests
    const functionTests = await Promise.all([
      this.testAIAgentExecutor(),
      this.testAIFileAnalyzer(),
      this.testAnalyzeDocument()
    ]);

    // Calculate summary
    const healthyFunctions = healthCheck.filter(f => f.status === 'healthy').length;
    const errorFunctions = healthCheck.filter(f => f.status === 'error').length;
    const successfulTests = functionTests.filter(t => t.success).length;
    const failedTests = functionTests.filter(t => !t.success).length;

    return {
      healthCheck,
      functionTests,
      summary: {
        totalFunctions: this.functions.length,
        healthyFunctions,
        errorFunctions,
        successfulTests,
        failedTests
      }
    };
  }

  /**
   * Get diagnostic information about Edge Functions environment
   */
  async getDiagnosticInfo(): Promise<{
    supabaseConnection: boolean;
    projectUrl: string;
    timestamp: string;
    userAgent: string;
  }> {
    try {
      // Test basic Supabase connection
      const { data, error } = await supabase
        .from('profiles')
        .select('id')
        .limit(1);

      return {
        supabaseConnection: !error,
        projectUrl: supabase.supabaseUrl,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent
      };
    } catch (error) {
      return {
        supabaseConnection: false,
        projectUrl: supabase.supabaseUrl,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent
      };
    }
  }

  /**
   * Generate a detailed report of Edge Functions status
   */
  async generateReport(): Promise<string> {
    const diagnostics = await this.getDiagnosticInfo();
    const tests = await this.runComprehensiveTests();

    let report = `# Edge Functions Diagnostic Report\n\n`;
    report += `**Generated:** ${diagnostics.timestamp}\n`;
    report += `**Supabase URL:** ${diagnostics.projectUrl}\n`;
    report += `**Connection Status:** ${diagnostics.supabaseConnection ? '✅ Connected' : '❌ Failed'}\n\n`;

    report += `## Summary\n`;
    report += `- **Total Functions:** ${tests.summary.totalFunctions}\n`;
    report += `- **Healthy Functions:** ${tests.summary.healthyFunctions}\n`;
    report += `- **Functions with Errors:** ${tests.summary.errorFunctions}\n`;
    report += `- **Successful Tests:** ${tests.summary.successfulTests}\n`;
    report += `- **Failed Tests:** ${tests.summary.failedTests}\n\n`;

    report += `## Health Check Results\n`;
    for (const func of tests.healthCheck) {
      const status = func.status === 'healthy' ? '✅' : '❌';
      report += `- **${func.name}:** ${status} ${func.status.toUpperCase()}`;
      if (func.responseTime) {
        report += ` (${func.responseTime}ms)`;
      }
      if (func.error) {
        report += ` - ${func.error}`;
      }
      report += `\n`;
    }

    report += `\n## Function Test Results\n`;
    for (const test of tests.functionTests) {
      const status = test.success ? '✅' : '❌';
      report += `- **${test.function}:** ${status} ${test.success ? 'PASSED' : 'FAILED'} (${test.responseTime}ms)\n`;
      if (test.error) {
        report += `  - Error: ${test.error}\n`;
      }
      if (test.response && test.success) {
        report += `  - Response: ${JSON.stringify(test.response, null, 2).substring(0, 200)}...\n`;
      }
    }

    return report;
  }
}

// Export singleton instance
export const edgeFunctionsFixer = new EdgeFunctionsFixer();