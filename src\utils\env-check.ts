/**
 * Environment Variables Checker
 * Ensures all required environment variables are present
 */

export interface EnvCheckResult {
  isValid: boolean;
  missing: string[];
  warnings: string[];
  supabaseConfigured: boolean;
}

export function checkEnvironmentVariables(): EnvCheckResult {
  const required = [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY'
  ];

  const missing: string[] = [];
  const warnings: string[] = [];

  // Check required variables
  required.forEach(varName => {
    const value = import.meta.env[varName];
    if (!value || value === 'undefined' || value === '') {
      missing.push(varName);
    }
  });

  // Check Supabase URL format
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  if (supabaseUrl && !supabaseUrl.includes('supabase.co')) {
    warnings.push('VITE_SUPABASE_URL does not appear to be a valid Supabase URL');
  }

  // Check if we're in production without proper config
  if (import.meta.env.PROD && missing.length > 0) {
    console.error('🚨 Production deployment missing environment variables:', missing);
  }

  const result: EnvCheckResult = {
    isValid: missing.length === 0,
    missing,
    warnings,
    supabaseConfigured: !missing.includes('VITE_SUPABASE_URL') && !missing.includes('VITE_SUPABASE_ANON_KEY')
  };

  // Log results
  if (import.meta.env.DEV) {
    console.log('🔍 Environment Check:', result);
  }

  return result;
}

export function getEnvironmentInfo() {
  return {
    mode: import.meta.env.MODE,
    prod: import.meta.env.PROD,
    dev: import.meta.env.DEV,
    supabaseUrl: import.meta.env.VITE_SUPABASE_URL ? 'configured' : 'missing',
    supabaseKey: import.meta.env.VITE_SUPABASE_ANON_KEY ? 'configured' : 'missing',
  };
}

// Auto-check in development
if (import.meta.env.DEV) {
  const envCheck = checkEnvironmentVariables();
  if (!envCheck.isValid) {
    console.warn('⚠️ Environment variables missing:', envCheck.missing);
    console.warn('💡 Create a .env.local file with the required variables');
  }
}

// Show helpful error in production if env vars are missing
if (import.meta.env.PROD) {
  const envCheck = checkEnvironmentVariables();
  if (!envCheck.isValid) {
    // Create a visible error message for production
    const errorDiv = document.createElement('div');
    errorDiv.innerHTML = `
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #000;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        font-family: monospace;
        text-align: center;
        padding: 20px;
        box-sizing: border-box;
      ">
        <div>
          <h1 style="color: #ff1c04; margin-bottom: 20px;">⚠️ Configuration Error</h1>
          <p style="font-size: 18px; margin-bottom: 20px;">Missing environment variables:</p>
          <ul style="list-style: none; padding: 0; color: #ffc107;">
            ${envCheck.missing.map((v, index) => `<li key="${index}" style="margin: 10px 0;">❌ ${v}</li>`).join('')}
          </ul>
          <p style="margin-top: 30px; color: #28a745;">
            Please configure these variables in your deployment platform.
          </p>
          <p style="margin-top: 20px; font-size: 14px; color: #6c757d;">
            For Vercel: Go to Project Settings → Environment Variables
          </p>
        </div>
      </div>
    `;
    document.body.appendChild(errorDiv);
  }
}
