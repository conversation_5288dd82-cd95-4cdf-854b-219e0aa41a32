/**
 * AI Workboard Agent for Supabase Edge Functions
 * Provides intelligent agent capabilities for the workboard system
 */

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.4';

const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const openAIKey = Deno.env.get('OPENAI_API_KEY') || Deno.env.get('VITE_OPENAI_API_KEY');

const supabase = createClient(supabaseUrl, supabaseServiceKey);

export interface AgentResponse {
  message: string;
  actions?: Array<{
    type: string;
    target: string;
    data?: any;
  }>;
  confidence: number;
  sources?: any[];
}

export class AIWorkboardAgent {
  public agent: any = null;
  private initialized = false;

  async initialize(): Promise<void> {
    if (this.initialized) return;
    
    try {
      // Initialize the agent with basic capabilities
      this.agent = {
        execute: this.execute.bind(this),
        initialized: true
      };
      
      this.initialized = true;
      console.log('AI Workboard Agent initialized successfully');
    } catch (error) {
      console.error('Failed to initialize AI Workboard Agent:', error);
      throw error;
    }
  }

  async execute(input: string): Promise<AgentResponse> {
    if (!this.initialized || !this.agent) {
      throw new Error('Agent not initialized');
    }

    try {
      // If OpenAI key is not available, provide mock response
      if (!openAIKey || openAIKey === '' || openAIKey === 'your_openai_api_key_here') {
        return this.getMockResponse(input);
      }

      // Use OpenAI for actual processing
      const response = await this.processWithOpenAI(input);
      return response;
    } catch (error) {
      console.error('Agent execution error:', error);
      // Fallback to mock response on error
      return this.getMockResponse(input);
    }
  }

  private async processWithOpenAI(input: string): Promise<AgentResponse> {
    try {
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${openAIKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: 'You are an AI assistant for a workboard management system. Help users with tasks, projects, time tracking, and system navigation. Provide helpful and actionable responses.'
            },
            {
              role: 'user',
              content: input
            }
          ],
          max_tokens: 500,
          temperature: 0.7
        })
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status}`);
      }

      const data = await response.json();
      const message = data.choices?.[0]?.message?.content || 'I apologize, but I could not process your request.';

      return {
        message,
        confidence: 0.8,
        actions: this.extractActions(input, message)
      };
    } catch (error) {
      console.error('OpenAI processing error:', error);
      throw error;
    }
  }

  private getMockResponse(input: string): AgentResponse {
    const lowerInput = input.toLowerCase();
    
    // Provide contextual mock responses based on input
    if (lowerInput.includes('task') || lowerInput.includes('todo')) {
      return {
        message: 'I can help you manage tasks. You can create new tasks, assign them to team members, set priorities, and track progress. Would you like me to help you create a new task or view existing ones?',
        confidence: 0.9,
        actions: [
          {
            type: 'navigate',
            target: '/tasks',
            data: { highlight: 'create-task-button' }
          }
        ]
      };
    }
    
    if (lowerInput.includes('project')) {
      return {
        message: 'I can assist with project management. You can create projects, assign team members, track milestones, and monitor progress. What would you like to do with projects?',
        confidence: 0.9,
        actions: [
          {
            type: 'navigate',
            target: '/projects',
            data: { highlight: 'project-overview' }
          }
        ]
      };
    }
    
    if (lowerInput.includes('time') || lowerInput.includes('clock')) {
      return {
        message: 'I can help with time tracking. You can clock in/out, view time logs, generate reports, and manage work schedules. What time tracking feature do you need?',
        confidence: 0.9,
        actions: [
          {
            type: 'navigate',
            target: '/time-tracking',
            data: { highlight: 'clock-controls' }
          }
        ]
      };
    }
    
    if (lowerInput.includes('report') || lowerInput.includes('analytics')) {
      return {
        message: 'I can help you generate reports and view analytics. You can create project reports, time tracking summaries, team performance metrics, and custom analytics. What type of report do you need?',
        confidence: 0.9,
        actions: [
          {
            type: 'navigate',
            target: '/reports',
            data: { highlight: 'report-generator' }
          }
        ]
      };
    }
    
    // Default response
    return {
      message: `I understand you're asking about: "${input}". I'm here to help you navigate the workboard system, manage tasks and projects, track time, and generate reports. Could you please be more specific about what you'd like to do?`,
      confidence: 0.7,
      actions: [
        {
          type: 'display',
          target: 'help-menu',
          data: { suggestions: ['Create a task', 'View projects', 'Track time', 'Generate report'] }
        }
      ]
    };
  }

  private extractActions(input: string, response: string): Array<{ type: string; target: string; data?: any }> {
    const actions: Array<{ type: string; target: string; data?: any }> = [];
    const lowerInput = input.toLowerCase();
    const lowerResponse = response.toLowerCase();
    
    // Extract navigation actions based on keywords
    if (lowerInput.includes('task') || lowerResponse.includes('task')) {
      actions.push({
        type: 'navigate',
        target: '/tasks',
        data: { reason: 'task-related-query' }
      });
    }
    
    if (lowerInput.includes('project') || lowerResponse.includes('project')) {
      actions.push({
        type: 'navigate',
        target: '/projects',
        data: { reason: 'project-related-query' }
      });
    }
    
    if (lowerInput.includes('time') || lowerInput.includes('clock')) {
      actions.push({
        type: 'navigate',
        target: '/time-tracking',
        data: { reason: 'time-tracking-query' }
      });
    }
    
    return actions;
  }

  async getSystemStatus(): Promise<any> {
    try {
      // Check basic system health
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select('id')
        .limit(1);
      
      const { data: tasks, error: tasksError } = await supabase
        .from('tasks')
        .select('id')
        .limit(1);
      
      return {
        database: !profilesError && !tasksError,
        profiles: !profilesError,
        tasks: !tasksError,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('System status check failed:', error);
      return {
        database: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Export singleton instance
export const aiWorkboardAgent = new AIWorkboardAgent();