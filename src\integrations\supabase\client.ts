import { createClient } from '@supabase/supabase-js';
import { Database } from './types';

// Use environment variables with fallback to production
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || "https://dvflgnqwbsjityrowatf.supabase.co";
const SUPABASE_ANON_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";

// Log which database we're connecting to
console.log('🔗 Supabase Client Configuration:', {
  url: SUPABASE_URL,
  environment: import.meta.env.MODE,
  isLocal: SUPABASE_URL.includes('localhost') || SUPABASE_URL.includes('127.0.0.1'),
  isProduction: SUPABASE_URL.includes('supabase.co')
});

// Enhanced Supabase client with automatic retry and error handling
export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true
  },
  global: {
    headers: {
      'X-Client-Info': 'ctnl-ai-workboard',
      'X-Client-Version': '1.0.0'
    },
    fetch: async (url, options = {}) => {
      const maxRetries = 3;
      let lastError = null;

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          console.log(`🔄 Supabase request attempt ${attempt}:`, url);

          const response = await fetch(url, {
            ...options,
            headers: {
              ...options.headers,
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            }
          });

          // Handle specific error codes
          if (response.status === 503) {
            throw new Error('Service Unavailable - Database service is temporarily down');
          }

          if (response.status === 429) {
            throw new Error('Rate Limited - Too many requests');
          }

          if (!response.ok && response.status >= 500) {
            throw new Error(`Server Error ${response.status}: ${response.statusText}`);
          }

          console.log(`✅ Supabase request successful on attempt ${attempt}`);
          return response;

        } catch (error) {
          lastError = error;
          console.warn(`⚠️ Supabase request failed on attempt ${attempt}:`, error.message);

          // Don't retry on client errors (4xx)
          if (error.message.includes('4') && !error.message.includes('429')) {
            break;
          }

          // Wait before retry (exponential backoff)
          if (attempt < maxRetries) {
            const delay = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s
            console.log(`⏳ Waiting ${delay}ms before retry...`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }
      }

      // If all retries failed, throw the last error
      console.error(`❌ All Supabase request attempts failed:`, lastError);
      throw lastError || new Error('Request failed after all retries');
    }
  },
  db: {
    schema: 'public'
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
});