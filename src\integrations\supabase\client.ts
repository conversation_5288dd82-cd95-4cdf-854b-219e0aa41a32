import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

// Use environment variables with fallback to production
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || "https://dvflgnqwbsjityrowatf.supabase.co";
const SUPABASE_ANON_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";

// Log which database we're connecting to
console.log('🔗 Supabase Client Configuration:', {
  url: SUPABASE_URL,
  environment: import.meta.env.MODE,
  isLocal: SUPABASE_URL.includes('localhost') || SUPABASE_URL.includes('127.0.0.1'),
  isProduction: SUPABASE_URL.includes('supabase.co')
});

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});