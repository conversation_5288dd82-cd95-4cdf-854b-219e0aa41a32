/**
 * Google Maps Location Service
 * Enhanced location service using Google Maps API and browser geolocation
 */

export interface LocationData {
  address: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  coordinates: {
    lat: number;
    lng: number;
  };
}

export class GoogleMapsLocationService {
  private static instance: GoogleMapsLocationService;
  private isGoogleMapsLoaded = false;
  private locationCache = new Map<string, { data: LocationData; timestamp: number }>();
  private readonly CACHE_DURATION = 30 * 60 * 1000; // 30 minutes

  static getInstance(): GoogleMapsLocationService {
    if (!GoogleMapsLocationService.instance) {
      GoogleMapsLocationService.instance = new GoogleMapsLocationService();
    }
    return GoogleMapsLocationService.instance;
  }

  /**
   * Load Google Maps API if not already loaded
   */
  private async loadGoogleMapsAPI(): Promise<boolean> {
    if (this.isGoogleMapsLoaded || (window as any).google?.maps) {
      this.isGoogleMapsLoaded = true;
      return true;
    }

    try {
      // Check if we have a Google Maps API key
      const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
      if (!apiKey) {
        console.log('ℹ️ Google Maps API key not found, using fallback location services');
        return false;
      }

      return new Promise((resolve) => {
        const script = document.createElement('script');
        script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
        script.async = true;
        script.defer = true;
        
        script.onload = () => {
          this.isGoogleMapsLoaded = true;
          console.log('✅ Google Maps API loaded');
          resolve(true);
        };
        
        script.onerror = () => {
          console.warn('⚠️ Google Maps API failed to load');
          resolve(false);
        };
        
        document.head.appendChild(script);
      });
    } catch (error) {
      console.warn('⚠️ Google Maps API loading failed:', error);
      return false;
    }
  }

  /**
   * Get current location using browser geolocation
   */
  async getCurrentLocation(): Promise<LocationData | null> {
    try {
      console.log('📍 Getting current location...');

      // Try to get GPS coordinates first
      const position = await this.getGPSPosition();
      if (!position) {
        return this.getDefaultLocation();
      }

      // Try Google Maps geocoding first
      const googleLocation = await this.reverseGeocodeWithGoogle(position.lat, position.lng);
      if (googleLocation) {
        return googleLocation;
      }

      // Fallback to other services
      return await this.reverseGeocodeWithFallback(position.lat, position.lng);
    } catch (error) {
      console.error('❌ Location service failed:', error);
      return this.getDefaultLocation();
    }
  }

  /**
   * Get GPS position with timeout
   */
  private async getGPSPosition(): Promise<{ lat: number; lng: number } | null> {
    return new Promise((resolve) => {
      if (!navigator.geolocation) {
        console.warn('⚠️ Geolocation not supported');
        resolve(null);
        return;
      }

      const timeoutId = setTimeout(() => {
        console.warn('⚠️ GPS timeout');
        resolve(null);
      }, 10000);

      navigator.geolocation.getCurrentPosition(
        (position) => {
          clearTimeout(timeoutId);
          console.log('✅ GPS position obtained');
          resolve({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          });
        },
        (error) => {
          clearTimeout(timeoutId);
          console.warn('⚠️ GPS error:', error.message);
          resolve(null);
        },
        {
          enableHighAccuracy: true,
          timeout: 8000,
          maximumAge: 5 * 60 * 1000
        }
      );
    });
  }

  /**
   * Reverse geocode using Google Maps API
   */
  private async reverseGeocodeWithGoogle(lat: number, lng: number): Promise<LocationData | null> {
    try {
      const isLoaded = await this.loadGoogleMapsAPI();
      if (!isLoaded || !(window as any).google?.maps) {
        return null;
      }

      const cacheKey = `google_${lat.toFixed(4)},${lng.toFixed(4)}`;
      const cached = this.locationCache.get(cacheKey);
      if (cached && (Date.now() - cached.timestamp) < this.CACHE_DURATION) {
        console.log('✅ Using cached Google Maps location data');
        return cached.data;
      }

      return new Promise((resolve) => {
        const geocoder = new (window as any).google.maps.Geocoder();
        const latlng = { lat, lng };

        geocoder.geocode({ location: latlng }, (results: any[], status: string) => {
          if (status === 'OK' && results[0]) {
            const result = results[0];
            const components = result.address_components;
            
            const locationData: LocationData = {
              address: result.formatted_address,
              city: this.extractComponent(components, 'locality') || 
                    this.extractComponent(components, 'administrative_area_level_2') || 
                    'Unknown City',
              state: this.extractComponent(components, 'administrative_area_level_1') || 'Unknown State',
              country: this.extractComponent(components, 'country') || 'Unknown Country',
              postalCode: this.extractComponent(components, 'postal_code') || '',
              coordinates: { lat, lng }
            };

            // Cache the result
            this.locationCache.set(cacheKey, {
              data: locationData,
              timestamp: Date.now()
            });

            console.log('✅ Location from Google Maps:', locationData.city);
            resolve(locationData);
          } else {
            console.warn('⚠️ Google Maps geocoding failed:', status);
            resolve(null);
          }
        });
      });
    } catch (error) {
      console.warn('⚠️ Google Maps geocoding error:', error);
      return null;
    }
  }

  /**
   * Extract component from Google Maps address components
   */
  private extractComponent(components: any[], type: string): string | null {
    const component = components.find(comp => comp.types.includes(type));
    return component ? component.long_name : null;
  }

  /**
   * Fallback reverse geocoding using free services
   */
  private async reverseGeocodeWithFallback(lat: number, lng: number): Promise<LocationData | null> {
    const services = [
      {
        name: 'nominatim',
        url: `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`,
        headers: { 'User-Agent': 'CTNL-AI-Workboard/1.0' },
        parser: (data: any): LocationData => ({
          address: data.display_name || 'Unknown Location',
          city: data.address?.city || data.address?.town || data.address?.village || 'Unknown City',
          state: data.address?.state || 'Unknown State',
          country: data.address?.country || 'Unknown Country',
          postalCode: data.address?.postcode || '',
          coordinates: { lat, lng }
        })
      }
    ];

    for (const service of services) {
      try {
        console.log(`🌍 Trying ${service.name} geocoding service...`);
        
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 8000);

        const response = await fetch(service.url, {
          headers: service.headers,
          signal: controller.signal,
          mode: 'cors'
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          console.warn(`${service.name} returned ${response.status}`);
          continue;
        }

        const data = await response.json();
        const locationData = service.parser(data);
        
        console.log(`✅ Location from ${service.name}:`, locationData.city);
        return locationData;
      } catch (error: any) {
        console.warn(`❌ ${service.name} failed:`, error.message);
        continue;
      }
    }

    return null;
  }

  /**
   * Get default location when all services fail
   */
  private getDefaultLocation(): LocationData {
    console.log('📍 Using default location (Lagos, Nigeria)');
    return {
      address: 'Lagos, Nigeria',
      city: 'Lagos',
      state: 'Lagos State',
      country: 'Nigeria',
      postalCode: '100001',
      coordinates: { lat: 6.5244, lng: 3.3792 }
    };
  }

  /**
   * Search for places using Google Maps Places API
   */
  async searchPlaces(query: string): Promise<any[]> {
    try {
      const isLoaded = await this.loadGoogleMapsAPI();
      if (!isLoaded || !(window as any).google?.maps) {
        console.warn('⚠️ Google Maps not available for place search');
        return [];
      }

      return new Promise((resolve) => {
        const service = new (window as any).google.maps.places.PlacesService(
          document.createElement('div')
        );

        service.textSearch(
          {
            query,
            location: new (window as any).google.maps.LatLng(6.5244, 3.3792), // Lagos center
            radius: 50000 // 50km radius
          },
          (results: any[], status: string) => {
            if (status === 'OK' && results) {
              console.log(`✅ Found ${results.length} places for "${query}"`);
              resolve(results);
            } else {
              console.warn('⚠️ Place search failed:', status);
              resolve([]);
            }
          }
        );
      });
    } catch (error) {
      console.warn('⚠️ Place search error:', error);
      return [];
    }
  }

  /**
   * Clear location cache
   */
  clearCache(): void {
    this.locationCache.clear();
    console.log('🧹 Location cache cleared');
  }

  /**
   * Check if Google Maps is available
   */
  isGoogleMapsAvailable(): boolean {
    return this.isGoogleMapsLoaded && !!(window as any).google?.maps;
  }
}

// Global instance
export const googleMapsLocationService = GoogleMapsLocationService.getInstance();

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).googleMapsLocationService = googleMapsLocationService;
  (window as any).getCurrentLocationGoogle = () => googleMapsLocationService.getCurrentLocation();
  (window as any).searchPlaces = (query: string) => googleMapsLocationService.searchPlaces(query);
  
  console.log('🗺️ Google Maps Location Service loaded. Available commands:');
  console.log('  - getCurrentLocationGoogle()');
  console.log('  - searchPlaces("query")');
}
