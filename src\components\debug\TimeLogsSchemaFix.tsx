import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { AlertCircle, CheckCircle, Clock, Database, RefreshCw } from 'lucide-react';
import { timeLogsSchemaFixer } from '@/utils/time-logs-schema-fixer';

interface FixResult {
  success: boolean;
  message: string;
  details: string[];
}

export function TimeLogsSchemaFix() {
  const [isChecking, setIsChecking] = useState(false);
  const [isFixing, setIsFixing] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [checkResult, setCheckResult] = useState<FixResult | null>(null);
  const [fixResult, setFixResult] = useState<FixResult | null>(null);
  const [testResult, setTestResult] = useState<FixResult | null>(null);

  const handleCheckSchema = async () => {
    setIsChecking(true);
    setCheckResult(null);
    
    try {
      const result = await timeLogsSchemaFixer.checkCurrentSchema();
      setCheckResult(result);
    } catch (error) {
      setCheckResult({
        success: false,
        message: `Check failed: ${error}`,
        details: []
      });
    } finally {
      setIsChecking(false);
    }
  };

  const handleFixSchema = async () => {
    setIsFixing(true);
    setFixResult(null);
    
    try {
      const result = await timeLogsSchemaFixer.fixTimeLogsSchema();
      setFixResult(result);
      
      // Auto-refresh check result after fix
      if (result.success) {
        setTimeout(() => {
          handleCheckSchema();
        }, 1000);
      }
    } catch (error) {
      setFixResult({
        success: false,
        message: `Fix failed: ${error}`,
        details: []
      });
    } finally {
      setIsFixing(false);
    }
  };

  const handleTestQueries = async () => {
    setIsTesting(true);
    setTestResult(null);
    
    try {
      const result = await timeLogsSchemaFixer.testTimeLogsQueries();
      setTestResult(result);
    } catch (error) {
      setTestResult({
        success: false,
        message: `Test failed: ${error}`,
        details: []
      });
    } finally {
      setIsTesting(false);
    }
  };

  const renderResult = (result: FixResult | null, title: string) => {
    if (!result) return null;

    return (
      <div className="mt-4 p-4 border rounded-lg">
        <div className="flex items-center gap-2 mb-2">
          {result.success ? (
            <CheckCircle className="h-4 w-4 text-green-500" />
          ) : (
            <AlertCircle className="h-4 w-4 text-red-500" />
          )}
          <span className="font-medium">{title}</span>
          <Badge variant={result.success ? "default" : "destructive"}>
            {result.success ? "Success" : "Failed"}
          </Badge>
        </div>
        
        <p className="text-sm text-muted-foreground mb-2">{result.message}</p>
        
        {result.details.length > 0 && (
          <div className="mt-2">
            <details className="text-xs">
              <summary className="cursor-pointer text-muted-foreground hover:text-foreground">
                View Details ({result.details.length} items)
              </summary>
              <div className="mt-2 p-2 bg-muted rounded text-xs font-mono whitespace-pre-wrap">
                {result.details.join('\n')}
              </div>
            </details>
          </div>
        )}
      </div>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          <CardTitle>Time Logs Schema Fix</CardTitle>
        </div>
        <CardDescription>
          Fix time_logs table schema issues causing 400 errors. The application expects 'clock_in' and 'clock_out' columns, 
          but some migrations created 'clock_in_time' and 'clock_out_time' instead.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button 
            onClick={handleCheckSchema}
            disabled={isChecking}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Database className="h-4 w-4" />
            {isChecking ? 'Checking...' : 'Check Schema'}
          </Button>
          
          <Button 
            onClick={handleFixSchema}
            disabled={isFixing}
            variant="default"
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isFixing ? 'animate-spin' : ''}`} />
            {isFixing ? 'Fixing...' : 'Fix Schema'}
          </Button>
          
          <Button 
            onClick={handleTestQueries}
            disabled={isTesting}
            variant="secondary"
            className="flex items-center gap-2"
          >
            <CheckCircle className="h-4 w-4" />
            {isTesting ? 'Testing...' : 'Test Queries'}
          </Button>
        </div>

        <Separator />

        <div className="space-y-4">
          <div className="text-sm text-muted-foreground">
            <p><strong>Common Issues:</strong></p>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>Column 'clock_out' does not exist (should be renamed from 'clock_out_time')</li>
              <li>Column 'clock_in' does not exist (should be renamed from 'clock_in_time')</li>
              <li>Missing user_id foreign key reference</li>
              <li>Missing RLS policies for data security</li>
            </ul>
          </div>

          {renderResult(checkResult, "Schema Check")}
          {renderResult(fixResult, "Schema Fix")}
          {renderResult(testResult, "Query Test")}
        </div>

        <Separator />

        <div className="text-xs text-muted-foreground">
          <p><strong>What this fix does:</strong></p>
          <ul className="list-disc list-inside mt-1 space-y-1">
            <li>Renames 'clock_in_time' to 'clock_in' and 'clock_out_time' to 'clock_out'</li>
            <li>Adds missing columns if they don't exist</li>
            <li>Ensures proper foreign key relationships</li>
            <li>Creates necessary indexes for performance</li>
            <li>Sets up Row Level Security (RLS) policies</li>
            <li>Tests the queries that were failing with 400 errors</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}