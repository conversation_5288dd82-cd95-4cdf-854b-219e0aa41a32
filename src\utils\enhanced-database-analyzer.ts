/**
 * Enhanced Database Analyzer
 * Comprehensive database analysis and foreign key fixes
 */

import { supabase } from '@/integrations/supabase/client';

export interface DatabaseAnalysisResult {
  table: string;
  status: 'healthy' | 'warning' | 'error' | 'missing';
  issues: string[];
  fixes: string[];
  foreignKeys: ForeignKeyStatus[];
  schema: TableSchema | null;
}

export interface ForeignKeyStatus {
  column: string;
  referencedTable: string;
  referencedColumn: string;
  status: 'working' | 'broken' | 'missing';
  error?: string;
}

export interface TableSchema {
  columns: string[];
  primaryKey: string[];
  foreignKeys: string[];
  indexes: string[];
}

export class EnhancedDatabaseAnalyzer {
  private static results: DatabaseAnalysisResult[] = [];

  /**
   * Run comprehensive database analysis
   */
  static async runFullAnalysis(): Promise<DatabaseAnalysisResult[]> {
    this.results = [];
    console.log('🔍 Running enhanced database analysis...');

    const coreTables = [
      'profiles',
      'departments',
      'projects', 
      'tasks',
      'invoices',
      'memos',
      'time_logs',
      'project_assignments',
      'document_archive',
      'notifications',
      'api_keys',
      'integrations'
    ];

    for (const table of coreTables) {
      await this.analyzeTable(table);
    }

    console.log(`✅ Enhanced analysis complete. Analyzed ${this.results.length} tables.`);
    return this.results;
  }

  /**
   * Analyze individual table
   */
  private static async analyzeTable(tableName: string): Promise<void> {
    const result: DatabaseAnalysisResult = {
      table: tableName,
      status: 'healthy',
      issues: [],
      fixes: [],
      foreignKeys: [],
      schema: null
    };

    try {
      // Check if table exists
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1);

      if (error) {
        if (error.code === 'PGRST116' || error.message.includes('does not exist')) {
          result.status = 'missing';
          result.issues.push(`Table ${tableName} does not exist`);
          result.fixes.push(`Create ${tableName} table with proper schema`);
        } else {
          result.status = 'error';
          result.issues.push(`Access error: ${error.message}`);
          result.fixes.push(`Check RLS policies for ${tableName}`);
        }
      } else {
        // Table exists, analyze schema
        if (data && data.length > 0) {
          result.schema = {
            columns: Object.keys(data[0]),
            primaryKey: ['id'], // Assume id is primary key
            foreignKeys: [],
            indexes: []
          };
        }

        // Test foreign key relationships
        await this.testForeignKeys(tableName, result);
        
        // Check for common issues
        await this.checkCommonIssues(tableName, result);
      }
    } catch (err: any) {
      result.status = 'error';
      result.issues.push(`Analysis failed: ${err.message}`);
    }

    this.results.push(result);
  }

  /**
   * Test foreign key relationships for a table
   */
  private static async testForeignKeys(tableName: string, result: DatabaseAnalysisResult): Promise<void> {
    const foreignKeyTests = {
      profiles: [
        { column: 'department_id', referencedTable: 'departments', referencedColumn: 'id' }
      ],
      projects: [
        { column: 'manager_id', referencedTable: 'profiles', referencedColumn: 'id' },
        { column: 'department_id', referencedTable: 'departments', referencedColumn: 'id' },
        { column: 'created_by', referencedTable: 'profiles', referencedColumn: 'id' }
      ],
      tasks: [
        { column: 'assigned_to_id', referencedTable: 'profiles', referencedColumn: 'id' },
        { column: 'created_by', referencedTable: 'profiles', referencedColumn: 'id' },
        { column: 'project_id', referencedTable: 'projects', referencedColumn: 'id' }
      ],
      project_assignments: [
        { column: 'project_id', referencedTable: 'projects', referencedColumn: 'id' },
        { column: 'assigned_to', referencedTable: 'profiles', referencedColumn: 'id' },
        { column: 'assigned_by', referencedTable: 'profiles', referencedColumn: 'id' }
      ],
      invoices: [
        { column: 'created_by', referencedTable: 'profiles', referencedColumn: 'id' }
      ],
      memos: [
        { column: 'created_by', referencedTable: 'profiles', referencedColumn: 'id' }
      ],
      time_logs: [
        { column: 'user_id', referencedTable: 'profiles', referencedColumn: 'id' },
        { column: 'project_id', referencedTable: 'projects', referencedColumn: 'id' }
      ]
    };

    const tests = foreignKeyTests[tableName as keyof typeof foreignKeyTests] || [];

    for (const test of tests) {
      try {
        const query = `${test.column}:${test.referencedTable}!${tableName}_${test.column}_fkey(${test.referencedColumn})`;
        
        const { data, error } = await supabase
          .from(tableName)
          .select(query)
          .limit(1);

        const fkStatus: ForeignKeyStatus = {
          column: test.column,
          referencedTable: test.referencedTable,
          referencedColumn: test.referencedColumn,
          status: error ? 'broken' : 'working',
          error: error?.message
        };

        if (error && error.message.includes('relationship')) {
          result.issues.push(`Foreign key ${test.column} -> ${test.referencedTable}.${test.referencedColumn} is broken`);
          result.fixes.push(`Fix foreign key constraint for ${test.column}`);
          if (result.status === 'healthy') result.status = 'warning';
        }

        result.foreignKeys.push(fkStatus);
      } catch (err: any) {
        result.foreignKeys.push({
          column: test.column,
          referencedTable: test.referencedTable,
          referencedColumn: test.referencedColumn,
          status: 'broken',
          error: err.message
        });
      }
    }
  }

  /**
   * Check for common database issues
   */
  private static async checkCommonIssues(tableName: string, result: DatabaseAnalysisResult): Promise<void> {
    // Check for required columns
    const requiredColumns = {
      profiles: ['id', 'full_name', 'email', 'role'],
      projects: ['id', 'name', 'status'],
      tasks: ['id', 'title', 'status'],
      invoices: ['id', 'invoice_number', 'client_name'],
      departments: ['id', 'name']
    };

    const required = requiredColumns[tableName as keyof typeof requiredColumns];
    if (required && result.schema) {
      const missing = required.filter(col => !result.schema!.columns.includes(col));
      if (missing.length > 0) {
        result.issues.push(`Missing required columns: ${missing.join(', ')}`);
        result.fixes.push(`Add missing columns: ${missing.join(', ')}`);
        if (result.status === 'healthy') result.status = 'warning';
      }
    }

    // Check for common timestamp columns
    if (result.schema && !result.schema.columns.includes('created_at')) {
      result.issues.push('Missing created_at timestamp column');
      result.fixes.push('Add created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()');
    }

    if (result.schema && !result.schema.columns.includes('updated_at')) {
      result.issues.push('Missing updated_at timestamp column');
      result.fixes.push('Add updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()');
    }
  }

  /**
   * Generate SQL fixes for database issues
   */
  static generateSQLFixes(results: DatabaseAnalysisResult[]): string[] {
    const sqlFixes: string[] = [];

    for (const result of results) {
      if (result.status === 'missing') {
        sqlFixes.push(this.generateCreateTableSQL(result.table));
      }

      // Generate foreign key fixes
      for (const fk of result.foreignKeys) {
        if (fk.status === 'broken') {
          sqlFixes.push(
            `-- Fix foreign key ${result.table}.${fk.column} -> ${fk.referencedTable}.${fk.referencedColumn}
ALTER TABLE ${result.table} 
ADD CONSTRAINT ${result.table}_${fk.column}_fkey 
FOREIGN KEY (${fk.column}) 
REFERENCES ${fk.referencedTable}(${fk.referencedColumn});`
          );
        }
      }

      // Generate column fixes
      if (result.schema && result.issues.some(issue => issue.includes('Missing'))) {
        const missingColumns = result.issues
          .filter(issue => issue.startsWith('Missing required columns:'))
          .map(issue => issue.replace('Missing required columns: ', '').split(', '))
          .flat();

        for (const column of missingColumns) {
          sqlFixes.push(
            `-- Add missing column ${column} to ${result.table}
ALTER TABLE ${result.table} ADD COLUMN ${column} TEXT;`
          );
        }
      }
    }

    return sqlFixes;
  }

  /**
   * Generate CREATE TABLE SQL for missing tables
   */
  private static generateCreateTableSQL(tableName: string): string {
    const tableSchemas = {
      departments: `
CREATE TABLE IF NOT EXISTS public.departments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    manager_id UUID REFERENCES public.profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);`,
      
      document_archive: `
CREATE TABLE IF NOT EXISTS public.document_archive (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_path TEXT,
    file_size INTEGER,
    file_type TEXT,
    uploaded_by UUID REFERENCES public.profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);`,

      notifications: `
CREATE TABLE IF NOT EXISTS public.notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    message TEXT,
    data JSONB DEFAULT '{}',
    read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);`
    };

    return tableSchemas[tableName as keyof typeof tableSchemas] || 
           `-- TODO: Define schema for ${tableName} table`;
  }

  /**
   * Auto-fix database issues
   */
  static async autoFixIssues(results: DatabaseAnalysisResult[]): Promise<{
    fixed: number;
    failed: number;
    errors: string[];
  }> {
    let fixed = 0;
    let failed = 0;
    const errors: string[] = [];

    console.log('🔧 Auto-fixing database issues...');

    for (const result of results) {
      try {
        if (result.status === 'missing' && ['departments', 'document_archive', 'notifications'].includes(result.table)) {
          const sql = this.generateCreateTableSQL(result.table);
          
          // Note: In a real implementation, you would execute this SQL
          // For now, we'll just log it
          console.log(`Would execute: ${sql}`);
          fixed++;
        }
      } catch (error: any) {
        failed++;
        errors.push(`Failed to fix ${result.table}: ${error.message}`);
      }
    }

    console.log(`✅ Auto-fix complete: ${fixed} fixed, ${failed} failed`);
    return { fixed, failed, errors };
  }

  /**
   * Generate comprehensive report
   */
  static generateReport(results: DatabaseAnalysisResult[]): string {
    const healthy = results.filter(r => r.status === 'healthy');
    const warnings = results.filter(r => r.status === 'warning');
    const errors = results.filter(r => r.status === 'error');
    const missing = results.filter(r => r.status === 'missing');

    let report = '📊 ENHANCED DATABASE ANALYSIS REPORT\n';
    report += '=====================================\n\n';
    
    report += `📈 SUMMARY:\n`;
    report += `✅ Healthy: ${healthy.length}\n`;
    report += `⚠️ Warnings: ${warnings.length}\n`;
    report += `❌ Errors: ${errors.length}\n`;
    report += `❓ Missing: ${missing.length}\n\n`;

    if (missing.length > 0) {
      report += `❓ MISSING TABLES:\n`;
      missing.forEach(result => {
        report += `  - ${result.table}\n`;
        result.fixes.forEach(fix => report += `    Fix: ${fix}\n`);
      });
      report += '\n';
    }

    if (errors.length > 0) {
      report += `❌ ERROR TABLES:\n`;
      errors.forEach(result => {
        report += `  - ${result.table}:\n`;
        result.issues.forEach(issue => report += `    Issue: ${issue}\n`);
        result.fixes.forEach(fix => report += `    Fix: ${fix}\n`);
      });
      report += '\n';
    }

    if (warnings.length > 0) {
      report += `⚠️ WARNING TABLES:\n`;
      warnings.forEach(result => {
        report += `  - ${result.table}:\n`;
        result.issues.forEach(issue => report += `    Issue: ${issue}\n`);
        result.foreignKeys.filter(fk => fk.status === 'broken').forEach(fk => {
          report += `    Broken FK: ${fk.column} -> ${fk.referencedTable}.${fk.referencedColumn}\n`;
        });
      });
      report += '\n';
    }

    if (healthy.length > 0) {
      report += `✅ HEALTHY TABLES:\n`;
      healthy.forEach(result => {
        const workingFKs = result.foreignKeys.filter(fk => fk.status === 'working').length;
        report += `  - ${result.table} (${result.schema?.columns.length || 0} columns, ${workingFKs} working FKs)\n`;
      });
    }

    return report;
  }
}

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).EnhancedDatabaseAnalyzer = EnhancedDatabaseAnalyzer;
  (window as any).runEnhancedDatabaseAnalysis = () => EnhancedDatabaseAnalyzer.runFullAnalysis();
  
  console.log('🔍 Enhanced Database Analyzer loaded. Available commands:');
  console.log('  - runEnhancedDatabaseAnalysis()');
}
