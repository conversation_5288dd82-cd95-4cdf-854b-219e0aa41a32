/**
 * Fix Authentication Issues
 * Utility to handle authentication token errors and refresh token issues
 */

import { supabase } from '@/integrations/supabase/client';

export class AuthIssuesFixer {
  private static instance: AuthIssuesFixer;
  private isFixed = false;

  static getInstance(): AuthIssuesFixer {
    if (!AuthIssuesFixer.instance) {
      AuthIssuesFixer.instance = new AuthIssuesFixer();
    }
    return AuthIssuesFixer.instance;
  }

  /**
   * Fix all authentication issues
   */
  async fixAllAuthIssues(): Promise<{ success: boolean; results: string[] }> {
    const results: string[] = [];

    try {
      console.log('🔧 Starting authentication fixes...');

      // Fix 1: Handle invalid refresh tokens
      await this.fixInvalidRefreshTokens();
      results.push('Fixed invalid refresh token handling');

      // Fix 2: Clear corrupted session data
      await this.clearCorruptedSessionData();
      results.push('Cleared corrupted session data');

      // Fix 3: Setup proper error handling for auth errors
      this.setupAuthErrorHandling();
      results.push('Setup enhanced auth error handling');

      // Fix 4: Handle 400 auth errors gracefully
      this.handleAuthErrors();
      results.push('Added graceful auth error handling');

      console.log('✅ Authentication fixes completed');
      this.isFixed = true;
      return { success: true, results };

    } catch (error: any) {
      console.error('❌ Error during authentication fixes:', error.message);
      results.push(`Error during fixes: ${error.message}`);
      return { success: false, results };
    }
  }

  /**
   * Fix invalid refresh token issues
   */
  private async fixInvalidRefreshTokens(): Promise<void> {
    try {
      console.log('🔧 Fixing invalid refresh tokens...');

      // Check current session
      const { data: { session }, error } = await supabase.auth.getSession();

      if (error) {
        console.log('⚠️ Session error detected, clearing session:', error.message);
        
        // Clear the session if it's invalid
        await supabase.auth.signOut();
        
        // Clear any stored tokens
        localStorage.removeItem('supabase.auth.token');
        sessionStorage.removeItem('supabase.auth.token');
        
        console.log('✅ Cleared invalid session');
        return;
      }

      if (!session) {
        console.log('ℹ️ No active session found');
        return;
      }

      // Try to refresh the session
      const { error: refreshError } = await supabase.auth.refreshSession();
      
      if (refreshError) {
        console.log('⚠️ Refresh failed, clearing session:', refreshError.message);
        await supabase.auth.signOut();
      } else {
        console.log('✅ Session refreshed successfully');
      }

    } catch (error: any) {
      console.warn('⚠️ Error fixing refresh tokens:', error.message);
      // Clear session on any error
      await supabase.auth.signOut();
    }
  }

  /**
   * Clear corrupted session data
   */
  private async clearCorruptedSessionData(): Promise<void> {
    try {
      console.log('🔧 Clearing corrupted session data...');

      // Clear all auth-related localStorage items
      const authKeys = Object.keys(localStorage).filter(key => 
        key.includes('supabase') || key.includes('auth') || key.includes('token')
      );

      authKeys.forEach(key => {
        localStorage.removeItem(key);
      });

      // Clear sessionStorage as well
      const sessionAuthKeys = Object.keys(sessionStorage).filter(key => 
        key.includes('supabase') || key.includes('auth') || key.includes('token')
      );

      sessionAuthKeys.forEach(key => {
        sessionStorage.removeItem(key);
      });

      console.log('✅ Cleared corrupted session data');

    } catch (error: any) {
      console.warn('⚠️ Error clearing session data:', error.message);
    }
  }

  /**
   * Setup enhanced auth error handling
   */
  private setupAuthErrorHandling(): void {
    try {
      console.log('🔧 Setting up auth error handling...');

      // Listen for auth state changes
      supabase.auth.onAuthStateChange((event, session) => {
        console.log('🔄 Auth state changed:', event);

        if (event === 'SIGNED_OUT') {
          // Clear any remaining session data
          localStorage.removeItem('supabase.auth.token');
          sessionStorage.removeItem('supabase.auth.token');
        }

        if (event === 'TOKEN_REFRESHED') {
          console.log('✅ Token refreshed successfully');
        }

        if (event === 'SIGNED_IN' && session) {
          console.log('✅ User signed in successfully');
        }
      });

      console.log('✅ Auth error handling setup complete');

    } catch (error: any) {
      console.warn('⚠️ Error setting up auth error handling:', error.message);
    }
  }

  /**
   * Handle auth errors gracefully (console override disabled)
   */
  private handleAuthErrors(): void {
    try {
      console.log('🔧 Setting up graceful auth error handling...');

      // Don't override console.error as it causes cascading issues

      // Add global error handler for unhandled auth errors
      window.addEventListener('unhandledrejection', (event) => {
        if (event.reason && event.reason.message) {
          const message = event.reason.message;
          
          if (message.includes('Invalid Refresh Token') || 
              message.includes('Refresh Token Not Found')) {
            console.warn('🔧 Caught and handled auth error:', message);
            event.preventDefault();
            
            // Clear session on auth errors
            supabase.auth.signOut();
            return false;
          }
        }
      });

      console.log('✅ Graceful auth error handling setup complete');

    } catch (error: any) {
      console.warn('⚠️ Error setting up graceful auth error handling:', error.message);
    }
  }

  /**
   * Check if fixes have been applied
   */
  isAuthFixed(): boolean {
    return this.isFixed;
  }

  /**
   * Reset fix status
   */
  resetFixStatus(): void {
    this.isFixed = false;
  }
}

// Export singleton instance
export const authIssuesFixer = AuthIssuesFixer.getInstance();

// Export utility function
export const fixAuthIssues = () => authIssuesFixer.fixAllAuthIssues();

// Auto-apply fixes when module loads
if (typeof window !== 'undefined') {
  // Apply fixes after a short delay to ensure Supabase is initialized
  setTimeout(() => {
    authIssuesFixer.fixAllAuthIssues();
  }, 1000);
}
