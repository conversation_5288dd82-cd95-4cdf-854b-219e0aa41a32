/**
 * <PERSON><PERSON><PERSON><PERSON> RAG (Retrieval-Augmented Generation) System
 * Provides context-aware responses using document retrieval
 */

import { supabase } from '@/integrations/supabase/client';
import { langChainConfig } from './config';
import { langChainDocumentProcessor } from './document-processor';

export interface RAGQuery {
  question: string;
  context?: Record<string, any>;
  maxResults?: number;
  threshold?: number;
  filters?: Record<string, any>;
}

export interface RAGResponse {
  answer: string;
  sources: Array<{
    id: string;
    content: string;
    title?: string;
    similarity: number;
    metadata: Record<string, any>;
  }>;
  confidence: number;
  processingTime: number;
  tokensUsed: number;
}

export interface RAGContext {
  documents: Array<{
    content: string;
    metadata: Record<string, any>;
  }>;
  totalSources: number;
  averageSimilarity: number;
}

export class LangChainRAGSystem {
  /**
   * Process RAG query and generate response
   */
  static async query(ragQuery: RAGQuery): Promise<RAGResponse> {
    const startTime = Date.now();
    
    try {
      // Retrieve relevant documents
      const relevantDocs = await this.retrieveDocuments(ragQuery);
      
      if (relevantDocs.length === 0) {
        return {
          answer: "I don't have enough information in my knowledge base to answer that question. Could you provide more context or ask about something else?",
          sources: [],
          confidence: 0.1,
          processingTime: Date.now() - startTime,
          tokensUsed: 0,
        };
      }

      // Build context from retrieved documents
      const context = this.buildContext(relevantDocs);
      
      // Generate response using LLM
      const response = await this.generateResponse(ragQuery.question, context);
      
      return {
        answer: response.answer,
        sources: relevantDocs.map(doc => ({
          id: doc.id,
          content: doc.content,
          title: doc.metadata.title,
          similarity: doc.similarity || 0,
          metadata: doc.metadata,
        })),
        confidence: this.calculateConfidence(relevantDocs),
        processingTime: Date.now() - startTime,
        tokensUsed: response.tokensUsed || 0,
      };
    } catch (error) {
      console.error('RAG query failed:', error);
      throw error;
    }
  }

  /**
   * Retrieve relevant documents for the query
   */
  private static async retrieveDocuments(ragQuery: RAGQuery): Promise<any[]> {
    const config = langChainConfig.getConfig();
    const maxResults = ragQuery.maxResults || config.rag.maxResults;
    const threshold = ragQuery.threshold || config.vectorStore.similarityThreshold;

    try {
      // Use document processor for similarity search
      const documents = await langChainDocumentProcessor.searchSimilar(
        ragQuery.question,
        maxResults,
        threshold
      );

      // Apply additional filters if provided
      if (ragQuery.filters) {
        return documents.filter(doc => this.matchesFilters(doc, ragQuery.filters!));
      }

      return documents;
    } catch (error) {
      console.error('Document retrieval failed:', error);
      return [];
    }
  }

  /**
   * Build context from retrieved documents
   */
  private static buildContext(documents: any[]): RAGContext {
    const context: RAGContext = {
      documents: documents.map(doc => ({
        content: doc.content,
        metadata: doc.metadata,
      })),
      totalSources: documents.length,
      averageSimilarity: documents.reduce((sum, doc) => sum + (doc.similarity || 0), 0) / documents.length,
    };

    return context;
  }

  /**
   * Generate response using LLM with context
   */
  private static async generateResponse(
    question: string,
    context: RAGContext
  ): Promise<{ answer: string; tokensUsed: number }> {
    try {
      // Build prompt with context
      const prompt = this.buildRAGPrompt(question, context);
      
      // Call LLM via Supabase Edge Function
      const { data, error } = await supabase.functions.invoke('langchain-rag', {
        body: {
          question,
          context: context.documents,
          prompt,
        },
      });

      if (error) {
        throw error;
      }

      return {
        answer: data.answer || "I couldn't generate a response based on the available information.",
        tokensUsed: data.tokensUsed || 0,
      };
    } catch (error) {
      console.error('Response generation failed:', error);
      return {
        answer: "I encountered an error while processing your question. Please try again.",
        tokensUsed: 0,
      };
    }
  }

  /**
   * Build RAG prompt template
   */
  private static buildRAGPrompt(question: string, context: RAGContext): string {
    const contextText = context.documents
      .map((doc, index) => `[Source ${index + 1}]: ${doc.content}`)
      .join('\n\n');

    return `You are an AI assistant for CTNL AI Workboard. Answer the question based on the provided context.

Context Information:
${contextText}

Question: ${question}

Instructions:
1. Answer based primarily on the provided context
2. If the context doesn't contain enough information, say so clearly
3. Cite sources when possible using [Source X] notation
4. Be concise but comprehensive
5. If asked about CTNL AI Workboard features, provide helpful guidance

Answer:`;
  }

  /**
   * Calculate confidence score based on retrieved documents
   */
  private static calculateConfidence(documents: any[]): number {
    if (documents.length === 0) return 0;
    
    const avgSimilarity = documents.reduce((sum, doc) => sum + (doc.similarity || 0), 0) / documents.length;
    const sourceCount = Math.min(documents.length / 5, 1); // Normalize to 0-1
    
    return Math.min((avgSimilarity * 0.7) + (sourceCount * 0.3), 1);
  }

  /**
   * Check if document matches filters
   */
  private static matchesFilters(document: any, filters: Record<string, any>): boolean {
    for (const [key, value] of Object.entries(filters)) {
      const docValue = document.metadata[key];
      if (docValue !== value) {
        return false;
      }
    }
    return true;
  }

  /**
   * Add document to RAG knowledge base
   */
  static async addDocument(
    content: string,
    metadata: Record<string, any> = {}
  ): Promise<string> {
    try {
      const processedDoc = await langChainDocumentProcessor.processDocument(content, {
        ...metadata,
        addedToRAG: true,
        addedAt: new Date().toISOString(),
      });

      return processedDoc.id;
    } catch (error) {
      console.error('Failed to add document to RAG:', error);
      throw error;
    }
  }

  /**
   * Remove document from RAG knowledge base
   */
  static async removeDocument(documentId: string): Promise<void> {
    try {
      await langChainDocumentProcessor.deleteDocument(documentId);
    } catch (error) {
      console.error('Failed to remove document from RAG:', error);
      throw error;
    }
  }

  /**
   * Get RAG system statistics
   */
  static async getStatistics(): Promise<{
    totalDocuments: number;
    totalChunks: number;
    averageChunksPerDocument: number;
    recentQueries: number;
  }> {
    try {
      const { data: docStats, error: docError } = await supabase
        .from('langchain_document_metadata')
        .select('chunk_count');

      if (docError) {
        console.error('Failed to get document stats:', docError);
        return { totalDocuments: 0, totalChunks: 0, averageChunksPerDocument: 0, recentQueries: 0 };
      }

      const totalDocuments = docStats?.length || 0;
      const totalChunks = docStats?.reduce((sum, doc) => sum + (doc.chunk_count || 0), 0) || 0;
      const averageChunksPerDocument = totalDocuments > 0 ? totalChunks / totalDocuments : 0;

      // Get recent queries count (last 24 hours)
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);

      const { count: recentQueries } = await supabase
        .from('langchain_conversations')
        .select('id', { count: 'exact', head: true })
        .gte('updated_at', yesterday.toISOString());

      return {
        totalDocuments,
        totalChunks,
        averageChunksPerDocument: Math.round(averageChunksPerDocument * 100) / 100,
        recentQueries: recentQueries || 0,
      };
    } catch (error) {
      console.error('Failed to get RAG statistics:', error);
      return { totalDocuments: 0, totalChunks: 0, averageChunksPerDocument: 0, recentQueries: 0 };
    }
  }

  /**
   * Search documents by metadata
   */
  static async searchByMetadata(
    filters: Record<string, any>,
    limit: number = 20
  ): Promise<any[]> {
    try {
      const config = langChainConfig.getConfig();
      let query = supabase.from(config.vectorStore.tableName).select('*');

      // Apply metadata filters
      for (const [key, value] of Object.entries(filters)) {
        query = query.eq(`metadata->${key}`, value);
      }

      const { data, error } = await query.limit(limit);

      if (error) {
        console.error('Metadata search failed:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Metadata search failed:', error);
      return [];
    }
  }

  /**
   * Update document metadata
   */
  static async updateDocumentMetadata(
    documentId: string,
    metadata: Record<string, any>
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('langchain_document_metadata')
        .update({ metadata })
        .eq('id', documentId);

      if (error) {
        console.error('Failed to update document metadata:', error);
      }
    } catch (error) {
      console.error('Failed to update document metadata:', error);
    }
  }
}

export const langChainRAG = LangChainRAGSystem;
