/**
 * Time Tracking History Component
 * Displays historical time tracking data with filtering and search
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar, Clock, MapPin, Search, Filter, Download } from 'lucide-react';

interface TimeEntry {
  id: string;
  date: string;
  clockIn: string;
  clockOut: string;
  duration: string;
  breakTime: string;
  location: string;
  project?: string;
  task?: string;
  notes?: string;
  status: 'completed' | 'ongoing' | 'incomplete';
}

export const TimeTrackingHistory: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterPeriod, setFilterPeriod] = useState('week');
  const [filterStatus, setFilterStatus] = useState('all');

  // Mock data for time entries
  const timeEntries: TimeEntry[] = [
    {
      id: '1',
      date: '2025-08-02',
      clockIn: '09:00 AM',
      clockOut: '05:30 PM',
      duration: '8h 30m',
      breakTime: '30m',
      location: 'Lagos Office',
      project: 'Website Redesign',
      task: 'Frontend Development',
      notes: 'Completed user dashboard components',
      status: 'completed'
    },
    {
      id: '2',
      date: '2025-08-01',
      clockIn: '08:45 AM',
      clockOut: '05:15 PM',
      duration: '8h 30m',
      breakTime: '45m',
      location: 'Lagos Office',
      project: 'Mobile App',
      task: 'API Integration',
      notes: 'Integrated payment gateway',
      status: 'completed'
    },
    {
      id: '3',
      date: '2025-07-31',
      clockIn: '09:15 AM',
      clockOut: '04:45 PM',
      duration: '7h 30m',
      breakTime: '30m',
      location: 'Remote',
      project: 'Database Migration',
      task: 'Data Analysis',
      notes: 'Analyzed legacy data structure',
      status: 'completed'
    },
    {
      id: '4',
      date: '2025-07-30',
      clockIn: '09:00 AM',
      clockOut: '—',
      duration: '6h 15m',
      breakTime: '15m',
      location: 'Lagos Office',
      project: 'System Maintenance',
      task: 'Server Updates',
      notes: 'Incomplete due to system issues',
      status: 'incomplete'
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case 'ongoing':
        return <Badge className="bg-blue-100 text-blue-800">Ongoing</Badge>;
      case 'incomplete':
        return <Badge className="bg-red-100 text-red-800">Incomplete</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const filteredEntries = timeEntries.filter(entry => {
    const matchesSearch = entry.project?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         entry.task?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         entry.notes?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === 'all' || entry.status === filterStatus;
    
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="space-y-6">
      {/* Header with Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Clock className="h-5 w-5" />
            <span>Time Tracking History</span>
          </CardTitle>
          <CardDescription>
            View and analyze your time tracking records
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search projects, tasks, or notes..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            {/* Period Filter */}
            <Select value={filterPeriod} onValueChange={setFilterPeriod}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
                <SelectItem value="quarter">This Quarter</SelectItem>
                <SelectItem value="year">This Year</SelectItem>
              </SelectContent>
            </Select>
            
            {/* Status Filter */}
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="ongoing">Ongoing</SelectItem>
                <SelectItem value="incomplete">Incomplete</SelectItem>
              </SelectContent>
            </Select>
            
            {/* Export Button */}
            <Button variant="outline" className="flex items-center space-x-2">
              <Download className="h-4 w-4" />
              <span>Export</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">32h 45m</p>
              <p className="text-sm text-gray-600">Total Hours</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">8h 15m</p>
              <p className="text-sm text-gray-600">Average Daily</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-orange-600">2h 00m</p>
              <p className="text-sm text-gray-600">Break Time</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">95%</p>
              <p className="text-sm text-gray-600">Completion Rate</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Time Entries List */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Entries</CardTitle>
          <CardDescription>
            {filteredEntries.length} entries found
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredEntries.map((entry) => (
              <div key={entry.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span className="font-medium">{new Date(entry.date).toLocaleDateString()}</span>
                    {getStatusBadge(entry.status)}
                  </div>
                  <div className="text-lg font-semibold text-blue-600">
                    {entry.duration}
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Time:</span>
                    <span className="ml-2">{entry.clockIn} - {entry.clockOut}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Break:</span>
                    <span className="ml-2">{entry.breakTime}</span>
                  </div>
                  <div className="flex items-center">
                    <MapPin className="h-3 w-3 text-gray-500 mr-1" />
                    <span className="text-gray-600">{entry.location}</span>
                  </div>
                </div>
                
                {entry.project && (
                  <div className="text-sm">
                    <span className="text-gray-500">Project:</span>
                    <span className="ml-2 font-medium">{entry.project}</span>
                    {entry.task && (
                      <>
                        <span className="text-gray-500 ml-4">Task:</span>
                        <span className="ml-2">{entry.task}</span>
                      </>
                    )}
                  </div>
                )}
                
                {entry.notes && (
                  <div className="text-sm">
                    <span className="text-gray-500">Notes:</span>
                    <span className="ml-2 text-gray-700">{entry.notes}</span>
                  </div>
                )}
              </div>
            ))}
          </div>
          
          {filteredEntries.length === 0 && (
            <div className="text-center py-8">
              <Clock className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">No time entries found</p>
              <p className="text-sm text-gray-400">Try adjusting your search or filters</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
